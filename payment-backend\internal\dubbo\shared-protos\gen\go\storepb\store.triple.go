// Code generated by protoc-gen-triple. DO NOT EDIT.
//
// Source: protos/store.proto
package storepb

import (
	"context"
)

import (
	"dubbo.apache.org/dubbo-go/v3"
	"dubbo.apache.org/dubbo-go/v3/client"
	"dubbo.apache.org/dubbo-go/v3/common"
	"dubbo.apache.org/dubbo-go/v3/common/constant"
	"dubbo.apache.org/dubbo-go/v3/protocol/triple/triple_protocol"
	"dubbo.apache.org/dubbo-go/v3/server"
)

// This is a compile-time assertion to ensure that this generated file and the Triple package
// are compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of Triple newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of Triple or updating the Triple
// version compiled into your binary.
const _ = triple_protocol.IsAtLeastVersion0_1_0

const (
	// StoreServiceName is the fully-qualified name of the StoreService service.
	StoreServiceName = "com.aibook.storepb.grpc.StoreService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// StoreServiceListAllPackagesProcedure is the fully-qualified name of the StoreService's ListAllPackages RPC.
	StoreServiceListAllPackagesProcedure = "/com.aibook.storepb.grpc.StoreService/ListAllPackages"
	// StoreServiceAdminAddPackagesProcedure is the fully-qualified name of the StoreService's AdminAddPackages RPC.
	StoreServiceAdminAddPackagesProcedure = "/com.aibook.storepb.grpc.StoreService/AdminAddPackages"
	// StoreServiceAdminDeletePackagesProcedure is the fully-qualified name of the StoreService's AdminDeletePackages RPC.
	StoreServiceAdminDeletePackagesProcedure = "/com.aibook.storepb.grpc.StoreService/AdminDeletePackages"
	// StoreServiceAdminUpdatePackagesProcedure is the fully-qualified name of the StoreService's AdminUpdatePackages RPC.
	StoreServiceAdminUpdatePackagesProcedure = "/com.aibook.storepb.grpc.StoreService/AdminUpdatePackages"
	// StoreServiceAdminListAllPackagesProcedure is the fully-qualified name of the StoreService's AdminListAllPackages RPC.
	StoreServiceAdminListAllPackagesProcedure = "/com.aibook.storepb.grpc.StoreService/AdminListAllPackages"
)

var (
	_ StoreService = (*StoreServiceImpl)(nil)
)

// StoreService is a client for the com.aibook.storepb.grpc.StoreService service.
type StoreService interface {
	ListAllPackages(ctx context.Context, req *ListAllPackagesRequest, opts ...client.CallOption) (*ListAllPackagesResponse, error)
	AdminAddPackages(ctx context.Context, req *CreatePackageRequest, opts ...client.CallOption) (*CreatePackageResponse, error)
	AdminDeletePackages(ctx context.Context, req *DeletePackageRequest, opts ...client.CallOption) (*DeletePackageResponse, error)
	AdminUpdatePackages(ctx context.Context, req *UpdatePackageRequest, opts ...client.CallOption) (*UpdatePackageResponse, error)
	AdminListAllPackages(ctx context.Context, req *AdminListAllPackagesRequest, opts ...client.CallOption) (*AdminListAllPackagesResponse, error)
}

// NewStoreService constructs a client for the storepb.StoreService service.
func NewStoreService(cli *client.Client, opts ...client.ReferenceOption) (StoreService, error) {
	conn, err := cli.DialWithInfo("com.aibook.storepb.grpc.StoreService", &StoreService_ClientInfo, opts...)
	if err != nil {
		return nil, err
	}
	return &StoreServiceImpl{
		conn: conn,
	}, nil
}

func SetConsumerStoreService(srv common.RPCService) {
	dubbo.SetConsumerServiceWithInfo(srv, &StoreService_ClientInfo)
}

// StoreServiceImpl implements StoreService.
type StoreServiceImpl struct {
	conn *client.Connection
}

func (c *StoreServiceImpl) ListAllPackages(ctx context.Context, req *ListAllPackagesRequest, opts ...client.CallOption) (*ListAllPackagesResponse, error) {
	resp := new(ListAllPackagesResponse)
	if err := c.conn.CallUnary(ctx, []interface{}{req}, resp, "ListAllPackages", opts...); err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *StoreServiceImpl) AdminAddPackages(ctx context.Context, req *CreatePackageRequest, opts ...client.CallOption) (*CreatePackageResponse, error) {
	resp := new(CreatePackageResponse)
	if err := c.conn.CallUnary(ctx, []interface{}{req}, resp, "AdminAddPackages", opts...); err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *StoreServiceImpl) AdminDeletePackages(ctx context.Context, req *DeletePackageRequest, opts ...client.CallOption) (*DeletePackageResponse, error) {
	resp := new(DeletePackageResponse)
	if err := c.conn.CallUnary(ctx, []interface{}{req}, resp, "AdminDeletePackages", opts...); err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *StoreServiceImpl) AdminUpdatePackages(ctx context.Context, req *UpdatePackageRequest, opts ...client.CallOption) (*UpdatePackageResponse, error) {
	resp := new(UpdatePackageResponse)
	if err := c.conn.CallUnary(ctx, []interface{}{req}, resp, "AdminUpdatePackages", opts...); err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *StoreServiceImpl) AdminListAllPackages(ctx context.Context, req *AdminListAllPackagesRequest, opts ...client.CallOption) (*AdminListAllPackagesResponse, error) {
	resp := new(AdminListAllPackagesResponse)
	if err := c.conn.CallUnary(ctx, []interface{}{req}, resp, "AdminListAllPackages", opts...); err != nil {
		return nil, err
	}
	return resp, nil
}

var StoreService_ClientInfo = client.ClientInfo{
	InterfaceName: "com.aibook.storepb.grpc.StoreService",
	MethodNames:   []string{"ListAllPackages", "AdminAddPackages", "AdminDeletePackages", "AdminUpdatePackages", "AdminListAllPackages"},
	ConnectionInjectFunc: func(dubboCliRaw interface{}, conn *client.Connection) {
		dubboCli := dubboCliRaw.(*StoreServiceImpl)
		dubboCli.conn = conn
	},
}

// StoreServiceHandler is an implementation of the com.aibook.storepb.grpc.StoreService service.
type StoreServiceHandler interface {
	ListAllPackages(context.Context, *ListAllPackagesRequest) (*ListAllPackagesResponse, error)
	AdminAddPackages(context.Context, *CreatePackageRequest) (*CreatePackageResponse, error)
	AdminDeletePackages(context.Context, *DeletePackageRequest) (*DeletePackageResponse, error)
	AdminUpdatePackages(context.Context, *UpdatePackageRequest) (*UpdatePackageResponse, error)
	AdminListAllPackages(context.Context, *AdminListAllPackagesRequest) (*AdminListAllPackagesResponse, error)
}

func RegisterStoreServiceHandler(srv *server.Server, hdlr StoreServiceHandler, opts ...server.ServiceOption) error {
	return srv.Register(hdlr, &StoreService_ServiceInfo, opts...)
}

func SetProviderStoreService(srv common.RPCService) {
	dubbo.SetProviderServiceWithInfo(srv, &StoreService_ServiceInfo)
}

var StoreService_ServiceInfo = server.ServiceInfo{
	InterfaceName: "com.aibook.storepb.grpc.StoreService",
	ServiceType:   (*StoreServiceHandler)(nil),
	Methods: []server.MethodInfo{
		{
			Name: "ListAllPackages",
			Type: constant.CallUnary,
			ReqInitFunc: func() interface{} {
				return new(ListAllPackagesRequest)
			},
			MethodFunc: func(ctx context.Context, args []interface{}, handler interface{}) (interface{}, error) {
				req := args[0].(*ListAllPackagesRequest)
				res, err := handler.(StoreServiceHandler).ListAllPackages(ctx, req)
				if err != nil {
					return nil, err
				}
				return triple_protocol.NewResponse(res), nil
			},
		},
		{
			Name: "AdminAddPackages",
			Type: constant.CallUnary,
			ReqInitFunc: func() interface{} {
				return new(CreatePackageRequest)
			},
			MethodFunc: func(ctx context.Context, args []interface{}, handler interface{}) (interface{}, error) {
				req := args[0].(*CreatePackageRequest)
				res, err := handler.(StoreServiceHandler).AdminAddPackages(ctx, req)
				if err != nil {
					return nil, err
				}
				return triple_protocol.NewResponse(res), nil
			},
		},
		{
			Name: "AdminDeletePackages",
			Type: constant.CallUnary,
			ReqInitFunc: func() interface{} {
				return new(DeletePackageRequest)
			},
			MethodFunc: func(ctx context.Context, args []interface{}, handler interface{}) (interface{}, error) {
				req := args[0].(*DeletePackageRequest)
				res, err := handler.(StoreServiceHandler).AdminDeletePackages(ctx, req)
				if err != nil {
					return nil, err
				}
				return triple_protocol.NewResponse(res), nil
			},
		},
		{
			Name: "AdminUpdatePackages",
			Type: constant.CallUnary,
			ReqInitFunc: func() interface{} {
				return new(UpdatePackageRequest)
			},
			MethodFunc: func(ctx context.Context, args []interface{}, handler interface{}) (interface{}, error) {
				req := args[0].(*UpdatePackageRequest)
				res, err := handler.(StoreServiceHandler).AdminUpdatePackages(ctx, req)
				if err != nil {
					return nil, err
				}
				return triple_protocol.NewResponse(res), nil
			},
		},
		{
			Name: "AdminListAllPackages",
			Type: constant.CallUnary,
			ReqInitFunc: func() interface{} {
				return new(AdminListAllPackagesRequest)
			},
			MethodFunc: func(ctx context.Context, args []interface{}, handler interface{}) (interface{}, error) {
				req := args[0].(*AdminListAllPackagesRequest)
				res, err := handler.(StoreServiceHandler).AdminListAllPackages(ctx, req)
				if err != nil {
					return nil, err
				}
				return triple_protocol.NewResponse(res), nil
			},
		},
	},
}
