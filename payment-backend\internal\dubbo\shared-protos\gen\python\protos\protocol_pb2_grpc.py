# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from protos import protocol_pb2 as protos_dot_protocol__pb2

GRPC_GENERATED_VERSION = '1.73.1'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in protos/protocol_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class ProtocolServiceStub(object):
    """ProtocolService 用于按照国家/地区和类型获取协议信息。
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.getProtocol = channel.unary_unary(
                '/com.aibook.admin.grpc.ProtocolService/getProtocol',
                request_serializer=protos_dot_protocol__pb2.ProtocolRequest.SerializeToString,
                response_deserializer=protos_dot_protocol__pb2.ProtocolResponse.FromString,
                _registered_method=True)


class ProtocolServiceServicer(object):
    """ProtocolService 用于按照国家/地区和类型获取协议信息。
    """

    def getProtocol(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_ProtocolServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'getProtocol': grpc.unary_unary_rpc_method_handler(
                    servicer.getProtocol,
                    request_deserializer=protos_dot_protocol__pb2.ProtocolRequest.FromString,
                    response_serializer=protos_dot_protocol__pb2.ProtocolResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'com.aibook.admin.grpc.ProtocolService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('com.aibook.admin.grpc.ProtocolService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class ProtocolService(object):
    """ProtocolService 用于按照国家/地区和类型获取协议信息。
    """

    @staticmethod
    def getProtocol(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.aibook.admin.grpc.ProtocolService/getProtocol',
            protos_dot_protocol__pb2.ProtocolRequest.SerializeToString,
            protos_dot_protocol__pb2.ProtocolResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
