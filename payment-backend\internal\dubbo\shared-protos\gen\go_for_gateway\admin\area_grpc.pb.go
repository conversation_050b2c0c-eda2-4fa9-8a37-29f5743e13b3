//*
//后台管理平台基础服务定义

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.31.1
// source: protos/area.proto

package admin

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	AreaService_GetAreas_FullMethodName = "/com.aibook.admin.grpc.AreaService/getAreas"
)

// AreaServiceClient is the client API for AreaService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AreaServiceClient interface {
	GetAreas(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*AreaResponse, error)
}

type areaServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAreaServiceClient(cc grpc.ClientConnInterface) AreaServiceClient {
	return &areaServiceClient{cc}
}

func (c *areaServiceClient) GetAreas(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*AreaResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AreaResponse)
	err := c.cc.Invoke(ctx, AreaService_GetAreas_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AreaServiceServer is the server API for AreaService service.
// All implementations must embed UnimplementedAreaServiceServer
// for forward compatibility.
type AreaServiceServer interface {
	GetAreas(context.Context, *Empty) (*AreaResponse, error)
	mustEmbedUnimplementedAreaServiceServer()
}

// UnimplementedAreaServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedAreaServiceServer struct{}

func (UnimplementedAreaServiceServer) GetAreas(context.Context, *Empty) (*AreaResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAreas not implemented")
}
func (UnimplementedAreaServiceServer) mustEmbedUnimplementedAreaServiceServer() {}
func (UnimplementedAreaServiceServer) testEmbeddedByValue()                     {}

// UnsafeAreaServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AreaServiceServer will
// result in compilation errors.
type UnsafeAreaServiceServer interface {
	mustEmbedUnimplementedAreaServiceServer()
}

func RegisterAreaServiceServer(s grpc.ServiceRegistrar, srv AreaServiceServer) {
	// If the following call pancis, it indicates UnimplementedAreaServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&AreaService_ServiceDesc, srv)
}

func _AreaService_GetAreas_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AreaServiceServer).GetAreas(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AreaService_GetAreas_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AreaServiceServer).GetAreas(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

// AreaService_ServiceDesc is the grpc.ServiceDesc for AreaService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AreaService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "com.aibook.admin.grpc.AreaService",
	HandlerType: (*AreaServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "getAreas",
			Handler:    _AreaService_GetAreas_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "protos/area.proto",
}
