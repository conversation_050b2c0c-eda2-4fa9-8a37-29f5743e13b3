package main

import (
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"payment-backend/internal/config"
)

func main() {
	// 示例1：基本配置加载
	fmt.Println("=== 示例1：基本配置加载 ===")
	basicConfigExample()

	// 示例2：使用配置管理器和动态配置更新
	fmt.Println("\n=== 示例2：配置管理器和动态配置更新 ===")
	configManagerExample()

	// 示例3：环境变量配置示例
	fmt.Println("\n=== 示例3：环境变量配置示例 ===")
	environmentVariableExample()
}

// basicConfigExample 基本配置加载示例
func basicConfigExample() {
	// 加载配置（默认情况下Nacos是禁用的）
	cfg, err := config.LoadConfig("")
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	fmt.Printf("Server Address: %s\n", cfg.Server.GetAddress())
	fmt.Printf("Database Driver: %s\n", cfg.Database.Driver)
	fmt.Printf("Nacos Enabled: %t\n", cfg.Nacos.Enabled)
	fmt.Printf("Nacos Endpoints: %v\n", cfg.Nacos.Endpoints)
}

// configManagerExample 配置管理器示例
func configManagerExample() {
	// 设置环境变量启用Nacos（仅用于演示）
	os.Setenv("PAYMENT_NACOS_ENABLED", "true")
	os.Setenv("PAYMENT_NACOS_ENDPOINTS", "127.0.0.1:8848")
	os.Setenv("PAYMENT_NACOS_NAMESPACE", "payment-service")
	os.Setenv("PAYMENT_NACOS_CONFIG_ENABLED", "true")
	os.Setenv("PAYMENT_NACOS_CONFIG_DATA_ID", "payment-backend.yaml")
	defer func() {
		os.Unsetenv("PAYMENT_NACOS_ENABLED")
		os.Unsetenv("PAYMENT_NACOS_ENDPOINTS")
		os.Unsetenv("PAYMENT_NACOS_NAMESPACE")
		os.Unsetenv("PAYMENT_NACOS_CONFIG_ENABLED")
		os.Unsetenv("PAYMENT_NACOS_CONFIG_DATA_ID")
	}()

	// 创建配置管理器
	configManager, err := config.NewConfigManager("")
	if err != nil {
		// 如果Nacos不可用，这是正常的，配置管理器仍会创建
		fmt.Printf("Warning: Failed to create config manager with Nacos: %v\n", err)
		fmt.Println("This is normal if Nacos server is not running")
		return
	}

	// 获取当前配置
	currentConfig := configManager.GetConfig()
	fmt.Printf("Current Server Port: %d\n", currentConfig.Server.Port)

	// 添加配置变更回调
	configManager.AddCallback(func(newConfig *config.Config) {
		fmt.Printf("Configuration updated! New server port: %d\n", newConfig.Server.Port)
		fmt.Printf("New log level: %s\n", newConfig.Log.Level)
	})

	fmt.Println("Config manager created successfully")
	fmt.Println("If Nacos is running and contains configuration, changes will be detected automatically")
}

// environmentVariableExample 环境变量配置示例
func environmentVariableExample() {
	// 设置各种环境变量来演示配置优先级
	envVars := map[string]string{
		"PAYMENT_SERVER_PORT":          "9999",
		"PAYMENT_LOG_LEVEL":            "debug",
		"PAYMENT_NACOS_ENABLED":        "false", // 禁用Nacos以简化示例
		"PAYMENT_NACOS_ENDPOINTS":      "192.168.1.100:8848,192.168.1.101:8848",
		"PAYMENT_NACOS_NAMESPACE":      "custom-namespace",
		"PAYMENT_NACOS_USERNAME":       "custom-user",
		"PAYMENT_NACOS_PASSWORD":       "custom-password",
		"PAYMENT_NACOS_CONFIG_ENABLED": "false",
		"PAYMENT_NACOS_CONFIG_DATA_ID": "custom-config.yaml",
		"PAYMENT_NACOS_CONFIG_GROUP":   "CUSTOM_GROUP",
		"PAYMENT_NACOS_CONFIG_TIMEOUT": "60",
	}

	// 设置环境变量
	for key, value := range envVars {
		os.Setenv(key, value)
	}

	// 清理环境变量
	defer func() {
		for key := range envVars {
			os.Unsetenv(key)
		}
	}()

	// 加载配置
	cfg, err := config.LoadConfig("")
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 显示配置值（应该反映环境变量的设置）
	fmt.Printf("Server Port (from env): %d\n", cfg.Server.Port)
	fmt.Printf("Log Level (from env): %s\n", cfg.Log.Level)
	fmt.Printf("Nacos Enabled (from env): %t\n", cfg.Nacos.Enabled)
	fmt.Printf("Nacos Endpoints (from env): %v\n", cfg.Nacos.Endpoints)
	fmt.Printf("Nacos Namespace (from env): %s\n", cfg.Nacos.Namespace)
	fmt.Printf("Nacos Username (from env): %s\n", cfg.Nacos.Username)
	fmt.Printf("Nacos Config Enabled (from env): %t\n", cfg.Nacos.Config.Enabled)
	fmt.Printf("Nacos Config Data ID (from env): %s\n", cfg.Nacos.Config.DataID)
	fmt.Printf("Nacos Config Group (from env): %s\n", cfg.Nacos.Config.Group)
	fmt.Printf("Nacos Config Timeout (from env): %d\n", cfg.Nacos.Config.Timeout)

	fmt.Println("\n配置优先级演示:")
	fmt.Println("1. 默认值 (最低优先级)")
	fmt.Println("2. 配置文件 config.yaml")
	fmt.Println("3. 环境特定配置文件 config.{env}.yaml")
	fmt.Println("4. Nacos配置中心 (如果启用)")
	fmt.Println("5. 环境变量 (最高优先级)")
}

// realWorldExample 真实世界使用示例
func realWorldExample() {
	fmt.Println("\n=== 真实世界使用示例 ===")

	// 创建配置管理器
	configManager, err := config.NewConfigManager("")
	if err != nil {
		log.Fatalf("Failed to create config manager: %v", err)
	}

	// 获取初始配置
	cfg := configManager.GetConfig()
	fmt.Printf("Initial configuration loaded\n")
	fmt.Printf("Server will start on: %s\n", cfg.Server.GetAddress())
	fmt.Printf("Database: %s://%s:%d/%s\n",
		cfg.Database.Driver, cfg.Database.Host, cfg.Database.Port, cfg.Database.Database)

	// 添加配置变更监听器
	configManager.AddCallback(func(newConfig *config.Config) {
		fmt.Printf("Configuration changed!\n")
		fmt.Printf("New server address: %s\n", newConfig.Server.GetAddress())
		fmt.Printf("New log level: %s\n", newConfig.Log.Level)

		// 在实际应用中，这里可以：
		// 1. 重新配置日志级别
		// 2. 更新数据库连接池
		// 3. 重新加载支付提供商配置
		// 4. 通知其他组件配置已更改
	})

	// 模拟应用运行
	fmt.Println("Application is running... Press Ctrl+C to exit")

	// 等待信号
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 模拟一些工作
	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			fmt.Println("Application is still running...")
		case sig := <-sigChan:
			fmt.Printf("Received signal: %v, shutting down...\n", sig)
			return
		}
	}
}
