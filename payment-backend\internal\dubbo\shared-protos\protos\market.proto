syntax = "proto3";

package com.aibook.market.grpc;

option go_package = "./marketpb;marketpb";

import "google/api/annotations.proto";

service MarketService {
  // 共享绘本
  rpc ShareBook(ShareBookRequest) returns (Empty) {
    option (google.api.http) = {
      post : "/api/v1/market/share"
      body : "*"
    };
  }

  // 绘本详情
  rpc BookDetail(BookDetailRequest) returns (BookDetailResponse) {
    option (google.api.http) = {
      get : "/api/v1/market/book/{book_id}"
    };
  }

  // 绘本属性修改(如审核，下架, 删除)
  rpc BookModify(BookModifyRequest) returns (Empty) {
    option (google.api.http) = {
      post : "/api/v1/market/book/{book_id}"
      body : "*"
    };
  }

  // 搜索绘本（按主题、按标题）， 按热度排序返回
  rpc SearchBook(SearchBookRequest) returns (SearchBookResponse) {
    option (google.api.http) = {
      get : "/api/v1/market/book"
    };
  }

  // 绘本主题 topK
  rpc ThemeTopk(Empty) returns (ThemeTopkResponse) {
    option (google.api.http) = {
      get : "/api/v1/market/theme/topk"
    };
  }

  // 指定主题下的绘本 topK
  rpc ThemeBookTopk(ThemeBookTopkRequest) returns (ThemeBookTopkResponse) {
    option (google.api.http) = {
      get : "/api/v1/market/theme/book/topk"
    };
  }
}

message SearchBookRequest {
  string search_str = 1;
  uint32 page = 2;
  uint32 page_size = 3;
}

message SearchBookResponse {
  repeated BookInfo books = 1;
  uint32 page = 2;
  uint32 page_size = 3;
  uint32 total = 4;
}

message BookModifyRequest {
  uint64 book_id = 1; // 绘本ID
  string title = 2;  // 绘本标题
  string description = 3; // 绘本描述
  string cover = 4; // 绘本封面
  repeated int32 themeids = 5; // 绘本主题
}

message BookDetailRequest { uint64 book_id = 1; }
message BookDetailResponse { BookInfo book_info = 1; }

message BookInfo {
  uint64 book_id = 1; // 绘本ID
  string title = 2;  // 绘本标题
  string description = 3; // 绘本描述
  string cover = 4; // 绘本封面
  uint32 recommend_score = 5; // 绘本推荐分数
  uint32 download_count = 6; // 绘本下载次数
  uint32 create_at = 7; // 绘本进入绘本市场时间
  uint32 update_at = 8; // 最后更新时间
}

message ThemeBookTopkRequest { uint64 theme_id = 1; }

message ThemeBookTopkResponse {
  repeated BookInfo books = 1; // 绘本数组
}



message ThemeTopkResponse {
  repeated Theme themes = 1; // 绘本主题数组
}

message Theme {
  uint64 theme_id = 1;        // 绘本主题ID
  string name = 2;            // 绘本主题名称
  string description = 3;     // 绘本主题描述
  string cover = 4;           // 绘本主题封面
  uint32 recommend_score = 5; // 绘本主题推荐分数
  uint32 book_count = 6;      // 绘本数量
}

message Empty {}

message ShareBookRequest {
  uint64 book_id = 1; // 绘本ID
}
