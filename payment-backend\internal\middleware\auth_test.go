package middleware

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"

	"payment-backend/internal/config"
	"payment-backend/internal/logger"
)

func TestAuthMiddleware(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// 创建测试日志记录器
	logConfig := &config.LogConfig{
		Level:  "info",
		Format: "console",
		Output: "stdout",
	}
	testLogger, _ := logger.NewLogger(logConfig)

	tests := []struct {
		name           string
		userID         string
		role           string
		expectedStatus int
		expectedError  string
	}{
		{
			name:           "成功认证",
			userID:         "user123",
			role:           "customer",
			expectedStatus: http.StatusOK,
		},
		{
			name:           "缺少用户ID",
			userID:         "",
			role:           "customer",
			expectedStatus: http.StatusUnauthorized,
			expectedError:  "unauthorized",
		},
		{
			name:           "缺少角色",
			userID:         "user123",
			role:           "",
			expectedStatus: http.StatusUnauthorized,
			expectedError:  "unauthorized",
		},
		{
			name:           "缺少所有头部",
			userID:         "",
			role:           "",
			expectedStatus: http.StatusUnauthorized,
			expectedError:  "unauthorized",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 设置路由
			router := gin.New()
			router.Use(AuthMiddleware(testLogger))
			router.GET("/test", func(c *gin.Context) {
				userCtx, err := MustGetUserContext(c)
				assert.NotNil(t, err)
				c.JSON(http.StatusOK, gin.H{
					"user_id": userCtx.UserID,
					"role":    userCtx.Role,
				})
			})

			// 创建请求
			req, _ := http.NewRequest("GET", "/test", nil)
			if tt.userID != "" {
				req.Header.Set("x-user-id", tt.userID)
			}
			if tt.role != "" {
				req.Header.Set("x-role", tt.role)
			}

			// 执行请求
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			// 验证响应
			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedError != "" {
				assert.Contains(t, w.Body.String(), tt.expectedError)
			}
		})
	}
}

func TestOptionalAuthMiddleware(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// 创建测试日志记录器
	logConfig := &config.LogConfig{
		Level:  "info",
		Format: "console",
		Output: "stdout",
	}
	testLogger, _ := logger.NewLogger(logConfig)

	tests := []struct {
		name           string
		userID         string
		role           string
		expectedStatus int
		hasUserContext bool
	}{
		{
			name:           "有用户信息",
			userID:         "user123",
			role:           "customer",
			expectedStatus: http.StatusOK,
			hasUserContext: true,
		},
		{
			name:           "无用户信息",
			userID:         "",
			role:           "",
			expectedStatus: http.StatusOK,
			hasUserContext: false,
		},
		{
			name:           "部分用户信息",
			userID:         "user123",
			role:           "",
			expectedStatus: http.StatusOK,
			hasUserContext: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 设置路由
			router := gin.New()
			router.Use(OptionalAuthMiddleware(testLogger))
			router.GET("/test", func(c *gin.Context) {
				userCtx, exists := GetUserContext(c)
				if exists {
					c.JSON(http.StatusOK, gin.H{
						"has_user_context": true,
						"user_id":          userCtx.UserID,
						"role":             userCtx.Role,
					})
				} else {
					c.JSON(http.StatusOK, gin.H{
						"has_user_context": false,
					})
				}
			})

			// 创建请求
			req, _ := http.NewRequest("GET", "/test", nil)
			if tt.userID != "" {
				req.Header.Set("x-user-id", tt.userID)
			}
			if tt.role != "" {
				req.Header.Set("x-role", tt.role)
			}

			// 执行请求
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			// 验证响应
			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.hasUserContext {
				assert.Contains(t, w.Body.String(), `"has_user_context":true`)
				assert.Contains(t, w.Body.String(), tt.userID)
				assert.Contains(t, w.Body.String(), tt.role)
			} else {
				assert.Contains(t, w.Body.String(), `"has_user_context":false`)
			}
		})
	}
}

func TestRequireRole(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// 创建测试日志记录器
	logConfig := &config.LogConfig{
		Level:  "info",
		Format: "console",
		Output: "stdout",
	}
	testLogger, _ := logger.NewLogger(logConfig)

	tests := []struct {
		name           string
		userRole       string
		allowedRoles   []string
		expectedStatus int
		expectedError  string
	}{
		{
			name:           "管理员访问管理员资源",
			userRole:       "admin",
			allowedRoles:   []string{"admin"},
			expectedStatus: http.StatusOK,
		},
		{
			name:           "超级管理员访问管理员资源",
			userRole:       "super_admin",
			allowedRoles:   []string{"admin", "super_admin"},
			expectedStatus: http.StatusOK,
		},
		{
			name:           "客户访问管理员资源",
			userRole:       "customer",
			allowedRoles:   []string{"admin"},
			expectedStatus: http.StatusForbidden,
			expectedError:  "forbidden",
		},
		{
			name:           "未知角色访问",
			userRole:       "unknown",
			allowedRoles:   []string{"admin", "customer"},
			expectedStatus: http.StatusForbidden,
			expectedError:  "forbidden",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 设置路由
			router := gin.New()
			router.Use(AuthMiddleware(testLogger))
			router.Use(RequireRole(tt.allowedRoles...))
			router.GET("/admin", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{"message": "admin access granted"})
			})

			// 创建请求
			req, _ := http.NewRequest("GET", "/admin", nil)
			req.Header.Set("x-user-id", "user123")
			req.Header.Set("x-role", tt.userRole)

			// 执行请求
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			// 验证响应
			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedError != "" {
				assert.Contains(t, w.Body.String(), tt.expectedError)
			}
		})
	}
}

func TestGetUserContext(t *testing.T) {
	gin.SetMode(gin.TestMode)

	t.Run("用户上下文存在", func(t *testing.T) {
		c, _ := gin.CreateTestContext(httptest.NewRecorder())

		expectedUserCtx := &UserContext{
			UserID: "user123",
			Role:   "customer",
		}
		c.Set(string(UserContextKey), expectedUserCtx)

		userCtx, exists := GetUserContext(c)
		assert.True(t, exists)
		assert.Equal(t, expectedUserCtx.UserID, userCtx.UserID)
		assert.Equal(t, expectedUserCtx.Role, userCtx.Role)
	})

	t.Run("用户上下文不存在", func(t *testing.T) {
		c, _ := gin.CreateTestContext(httptest.NewRecorder())

		userCtx, exists := GetUserContext(c)
		assert.False(t, exists)
		assert.Nil(t, userCtx)
	})
}

func TestMustGetUserContext(t *testing.T) {
	gin.SetMode(gin.TestMode)

	t.Run("用户上下文存在", func(t *testing.T) {
		c, _ := gin.CreateTestContext(httptest.NewRecorder())

		expectedUserCtx := &UserContext{
			UserID: "user123",
			Role:   "customer",
		}
		c.Set(string(UserContextKey), expectedUserCtx)

		userCtx, err := MustGetUserContext(c)
		assert.NotNil(t, err)
		assert.Equal(t, expectedUserCtx.UserID, userCtx.UserID)
		assert.Equal(t, expectedUserCtx.Role, userCtx.Role)
	})

	t.Run("用户上下文不存在应该panic", func(t *testing.T) {
		c, _ := gin.CreateTestContext(httptest.NewRecorder())

		assert.Panics(t, func() {
			MustGetUserContext(c)
		})
	})
}
