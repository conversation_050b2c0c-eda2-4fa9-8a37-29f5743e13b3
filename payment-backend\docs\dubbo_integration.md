# Dubbo 集成文档

## 概述

本项目已成功集成 Dubbo RPC 框架，为 `ListAllOrders` 接口提供了 HTTP 和 Dubbo 两种调用方式。现有的 HTTP 接口保持不变，新增的 Dubbo 接口可供其他微服务调用。

## 架构设计

```
┌─────────────────┐    ┌─────────────────┐
│   HTTP Client   │    │  Dubbo Client   │
└─────────┬───────┘    └─────────┬───────┘
          │                      │
          ▼                      ▼
┌─────────────────┐    ┌─────────────────┐
│  HTTP Handler   │    │  Dubbo Handler  │
│ (ListAllOrders) │    │ (ListAllOrders) │
└─────────┬───────┘    └─────────┬───────┘
          │                      │
          └──────────┬───────────┘
                     ▼
          ┌─────────────────┐
          │  Order Service  │
          │ (业务逻辑层)     │
          └─────────────────┘
```

## 目录结构

```
payment-backend/
├── internal/
│   ├── dubbo/                    # Dubbo相关代码
│   │   ├── handler/              # Dubbo处理器
│   │   │   ├── order_dubbo_handler.go
│   │   │   └── order_dubbo_handler_test.go
│   │   ├── shared-protos/        # Protocol Buffers定义
│   │   │   ├── protos/
│   │   │   │   └── order.proto
│   │   │   └── gen/go/paymentpb/ # 生成的Go代码
│   │   │       ├── order.pb.go
│   │   │       ├── order.triple.go
│   │   │       └── order_grpc.pb.go
│   │   └── server/               # Dubbo服务器
│   │       └── dubbo_server.go
│   └── ...
└── configs/
    ├── config.yaml              # 生产环境配置
    └── config.dev.yaml          # 开发环境配置
```

## 配置说明

### 开发环境配置 (config.dev.yaml)

```yaml
# Dubbo配置（开发环境）
dubbo:
  enabled: true # 开发环境启用Dubbo服务
  port: 20000
  ip: "0.0.0.0"
  registry:
    address: "nacos://127.0.0.1:8848"
    namespace: "payment-service-dev"
    username: "nacos"
    password: "nacos"
```

### 生产环境配置 (config.yaml)

```yaml
# Dubbo配置
dubbo:
  enabled: false # 生产环境默认关闭，需要时手动开启
  port: 20000
  ip: "0.0.0.0"
  registry:
    address: "nacos://127.0.0.1:8848"
    namespace: "payment-service"
    username: "nacos"
    password: "nacos"
```

## 接口定义

### Protocol Buffers 定义

订单服务的 proto 定义位于 `internal/dubbo/shared-protos/protos/order.proto`：

```protobuf
service OrderService {
  // 获取所有订单列表（管理员接口）
  rpc ListAllOrders(ListAllOrdersRequest) returns (ListAllOrdersResponse);
}
```

### 请求和响应结构

- **ListAllOrdersRequest**: 包含过滤条件和分页参数
- **ListAllOrdersResponse**: 包含订单列表和分页信息
- **OrderFilter**: 支持按用户ID、货币、支付状态等条件过滤
- **PaginationRequest**: 分页请求参数（limit, offset）
- **PaginationResponse**: 分页响应信息（total, limit, offset, remaining）

## 使用方法

### 启动服务

1. **启动 Nacos 注册中心**（如果使用 Nacos）
2. **配置环境变量**（如果需要）
3. **启动服务**：
   ```bash
   go run . serve
   ```

服务启动后会同时提供：
- HTTP 接口：`GET /admin/orders`
- Dubbo 接口：`OrderService.ListAllOrders`

### 客户端调用示例

#### HTTP 调用（现有方式）

```bash
curl -X GET "http://localhost:8080/admin/orders?limit=10&offset=0" \
  -H "x-user-id: admin" \
  -H "x-role: admin"
```

#### Dubbo 客户端调用

```go
package main

import (
    "context"
    "log"
    
    "dubbo.apache.org/dubbo-go/v3/client"
    "dubbo.apache.org/dubbo-go/v3/registry"
    "payment-backend/internal/dubbo/shared-protos/gen/go/paymentpb"
)

func main() {
    // 创建客户端
    cli, err := client.NewClient(
        client.WithClientRegistry(
            registry.WithNacos(),
            registry.WithAddress("nacos://127.0.0.1:8848"),
            registry.WithNamespace("payment-service-dev"),
            registry.WithUsername("nacos"),
            registry.WithPassword("nacos"),
        ),
    )
    if err != nil {
        log.Fatal(err)
    }

    // 获取服务连接
    conn, err := cli.Dial("OrderService")
    if err != nil {
        log.Fatal(err)
    }

    // 创建客户端
    orderClient := paymentpb.NewOrderServiceClient(conn)

    // 调用接口
    resp, err := orderClient.ListAllOrders(context.Background(), &paymentpb.ListAllOrdersRequest{
        Filter: &paymentpb.OrderFilter{
            UserId: stringPtr("user123"),
        },
        Pagination: &paymentpb.PaginationRequest{
            Limit:  10,
            Offset: 0,
        },
    })
    if err != nil {
        log.Fatal(err)
    }

    log.Printf("Found %d orders", len(resp.Orders))
}

func stringPtr(s string) *string {
    return &s
}
```

## 测试

### 运行单元测试

```bash
# 运行 Dubbo 处理器测试
go test ./internal/dubbo/handler/...

# 运行所有测试
go test ./...
```

### 集成测试

确保 Nacos 注册中心运行正常，然后启动服务进行集成测试。

## 注意事项

1. **注册中心依赖**：Dubbo 服务需要 Nacos 注册中心，确保 Nacos 服务可用
2. **端口配置**：默认 Dubbo 端口为 20000，确保端口未被占用
3. **网络配置**：确保客户端和服务端网络连通
4. **版本兼容**：使用 dubbo-go v3.x 版本，确保客户端和服务端版本兼容

## 故障排除

### 常见问题

1. **服务注册失败**：检查 Nacos 连接配置和网络连通性
2. **端口冲突**：修改 `dubbo.port` 配置
3. **编译错误**：确保 protobuf 文件正确生成

### 日志查看

服务启动时会输出 Dubbo 相关日志，可以通过日志排查问题：

```
INFO  Starting Dubbo server  port=20000 ip=0.0.0.0
INFO  Dubbo server is disabled  # 如果配置中 enabled=false
```

## 扩展

如需为其他接口添加 Dubbo 支持，可以参考 `ListAllOrders` 的实现方式：

1. 在 `order.proto` 中添加新的 RPC 方法
2. 重新生成 protobuf 代码
3. 在 `OrderDubboHandler` 中实现新方法
4. 添加相应的测试用例
