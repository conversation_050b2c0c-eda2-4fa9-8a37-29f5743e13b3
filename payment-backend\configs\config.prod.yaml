# 生产环境配置

# 服务器配置
server:
  port: 8080

# 数据库配置（生产环境使用MySQL）
database:
  driver: "mysql"
  host: "mysql-server"
  port: 3306
  username: "payment_user"
  password: "payment_password"
  database: "payment_db"
  ssl_mode: "disable"

# 支付配置
payment:
  providers:
    paypal:
      enabled: true
      api_key: "your_paypal_api_key_prod"
      secret_key: "your_paypal_secret_key"
      success_url: "https://your-domain.com/success"
      cancel_url: "https://your-domain.com/cancel"
      webhook:
        url: "https://your-domain.com/api/v1/pay-service/webhooks/paypal"
        secret: "your_paypal_webhook_secret"
      settings:
        environment: "sandbox" # sandbox, live

    stripe:
      enabled: true
      api_key: "your_stripe_api_key"
      secret_key: "your_stripe_secret_key"
      success_url: "https://your-domain.com/success"
      cancel_url: "https://your-domain.com/cancel"
      webhook:
        url: "https://your-domain.com/api/v1/pay-service/webhooks/stripe"
        secret: "your_stripe_webhook_secret"
      settings:
        environment: "test" # test, live

# 日志配置
log:
  level: "info"
  format: "json"
  output: "stdout"

# 雪花算法配置
snowflake:
  node_id: -1
