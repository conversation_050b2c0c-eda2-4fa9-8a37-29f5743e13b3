# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: protos/protocol.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'protos/protocol.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x15protos/protocol.proto\x12\x15\x63om.aibook.admin.grpc\x1a\x1cgoogle/api/annotations.proto\"0\n\x0fProtocolRequest\x12\x0f\n\x07\x63ountry\x18\x01 \x01(\t\x12\x0c\n\x04type\x18\x02 \x01(\x05\"4\n\x10ProtocolResponse\x12\x0f\n\x07\x63ontent\x18\x01 \x01(\t\x12\x0f\n\x07version\x18\x02 \x01(\x05\x32\x91\x01\n\x0fProtocolService\x12~\n\x0bgetProtocol\x12&.com.aibook.admin.grpc.ProtocolRequest\x1a\'.com.aibook.admin.grpc.ProtocolResponse\"\x1e\x82\xd3\xe4\x93\x02\x18\x12\x16/api/v1/admin/protocolB\x11P\x01Z\r./admin;adminb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'protos.protocol_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'P\001Z\r./admin;admin'
  _globals['_PROTOCOLSERVICE'].methods_by_name['getProtocol']._loaded_options = None
  _globals['_PROTOCOLSERVICE'].methods_by_name['getProtocol']._serialized_options = b'\202\323\344\223\002\030\022\026/api/v1/admin/protocol'
  _globals['_PROTOCOLREQUEST']._serialized_start=78
  _globals['_PROTOCOLREQUEST']._serialized_end=126
  _globals['_PROTOCOLRESPONSE']._serialized_start=128
  _globals['_PROTOCOLRESPONSE']._serialized_end=180
  _globals['_PROTOCOLSERVICE']._serialized_start=183
  _globals['_PROTOCOLSERVICE']._serialized_end=328
# @@protoc_insertion_point(module_scope)
