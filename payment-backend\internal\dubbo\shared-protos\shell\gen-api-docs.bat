@echo off
setlocal


set PROTO_DIR=.


set OUT_DIR=./docs


if not exist %OUT_DIR% (
    mkdir %OUT_DIR%
)



protoc  -I=%PROTO_DIR% ^
        -I=%PROTO_DIR%/third_party/googleapis ^
        -I=%PROTO_DIR%/third_party/validate ^
        --openapiv2_out=%OUT_DIR% ^
        --openapiv2_opt logtostderr=true ^
        --openapiv2_opt allow_merge=true ^
        --openapiv2_opt merge_file_name=user ^
        %PROTO_DIR%\protos/user.proto

protoc  -I=%PROTO_DIR% ^
        -I=%PROTO_DIR%/third_party/googleapis ^
        -I=%PROTO_DIR%/third_party/validate ^
        --openapiv2_out=%OUT_DIR% ^
        --openapiv2_opt logtostderr=true ^
        --openapiv2_opt allow_merge=true ^
        --openapiv2_opt merge_file_name=market ^
        %PROTO_DIR%\protos/market.proto

protoc  -I=%PROTO_DIR% ^
        -I=%PROTO_DIR%/third_party/googleapis ^
        -I=%PROTO_DIR%/third_party/validate ^
        --openapiv2_out=%OUT_DIR% ^
        --openapiv2_opt logtostderr=true ^
        --openapiv2_opt allow_merge=true ^
        --openapiv2_opt merge_file_name=ota ^
        %PROTO_DIR%\protos/ota.proto

protoc  -I=%PROTO_DIR% ^
        -I=%PROTO_DIR%/third_party/googleapis ^
        -I=%PROTO_DIR%/third_party/validate ^
        --openapiv2_out=%OUT_DIR% ^
        --openapiv2_opt logtostderr=true ^
        --openapiv2_opt allow_merge=true ^
        --openapiv2_opt merge_file_name=area ^
        %PROTO_DIR%\protos/area.proto

protoc  -I=%PROTO_DIR% ^
        -I=%PROTO_DIR%/third_party/googleapis ^
        -I=%PROTO_DIR%/third_party/validate ^
        --openapiv2_out=%OUT_DIR% ^
        --openapiv2_opt logtostderr=true ^
        --openapiv2_opt allow_merge=true ^
        --openapiv2_opt merge_file_name=protocol ^
        %PROTO_DIR%\protos/protocol.proto

if errorlevel 1 (
    echo %time% [ERROR] protoc xxxx  failed
    exit /b 1
) else (
    echo %time% [OK] xxxx %OUT_DIR%
)

endlocal
