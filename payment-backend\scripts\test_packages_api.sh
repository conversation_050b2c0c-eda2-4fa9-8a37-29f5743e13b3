#!/bin/bash

# 流量包管理API测试脚本
# 使用方法: ./test_packages_api.sh [server_host]
# 示例: ./test_packages_api.sh http://localhost:8080

set -e

# 设置服务器地址
SERVER_HOST=${1:-"http://localhost:8080"}
echo "测试服务器: $SERVER_HOST"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印分隔线
print_separator() {
    echo -e "${BLUE}================================================${NC}"
}

# 打印步骤
print_step() {
    echo -e "${YELLOW}=== $1 ===${NC}"
}

# 打印成功
print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

# 打印错误
print_error() {
    echo -e "${RED}✗ $1${NC}"
}

# 检查jq是否安装
if ! command -v jq &> /dev/null; then
    print_error "jq 未安装，请先安装 jq 用于JSON格式化"
    echo "Ubuntu/Debian: sudo apt-get install jq"
    echo "CentOS/RHEL: sudo yum install jq"
    echo "macOS: brew install jq"
    exit 1
fi

print_separator
echo -e "${BLUE}流量包管理API测试脚本${NC}"
print_separator

# 1. 创建测试流量包
print_step "1. 创建基础流量包"
RESPONSE1=$(curl -s -X PUT "${SERVER_HOST}/api/v1/pay-service/admin/store-service/packages" \
  -H "Content-Type: application/json" \
  -H "x-trace-id: test_basic_001" \
  -d '{
    "package_name": "基础流量包",
    "package_desc": "适合个人用户的基础流量包，包含100GB流量",
    "entitlement": "100GB_TRAFFIC",
    "original_price": 9.99,
    "currency": "USD",
    "country": "US",
    "extra1": "basic_plan",
    "extra2": "monthly"
  }')

echo "$RESPONSE1" | jq '.'
if echo "$RESPONSE1" | jq -e '.message' > /dev/null 2>&1; then
    print_success "基础流量包创建成功"
else
    print_error "基础流量包创建失败"
fi

print_separator

# 2. 创建有优惠价的流量包
print_step "2. 创建高级流量包（有优惠价）"
RESPONSE2=$(curl -s -X PUT "${SERVER_HOST}/api/v1/pay-service/admin/store-service/packages" \
  -H "Content-Type: application/json" \
  -H "x-trace-id: test_premium_001" \
  -d '{
    "package_name": "高级流量包",
    "package_desc": "适合企业用户的高级流量包，包含1TB流量",
    "entitlement": "1TB_TRAFFIC",
    "original_price": 99.99,
    "discount_price": 79.99,
    "currency": "USD",
    "country": "US",
    "extra1": "premium_plan",
    "extra2": "monthly",
    "extra3": 1024,
    "extra4": 30
  }')

echo "$RESPONSE2" | jq '.'
if echo "$RESPONSE2" | jq -e '.message' > /dev/null 2>&1; then
    print_success "高级流量包创建成功"
else
    print_error "高级流量包创建失败"
fi

print_separator

# 3. 创建有折扣的流量包
print_step "3. 创建专业流量包（有折扣）"
RESPONSE3=$(curl -s -X PUT "${SERVER_HOST}/api/v1/pay-service/admin/store-service/packages" \
  -H "Content-Type: application/json" \
  -H "x-trace-id: test_pro_001" \
  -d '{
    "package_name": "专业流量包",
    "package_desc": "适合专业用户的流量包，包含500GB流量",
    "entitlement": "500GB_TRAFFIC",
    "original_price": 49.99,
    "discount_percent": 20,
    "currency": "USD",
    "country": "US",
    "extra1": "professional_plan",
    "extra2": "monthly"
  }')

echo "$RESPONSE3" | jq '.'
if echo "$RESPONSE3" | jq -e '.message' > /dev/null 2>&1; then
    print_success "专业流量包创建成功"
else
    print_error "专业流量包创建失败"
fi

print_separator

# 4. 创建中国区流量包
print_step "4. 创建中国区流量包"
RESPONSE4=$(curl -s -X PUT "${SERVER_HOST}/api/v1/pay-service/admin/store-service/packages" \
  -H "Content-Type: application/json" \
  -H "x-trace-id: test_cn_001" \
  -d '{
    "package_name": "中国区基础流量包",
    "package_desc": "适合中国用户的基础流量包，包含100GB流量",
    "entitlement": "100GB_TRAFFIC_CN",
    "original_price": 68.00,
    "currency": "CNY",
    "country": "CN",
    "extra1": "basic_plan_cn",
    "extra2": "monthly"
  }')

echo "$RESPONSE4" | jq '.'
if echo "$RESPONSE4" | jq -e '.message' > /dev/null 2>&1; then
    print_success "中国区流量包创建成功"
else
    print_error "中国区流量包创建失败"
fi

print_separator

# 5. 终端用户查询流量包列表
print_step "5. 终端用户查询流量包列表"
USER_RESPONSE=$(curl -s -X GET "${SERVER_HOST}/api/v1/pay-service/store-service/packages" \
  -H "Content-Type: application/json" \
  -H "x-trace-id: test_user_list_001" \
  -H "x-user-id: test_user" \
  -H "x-role: customer")

echo "$USER_RESPONSE" | jq '.'
if echo "$USER_RESPONSE" | jq -e '.packages' > /dev/null 2>&1; then
    PACKAGE_COUNT=$(echo "$USER_RESPONSE" | jq '.packages | length')
    print_success "终端用户查询成功，找到 $PACKAGE_COUNT 个流量包"
else
    print_error "终端用户查询失败"
fi

print_separator

# 6. 管理员查询流量包列表
print_step "6. 管理员查询流量包列表"
ADMIN_RESPONSE=$(curl -s -X GET "${SERVER_HOST}/api/v1/pay-service/admin/store-service/packages" \
  -H "Content-Type: application/json" \
  -H "x-trace-id: test_admin_list_001")

echo "$ADMIN_RESPONSE" | jq '.'
if echo "$ADMIN_RESPONSE" | jq -e '.packages' > /dev/null 2>&1; then
    ADMIN_PACKAGE_COUNT=$(echo "$ADMIN_RESPONSE" | jq '.packages | length')
    print_success "管理员查询成功，找到 $ADMIN_PACKAGE_COUNT 个流量包"
    
    # 获取第一个流量包的ID用于后续测试
    FIRST_PACKAGE_ID=$(echo "$ADMIN_RESPONSE" | jq -r '.packages[0].package_id // empty')
    if [ -n "$FIRST_PACKAGE_ID" ]; then
        print_success "获取到流量包ID: $FIRST_PACKAGE_ID"
    fi
else
    print_error "管理员查询失败"
fi

print_separator

# 7. 按货币查询（USD）
print_step "7. 按货币查询（USD）"
USD_RESPONSE=$(curl -s -X GET "${SERVER_HOST}/api/v1/pay-service/store-service/packages?currency=USD" \
  -H "Content-Type: application/json" \
  -H "x-trace-id: test_currency_001" \
  -H "x-user-id: test_user" \
  -H "x-role: customer")

echo "$USD_RESPONSE" | jq '.'
if echo "$USD_RESPONSE" | jq -e '.packages' > /dev/null 2>&1; then
    USD_COUNT=$(echo "$USD_RESPONSE" | jq '.packages | length')
    print_success "USD货币查询成功，找到 $USD_COUNT 个流量包"
else
    print_error "USD货币查询失败"
fi

print_separator

# 8. 按国家查询（CN）
print_step "8. 按国家查询（CN）"
CN_RESPONSE=$(curl -s -X GET "${SERVER_HOST}/api/v1/pay-service/store-service/packages?country=CN" \
  -H "Content-Type: application/json" \
  -H "x-trace-id: test_country_001" \
  -H "x-user-id: test_user" \
  -H "x-role: customer")

echo "$CN_RESPONSE" | jq '.'
if echo "$CN_RESPONSE" | jq -e '.packages' > /dev/null 2>&1; then
    CN_COUNT=$(echo "$CN_RESPONSE" | jq '.packages | length')
    print_success "中国区查询成功，找到 $CN_COUNT 个流量包"
else
    print_error "中国区查询失败"
fi

print_separator

# 9. 分页查询
print_step "9. 分页查询（limit=2）"
PAGE_RESPONSE=$(curl -s -X GET "${SERVER_HOST}/api/v1/pay-service/store-service/packages?limit=2&offset=0" \
  -H "Content-Type: application/json" \
  -H "x-trace-id: test_page_001" \
  -H "x-user-id: test_user" \
  -H "x-role: customer")

echo "$PAGE_RESPONSE" | jq '.'
if echo "$PAGE_RESPONSE" | jq -e '.packages' > /dev/null 2>&1; then
    PAGE_COUNT=$(echo "$PAGE_RESPONSE" | jq '.packages | length')
    TOTAL_COUNT=$(echo "$PAGE_RESPONSE" | jq '.pagination.total')
    print_success "分页查询成功，当前页 $PAGE_COUNT 个，总计 $TOTAL_COUNT 个流量包"
else
    print_error "分页查询失败"
fi

print_separator

# 10. 更新流量包（如果有可用的ID）
if [ -n "$FIRST_PACKAGE_ID" ]; then
    print_step "10. 更新流量包"
    UPDATE_RESPONSE=$(curl -s -X POST "${SERVER_HOST}/api/v1/pay-service/admin/store-service/packages" \
      -H "Content-Type: application/json" \
      -H "x-trace-id: test_update_001" \
      -d "{
        \"package_id\": \"$FIRST_PACKAGE_ID\",
        \"package_name\": \"更新后的流量包\",
        \"package_desc\": \"这是更新后的流量包描述\",
        \"original_price\": 19.99
      }")
    
    echo "$UPDATE_RESPONSE" | jq '.'
    if echo "$UPDATE_RESPONSE" | jq -e '.message' > /dev/null 2>&1; then
        print_success "流量包更新成功"
    else
        print_error "流量包更新失败"
    fi
else
    print_error "跳过更新测试：没有可用的流量包ID"
fi

print_separator
print_step "测试完成"
print_success "所有API测试已完成！"
print_separator

echo -e "${BLUE}测试总结:${NC}"
echo "- 创建了4个不同类型的流量包"
echo "- 测试了终端用户和管理员查询接口"
echo "- 测试了按货币、国家、分页查询"
echo "- 测试了流量包更新功能"
echo ""
echo -e "${YELLOW}注意事项:${NC}"
echo "- 如需测试删除功能，请手动运行删除命令"
echo "- 生产环境请添加适当的认证和权限验证"
echo "- 建议在测试环境中运行此脚本"
