# 快速启动指南

## 前置要求

1. **Go 1.24+** - 编程语言环境
2. **MySQL 8.0+** - 数据库服务器
3. **Git** - 版本控制

## 数据库准备

### 1. 安装MySQL
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install mysql-server

# macOS (使用Homebrew)
brew install mysql

# Windows
# 下载并安装MySQL Community Server
```

### 2. 创建数据库和用户
```sql
-- 连接到MySQL
mysql -u root -p

-- 创建数据库
CREATE DATABASE payment_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE payment_dev_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户并授权
CREATE USER 'payment_user'@'localhost' IDENTIFIED BY 'payment_password';
GRANT ALL PRIVILEGES ON payment_db.* TO 'payment_user'@'localhost';
GRANT ALL PRIVILEGES ON payment_dev_db.* TO 'payment_user'@'localhost';
FLUSH PRIVILEGES;
```

## 项目启动

### 1. 克隆项目
```bash
git clone <repository-url>
cd payment-backend
```

### 2. 安装依赖
```bash
go mod tidy
```

### 3. 配置环境变量（可选）
```bash
# 开发环境
export PAYMENT_DATABASE_HOST=localhost
export PAYMENT_DATABASE_PORT=3306
export PAYMENT_DATABASE_USERNAME=payment_user
export PAYMENT_DATABASE_PASSWORD=payment_password
export PAYMENT_DATABASE_DATABASE=payment_dev_db

# 或者使用配置文件（推荐）
```

### 4. 运行数据库迁移
```bash
# 使用开发配置
go run scripts/migrate.go -config=configs/config.dev.yaml -action=migrate

# 或使用默认配置
go run scripts/migrate.go -action=migrate
```

### 5. 启动服务
```bash
# 使用开发配置
go run main.go serve --config=configs/config.dev.yaml

# 或使用默认配置
go run main.go serve
```

## 验证安装

### 1. 检查服务状态
```bash
curl http://localhost:8080/health
```

### 2. 检查数据库连接
```bash
go run scripts/migrate.go -action=info
```

## 开发工作流

### 1. 数据库操作
```bash
# 查看数据库信息
go run scripts/migrate.go -action=info

# 重新创建表结构
go run scripts/migrate.go -action=drop
go run scripts/migrate.go -action=migrate
```

### 2. 运行测试
```bash
# 运行所有测试
go test ./...

# 运行特定包的测试
go test ./internal/repository/mysql/...

# 生成测试覆盖率报告
go test -coverprofile=coverage.out ./...
go tool cover -html=coverage.out -o coverage.html
```

### 3. 构建生产版本
```bash
# 构建可执行文件
go build -o payment-backend .

# 使用生产配置运行
./payment-backend serve --config=configs/config.prod.yaml
```

## 配置说明

### 开发环境 (config.dev.yaml)
- 数据库: `payment_dev_db`
- 日志级别: `debug`
- 服务器模式: `debug`

### 生产环境 (config.prod.yaml)
- 数据库: `payment_db`
- 日志级别: `info`
- 服务器模式: `release`

## 常见问题

### 1. 数据库连接失败
- 检查MySQL服务是否运行
- 验证用户名和密码
- 确认数据库是否存在

### 2. 迁移失败
- 检查数据库权限
- 确认表是否已存在
- 查看详细错误日志

### 3. 端口冲突
- 修改配置文件中的端口号
- 或使用环境变量: `export PAYMENT_SERVER_PORT=8081`

## 下一步

- 阅读 [数据库文档](DATABASE.md) 了解详细的数据库配置
- 查看 [API文档](API.md) 了解接口使用方法
- 参考 [配置文档](CONFIGURATION.md) 了解所有配置选项
