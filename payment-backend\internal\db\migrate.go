package db

import (
	"fmt"

	"gorm.io/gorm"
)

// Migrate 执行数据库迁移
func Migrate(db *gorm.DB) error {
	// 自动迁移表结构（这会自动创建模型中定义的基础索引）
	if err := AutoMigrate(db); err != nil {
		return fmt.Errorf("failed to auto migrate: %w", err)
	}

	// 创建复合索引和特殊索引
	// TODO: 未来完善.
	// if err := createIndexes(db); err != nil {
	// 	return fmt.Errorf("failed to create indexes: %w", err)
	// }

	return nil
}

// MigrateWithValidation 执行数据库迁移并验证索引
func MigrateWithValidation(db *gorm.DB) error {
	// 执行标准迁移
	if err := Migrate(db); err != nil {
		return err
	}

	// 验证模型索引
	indexManager := NewIndexManager(db)
	if err := indexManager.ValidateIndexes(&OrderModel{}); err != nil {
		return fmt.Errorf("index validation failed: %w", err)
	}

	return nil
}

// createIndexes 创建额外的索引
// 注意：基础索引（如 uniqueIndex, index）已在 OrderModel 的 gorm 标签中定义
// 这里只创建复合索引和特殊索引
func createIndexes(db *gorm.DB) error {
	// 获取 OrderModel 的表名
	model := &OrderModel{}
	tableName := model.TableName()

	// 定义复合索引和特殊索引
	// 注意：单列索引已在 OrderModel 的 gorm 标签中定义，这里只定义复合索引
	indexes := []IndexDefinition{
		// 复合索引 - 用于常见查询模式
		{
			Name:    "idx_orders_user_pay_status",
			Table:   tableName,
			Columns: []string{"user_id", "pay_status"},
			Comment: "用户ID和支付状态的复合索引，用于查询用户的特定状态订单",
		},
		{
			Name:    "idx_orders_user_created",
			Table:   tableName,
			Columns: []string{"user_id", "created_at DESC"},
			Comment: "用户ID和创建时间的复合索引，用于按时间排序查询用户订单",
		},
		{
			Name:    "idx_orders_status_created",
			Table:   tableName,
			Columns: []string{"pay_status", "created_at DESC"},
			Comment: "支付状态和创建时间的复合索引，用于按状态和时间查询",
		},
		{
			Name:    "idx_orders_user_product",
			Table:   tableName,
			Columns: []string{"user_id", "product_id"},
			Comment: "用户ID和产品ID的复合索引，用于查询用户的特定产品订单",
		},
		{
			Name:    "idx_orders_psp_provider_status",
			Table:   tableName,
			Columns: []string{"psp_provider", "pay_status"},
			Comment: "PSP提供商和支付状态的复合索引，用于按提供商统计",
		},
	}

	// 创建索引
	for _, idx := range indexes {
		if err := createIndex(db, idx); err != nil {
			return fmt.Errorf("failed to create index %s: %w", idx.Name, err)
		}
	}

	return nil
}

// IndexDefinition 索引定义结构
type IndexDefinition struct {
	Name    string   // 索引名称
	Table   string   // 表名
	Columns []string // 列名列表
	Unique  bool     // 是否唯一索引
	Comment string   // 索引注释
}

// createIndex 创建单个索引
func createIndex(db *gorm.DB, idx IndexDefinition) error {
	indexType := "INDEX"
	if idx.Unique {
		indexType = "UNIQUE INDEX"
	}

	columnsStr := fmt.Sprintf("(%s)", fmt.Sprintf("%s", fmt.Sprintf("%v", idx.Columns)[1:len(fmt.Sprintf("%v", idx.Columns))-1]))
	columnsStr = fmt.Sprintf("(%s)", joinColumns(idx.Columns))

	sql := fmt.Sprintf("CREATE %s IF NOT EXISTS %s ON %s %s",
		indexType, idx.Name, idx.Table, columnsStr)

	return db.Exec(sql).Error
}

// joinColumns 连接列名
func joinColumns(columns []string) string {
	if len(columns) == 0 {
		return ""
	}
	if len(columns) == 1 {
		return columns[0]
	}

	result := columns[0]
	for i := 1; i < len(columns); i++ {
		result += ", " + columns[i]
	}
	return result
}

// DropTables 删除所有表（用于测试或重置）
func DropTables(db *gorm.DB) error {
	// 删除订单表
	tables := []string{
		"orders",
	}

	for _, table := range tables {
		if err := db.Exec(fmt.Sprintf("DROP TABLE IF EXISTS %s", table)).Error; err != nil {
			return fmt.Errorf("failed to drop table %s: %w", table, err)
		}
	}

	return nil
}

// CreateDatabase 创建数据库（如果不存在）
func CreateDatabase(db *gorm.DB, dbName string) error {
	sql := fmt.Sprintf("CREATE DATABASE IF NOT EXISTS %s CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci", dbName)
	return db.Exec(sql).Error
}

// CheckConnection 检查数据库连接
func CheckConnection(db *gorm.DB) error {
	sqlDB, err := db.DB()
	if err != nil {
		return fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}

	return sqlDB.Ping()
}

// GetTableInfo 获取表信息（用于调试）
func GetTableInfo(db *gorm.DB, tableName string) ([]map[string]interface{}, error) {
	var results []map[string]interface{}

	sql := fmt.Sprintf("DESCRIBE %s", tableName)
	if err := db.Raw(sql).Scan(&results).Error; err != nil {
		return nil, fmt.Errorf("failed to get table info for %s: %w", tableName, err)
	}

	return results, nil
}

// GetIndexInfo 获取索引信息（用于调试）
func GetIndexInfo(db *gorm.DB, tableName string) ([]map[string]interface{}, error) {
	var results []map[string]interface{}

	sql := fmt.Sprintf("SHOW INDEX FROM %s", tableName)
	if err := db.Raw(sql).Scan(&results).Error; err != nil {
		return nil, fmt.Errorf("failed to get index info for %s: %w", tableName, err)
	}

	return results, nil
}
