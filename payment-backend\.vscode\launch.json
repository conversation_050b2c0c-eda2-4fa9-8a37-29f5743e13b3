{
    // 使用 IntelliSense 了解相关属性。 
    // 悬停以查看现有属性的描述。
    // 欲了解更多信息，请访问: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Launch Package",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}",
            "args": [
                "serve",
                "--config",
                "./configs/config.dev.yaml"
            ], // 启动时传递的参数
            "env": { // 设置调试时的环境变量
                "PAYMENT_ENV": "dev",
                "POD_IP": "***************"
            },
        }
    ]
}