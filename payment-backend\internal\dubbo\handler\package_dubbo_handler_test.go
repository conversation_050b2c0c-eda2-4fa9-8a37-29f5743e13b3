package handler

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"google.golang.org/protobuf/types/known/timestamppb"

	"payment-backend/internal/domain/store"
	"payment-backend/internal/dubbo/shared-protos/gen/go/paymentpb"
	"payment-backend/internal/logger"
	"payment-backend/internal/middleware"
)

// MockPackageService 模拟流量包服务
type MockPackageService struct {
	mock.Mock
}

func (m *MockPackageService) ListAllPackages(userCtx *middleware.UserContext, filter *store.PackageFilter, pagination *store.PaginationRequest) (*store.ListPackagesResponse, error) {
	args := m.Called(userCtx, filter, pagination)
	return args.Get(0).(*store.ListPackagesResponse), args.Error(1)
}

func (m *MockPackageService) AdminAddPackages(req *store.CreatePackageRequest) error {
	args := m.Called(req)
	return args.Error(0)
}

func (m *MockPackageService) AdminDeletePackages(req *store.DeletePackageRequest) error {
	args := m.Called(req)
	return args.Error(0)
}

func (m *MockPackageService) AdminUpdatePackages(req *store.UpdatePackageRequest) error {
	args := m.Called(req)
	return args.Error(0)
}

func (m *MockPackageService) AdminListAllPackages(filter *store.PackageFilter, pagination *store.PaginationRequest) (*store.AdminListPackagesResponse, error) {
	args := m.Called(filter, pagination)
	return args.Get(0).(*store.AdminListPackagesResponse), args.Error(1)
}

func TestPackageDubboHandler_convertUserContextFromProto(t *testing.T) {
	handler := NewPackageDubboHandler(nil, logger.NewNopLogger())

	tests := []struct {
		name     string
		input    *paymentpb.UserContext
		expected *middleware.UserContext
	}{
		{
			name:  "nil input",
			input: nil,
			expected: &middleware.UserContext{},
		},
		{
			name: "valid input",
			input: &paymentpb.UserContext{
				UserId: "user123",
				Role:   "customer",
			},
			expected: &middleware.UserContext{
				UserID: "user123",
				Role:   "customer",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := handler.convertUserContextFromProto(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestPackageDubboHandler_convertPackageFilterFromProto(t *testing.T) {
	handler := NewPackageDubboHandler(nil, logger.NewNopLogger())

	tests := []struct {
		name     string
		input    *paymentpb.PackageFilter
		expected *store.PackageFilter
	}{
		{
			name:  "nil input",
			input: nil,
			expected: &store.PackageFilter{},
		},
		{
			name: "valid input",
			input: &paymentpb.PackageFilter{
				Currency: stringPtr("USD"),
				Country:  stringPtr("US"),
			},
			expected: &store.PackageFilter{
				Currency: stringPtr("USD"),
				Country:  stringPtr("US"),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := handler.convertPackageFilterFromProto(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestPackageDubboHandler_convertPaginationFromProto(t *testing.T) {
	handler := NewPackageDubboHandler(nil, logger.NewNopLogger())

	tests := []struct {
		name     string
		input    *paymentpb.PaginationRequest
		expected *store.PaginationRequest
	}{
		{
			name:  "nil input",
			input: nil,
			expected: &store.PaginationRequest{
				Limit:  50,
				Offset: 0,
			},
		},
		{
			name: "valid input",
			input: &paymentpb.PaginationRequest{
				Limit:  10,
				Offset: 20,
			},
			expected: &store.PaginationRequest{
				Limit:  10,
				Offset: 20,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := handler.convertPaginationFromProto(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestPackageDubboHandler_convertCreatePackageRequestFromProto(t *testing.T) {
	handler := NewPackageDubboHandler(nil, logger.NewNopLogger())

	now := time.Now()
	discountPrice := 8.99

	input := &paymentpb.CreatePackageRequest{
		PackageName:       "Test Package",
		PackageDesc:       "Test Description",
		Entitlement:       100,
		OriginalPrice:     9.99,
		DiscountPrice:     &discountPrice,
		DiscountStartTime: timestamppb.New(now),
		DiscountEndTime:   timestamppb.New(now.Add(24 * time.Hour)),
		SaleStatus:        "on_sale",
		Currency:          "USD",
		Country:           "US",
		Extra1:            "extra1",
		Extra2:            "extra2",
		Extra3:            100,
		Extra4:            200,
	}

	expected := &store.CreatePackageRequest{
		PackageName:       "Test Package",
		PackageDesc:       "Test Description",
		Entitlement:       100,
		OriginalPrice:     9.99,
		DiscountPrice:     &discountPrice,
		DiscountStartTime: &now,
		DiscountEndTime:   timePtr(now.Add(24 * time.Hour)),
		SaleStatus:        "on_sale",
		Currency:          "USD",
		Country:           "US",
		Extra1:            "extra1",
		Extra2:            "extra2",
		Extra3:            100,
		Extra4:            200,
	}

	result := handler.convertCreatePackageRequestFromProto(input)
	
	// 比较基本字段
	assert.Equal(t, expected.PackageName, result.PackageName)
	assert.Equal(t, expected.PackageDesc, result.PackageDesc)
	assert.Equal(t, expected.Entitlement, result.Entitlement)
	assert.Equal(t, expected.OriginalPrice, result.OriginalPrice)
	assert.Equal(t, expected.DiscountPrice, result.DiscountPrice)
	assert.Equal(t, expected.SaleStatus, result.SaleStatus)
	assert.Equal(t, expected.Currency, result.Currency)
	assert.Equal(t, expected.Country, result.Country)
	assert.Equal(t, expected.Extra1, result.Extra1)
	assert.Equal(t, expected.Extra2, result.Extra2)
	assert.Equal(t, expected.Extra3, result.Extra3)
	assert.Equal(t, expected.Extra4, result.Extra4)
	
	// 比较时间字段（允许小的时间差）
	assert.WithinDuration(t, *expected.DiscountStartTime, *result.DiscountStartTime, time.Second)
	assert.WithinDuration(t, *expected.DiscountEndTime, *result.DiscountEndTime, time.Second)
}

// 辅助函数
func stringPtr(s string) *string {
	return &s
}

func timePtr(t time.Time) *time.Time {
	return &t
}
