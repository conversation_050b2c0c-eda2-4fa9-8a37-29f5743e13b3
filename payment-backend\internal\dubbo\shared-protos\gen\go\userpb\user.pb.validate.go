// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: protos/user.proto

package userpb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ChargeRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ChargeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ChargeRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ChargeRequestMultiError, or
// nil if none found.
func (m *ChargeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ChargeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if val := m.GetPoints(); val < 1 || val > 1e+06 {
		err := ChargeRequestValidationError{
			field:  "Points",
			reason: "value must be inside range [1, 1e+06]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetReason()); l < 1 || l > 88 {
		err := ChargeRequestValidationError{
			field:  "Reason",
			reason: "value length must be between 1 and 88 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ChargeRequestMultiError(errors)
	}

	return nil
}

// ChargeRequestMultiError is an error wrapping multiple validation errors
// returned by ChargeRequest.ValidateAll() if the designated constraints
// aren't met.
type ChargeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ChargeRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ChargeRequestMultiError) AllErrors() []error { return m }

// ChargeRequestValidationError is the validation error returned by
// ChargeRequest.Validate if the designated constraints aren't met.
type ChargeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ChargeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ChargeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ChargeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ChargeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ChargeRequestValidationError) ErrorName() string { return "ChargeRequestValidationError" }

// Error satisfies the builtin error interface
func (e ChargeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sChargeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ChargeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ChargeRequestValidationError{}

// Validate checks the field values on PageRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PageRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PageRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PageRequestMultiError, or
// nil if none found.
func (m *PageRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *PageRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if val := m.GetPage(); val < 1 || val > 100000 {
		err := PageRequestValidationError{
			field:  "Page",
			reason: "value must be inside range [1, 100000]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if val := m.GetPageSize(); val < 1 || val > 10000 {
		err := PageRequestValidationError{
			field:  "PageSize",
			reason: "value must be inside range [1, 10000]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return PageRequestMultiError(errors)
	}

	return nil
}

// PageRequestMultiError is an error wrapping multiple validation errors
// returned by PageRequest.ValidateAll() if the designated constraints aren't met.
type PageRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PageRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PageRequestMultiError) AllErrors() []error { return m }

// PageRequestValidationError is the validation error returned by
// PageRequest.Validate if the designated constraints aren't met.
type PageRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PageRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PageRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PageRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PageRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PageRequestValidationError) ErrorName() string { return "PageRequestValidationError" }

// Error satisfies the builtin error interface
func (e PageRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPageRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PageRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PageRequestValidationError{}

// Validate checks the field values on PageResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PageResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PageResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PageResponseMultiError, or
// nil if none found.
func (m *PageResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *PageResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Page

	// no validation rules for PageSize

	// no validation rules for Total

	if len(errors) > 0 {
		return PageResponseMultiError(errors)
	}

	return nil
}

// PageResponseMultiError is an error wrapping multiple validation errors
// returned by PageResponse.ValidateAll() if the designated constraints aren't met.
type PageResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PageResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PageResponseMultiError) AllErrors() []error { return m }

// PageResponseValidationError is the validation error returned by
// PageResponse.Validate if the designated constraints aren't met.
type PageResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PageResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PageResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PageResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PageResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PageResponseValidationError) ErrorName() string { return "PageResponseValidationError" }

// Error satisfies the builtin error interface
func (e PageResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPageResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PageResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PageResponseValidationError{}

// Validate checks the field values on BenefitRecordResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BenefitRecordResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BenefitRecordResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BenefitRecordResponseMultiError, or nil if none found.
func (m *BenefitRecordResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *BenefitRecordResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetRecords() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BenefitRecordResponseValidationError{
						field:  fmt.Sprintf("Records[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BenefitRecordResponseValidationError{
						field:  fmt.Sprintf("Records[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BenefitRecordResponseValidationError{
					field:  fmt.Sprintf("Records[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BenefitRecordResponseValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BenefitRecordResponseValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BenefitRecordResponseValidationError{
				field:  "Page",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return BenefitRecordResponseMultiError(errors)
	}

	return nil
}

// BenefitRecordResponseMultiError is an error wrapping multiple validation
// errors returned by BenefitRecordResponse.ValidateAll() if the designated
// constraints aren't met.
type BenefitRecordResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BenefitRecordResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BenefitRecordResponseMultiError) AllErrors() []error { return m }

// BenefitRecordResponseValidationError is the validation error returned by
// BenefitRecordResponse.Validate if the designated constraints aren't met.
type BenefitRecordResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BenefitRecordResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BenefitRecordResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BenefitRecordResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BenefitRecordResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BenefitRecordResponseValidationError) ErrorName() string {
	return "BenefitRecordResponseValidationError"
}

// Error satisfies the builtin error interface
func (e BenefitRecordResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBenefitRecordResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BenefitRecordResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BenefitRecordResponseValidationError{}

// Validate checks the field values on BenefitRecord with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BenefitRecord) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BenefitRecord with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BenefitRecordMultiError, or
// nil if none found.
func (m *BenefitRecord) ValidateAll() error {
	return m.validate(true)
}

func (m *BenefitRecord) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for BenefitType

	// no validation rules for BenefitCount

	// no validation rules for PointsUsed

	// no validation rules for PointsRecharged

	// no validation rules for Reason

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return BenefitRecordMultiError(errors)
	}

	return nil
}

// BenefitRecordMultiError is an error wrapping multiple validation errors
// returned by BenefitRecord.ValidateAll() if the designated constraints
// aren't met.
type BenefitRecordMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BenefitRecordMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BenefitRecordMultiError) AllErrors() []error { return m }

// BenefitRecordValidationError is the validation error returned by
// BenefitRecord.Validate if the designated constraints aren't met.
type BenefitRecordValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BenefitRecordValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BenefitRecordValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BenefitRecordValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BenefitRecordValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BenefitRecordValidationError) ErrorName() string { return "BenefitRecordValidationError" }

// Error satisfies the builtin error interface
func (e BenefitRecordValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBenefitRecord.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BenefitRecordValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BenefitRecordValidationError{}

// Validate checks the field values on SearchRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SearchRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SearchRequestMultiError, or
// nil if none found.
func (m *SearchRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if val := m.GetPage(); val < 1 || val > 100000 {
		err := SearchRequestValidationError{
			field:  "Page",
			reason: "value must be inside range [1, 100000]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if val := m.GetPageSize(); val < 1 || val > 10000 {
		err := SearchRequestValidationError{
			field:  "PageSize",
			reason: "value must be inside range [1, 10000]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for SearchByNickname

	// no validation rules for SearchByEmail

	// no validation rules for SearchByDeviceId

	// no validation rules for SearchByUserId

	if len(errors) > 0 {
		return SearchRequestMultiError(errors)
	}

	return nil
}

// SearchRequestMultiError is an error wrapping multiple validation errors
// returned by SearchRequest.ValidateAll() if the designated constraints
// aren't met.
type SearchRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchRequestMultiError) AllErrors() []error { return m }

// SearchRequestValidationError is the validation error returned by
// SearchRequest.Validate if the designated constraints aren't met.
type SearchRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchRequestValidationError) ErrorName() string { return "SearchRequestValidationError" }

// Error satisfies the builtin error interface
func (e SearchRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchRequestValidationError{}

// Validate checks the field values on SearchResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SearchResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SearchResponseMultiError,
// or nil if none found.
func (m *SearchResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetUsers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SearchResponseValidationError{
						field:  fmt.Sprintf("Users[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SearchResponseValidationError{
						field:  fmt.Sprintf("Users[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SearchResponseValidationError{
					field:  fmt.Sprintf("Users[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Page

	// no validation rules for PageSize

	// no validation rules for TotalSize

	if len(errors) > 0 {
		return SearchResponseMultiError(errors)
	}

	return nil
}

// SearchResponseMultiError is an error wrapping multiple validation errors
// returned by SearchResponse.ValidateAll() if the designated constraints
// aren't met.
type SearchResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchResponseMultiError) AllErrors() []error { return m }

// SearchResponseValidationError is the validation error returned by
// SearchResponse.Validate if the designated constraints aren't met.
type SearchResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchResponseValidationError) ErrorName() string { return "SearchResponseValidationError" }

// Error satisfies the builtin error interface
func (e SearchResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchResponseValidationError{}

// Validate checks the field values on Empty with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Empty) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Empty with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in EmptyMultiError, or nil if none found.
func (m *Empty) ValidateAll() error {
	return m.validate(true)
}

func (m *Empty) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return EmptyMultiError(errors)
	}

	return nil
}

// EmptyMultiError is an error wrapping multiple validation errors returned by
// Empty.ValidateAll() if the designated constraints aren't met.
type EmptyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EmptyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EmptyMultiError) AllErrors() []error { return m }

// EmptyValidationError is the validation error returned by Empty.Validate if
// the designated constraints aren't met.
type EmptyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EmptyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EmptyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EmptyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EmptyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EmptyValidationError) ErrorName() string { return "EmptyValidationError" }

// Error satisfies the builtin error interface
func (e EmptyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEmpty.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EmptyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EmptyValidationError{}

// Validate checks the field values on DeleteSystemPlanRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteSystemPlanRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteSystemPlanRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteSystemPlanRequestMultiError, or nil if none found.
func (m *DeleteSystemPlanRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteSystemPlanRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetPlanId() < 1 {
		err := DeleteSystemPlanRequestValidationError{
			field:  "PlanId",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return DeleteSystemPlanRequestMultiError(errors)
	}

	return nil
}

// DeleteSystemPlanRequestMultiError is an error wrapping multiple validation
// errors returned by DeleteSystemPlanRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteSystemPlanRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteSystemPlanRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteSystemPlanRequestMultiError) AllErrors() []error { return m }

// DeleteSystemPlanRequestValidationError is the validation error returned by
// DeleteSystemPlanRequest.Validate if the designated constraints aren't met.
type DeleteSystemPlanRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteSystemPlanRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteSystemPlanRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteSystemPlanRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteSystemPlanRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteSystemPlanRequestValidationError) ErrorName() string {
	return "DeleteSystemPlanRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteSystemPlanRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteSystemPlanRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteSystemPlanRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteSystemPlanRequestValidationError{}

// Validate checks the field values on DeleteSystemFeaturePointsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *DeleteSystemFeaturePointsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteSystemFeaturePointsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// DeleteSystemFeaturePointsRequestMultiError, or nil if none found.
func (m *DeleteSystemFeaturePointsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteSystemFeaturePointsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetId() < 1 {
		err := DeleteSystemFeaturePointsRequestValidationError{
			field:  "Id",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return DeleteSystemFeaturePointsRequestMultiError(errors)
	}

	return nil
}

// DeleteSystemFeaturePointsRequestMultiError is an error wrapping multiple
// validation errors returned by
// DeleteSystemFeaturePointsRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteSystemFeaturePointsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteSystemFeaturePointsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteSystemFeaturePointsRequestMultiError) AllErrors() []error { return m }

// DeleteSystemFeaturePointsRequestValidationError is the validation error
// returned by DeleteSystemFeaturePointsRequest.Validate if the designated
// constraints aren't met.
type DeleteSystemFeaturePointsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteSystemFeaturePointsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteSystemFeaturePointsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteSystemFeaturePointsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteSystemFeaturePointsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteSystemFeaturePointsRequestValidationError) ErrorName() string {
	return "DeleteSystemFeaturePointsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteSystemFeaturePointsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteSystemFeaturePointsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteSystemFeaturePointsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteSystemFeaturePointsRequestValidationError{}

// Validate checks the field values on SystemPlans with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SystemPlans) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SystemPlans with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SystemPlansMultiError, or
// nil if none found.
func (m *SystemPlans) ValidateAll() error {
	return m.validate(true)
}

func (m *SystemPlans) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetPlans() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SystemPlansValidationError{
						field:  fmt.Sprintf("Plans[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SystemPlansValidationError{
						field:  fmt.Sprintf("Plans[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SystemPlansValidationError{
					field:  fmt.Sprintf("Plans[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return SystemPlansMultiError(errors)
	}

	return nil
}

// SystemPlansMultiError is an error wrapping multiple validation errors
// returned by SystemPlans.ValidateAll() if the designated constraints aren't met.
type SystemPlansMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SystemPlansMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SystemPlansMultiError) AllErrors() []error { return m }

// SystemPlansValidationError is the validation error returned by
// SystemPlans.Validate if the designated constraints aren't met.
type SystemPlansValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SystemPlansValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SystemPlansValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SystemPlansValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SystemPlansValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SystemPlansValidationError) ErrorName() string { return "SystemPlansValidationError" }

// Error satisfies the builtin error interface
func (e SystemPlansValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSystemPlans.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SystemPlansValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SystemPlansValidationError{}

// Validate checks the field values on SystemFeaturePoints with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SystemFeaturePoints) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SystemFeaturePoints with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SystemFeaturePointsMultiError, or nil if none found.
func (m *SystemFeaturePoints) ValidateAll() error {
	return m.validate(true)
}

func (m *SystemFeaturePoints) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if l := utf8.RuneCountInString(m.GetCountry()); l < 2 || l > 3 {
		err := SystemFeaturePointsValidationError{
			field:  "Country",
			reason: "value length must be between 2 and 3 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_SystemFeaturePoints_Country_Pattern.MatchString(m.GetCountry()) {
		err := SystemFeaturePointsValidationError{
			field:  "Country",
			reason: "value does not match regex pattern \"^[A-Z]+$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetFeature1Points() < 0.1 {
		err := SystemFeaturePointsValidationError{
			field:  "Feature1Points",
			reason: "value must be greater than or equal to 0.1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetFeature2Points() < 0.1 {
		err := SystemFeaturePointsValidationError{
			field:  "Feature2Points",
			reason: "value must be greater than or equal to 0.1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetFeature3Points() < 0 {
		err := SystemFeaturePointsValidationError{
			field:  "Feature3Points",
			reason: "value must be greater than or equal to 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return SystemFeaturePointsMultiError(errors)
	}

	return nil
}

// SystemFeaturePointsMultiError is an error wrapping multiple validation
// errors returned by SystemFeaturePoints.ValidateAll() if the designated
// constraints aren't met.
type SystemFeaturePointsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SystemFeaturePointsMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SystemFeaturePointsMultiError) AllErrors() []error { return m }

// SystemFeaturePointsValidationError is the validation error returned by
// SystemFeaturePoints.Validate if the designated constraints aren't met.
type SystemFeaturePointsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SystemFeaturePointsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SystemFeaturePointsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SystemFeaturePointsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SystemFeaturePointsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SystemFeaturePointsValidationError) ErrorName() string {
	return "SystemFeaturePointsValidationError"
}

// Error satisfies the builtin error interface
func (e SystemFeaturePointsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSystemFeaturePoints.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SystemFeaturePointsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SystemFeaturePointsValidationError{}

var _SystemFeaturePoints_Country_Pattern = regexp.MustCompile("^[A-Z]+$")

// Validate checks the field values on GetSystemFeaturePointsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetSystemFeaturePointsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSystemFeaturePointsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetSystemFeaturePointsResponseMultiError, or nil if none found.
func (m *GetSystemFeaturePointsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSystemFeaturePointsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetRecords() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetSystemFeaturePointsResponseValidationError{
						field:  fmt.Sprintf("Records[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetSystemFeaturePointsResponseValidationError{
						field:  fmt.Sprintf("Records[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetSystemFeaturePointsResponseValidationError{
					field:  fmt.Sprintf("Records[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetSystemFeaturePointsResponseMultiError(errors)
	}

	return nil
}

// GetSystemFeaturePointsResponseMultiError is an error wrapping multiple
// validation errors returned by GetSystemFeaturePointsResponse.ValidateAll()
// if the designated constraints aren't met.
type GetSystemFeaturePointsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSystemFeaturePointsResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSystemFeaturePointsResponseMultiError) AllErrors() []error { return m }

// GetSystemFeaturePointsResponseValidationError is the validation error
// returned by GetSystemFeaturePointsResponse.Validate if the designated
// constraints aren't met.
type GetSystemFeaturePointsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSystemFeaturePointsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSystemFeaturePointsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSystemFeaturePointsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSystemFeaturePointsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSystemFeaturePointsResponseValidationError) ErrorName() string {
	return "GetSystemFeaturePointsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetSystemFeaturePointsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSystemFeaturePointsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSystemFeaturePointsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSystemFeaturePointsResponseValidationError{}

// Validate checks the field values on SystemPlan with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SystemPlan) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SystemPlan with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SystemPlanMultiError, or
// nil if none found.
func (m *SystemPlan) ValidateAll() error {
	return m.validate(true)
}

func (m *SystemPlan) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PlanId

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for Price

	// no validation rules for PlanType

	if l := utf8.RuneCountInString(m.GetCountry()); l < 2 || l > 3 {
		err := SystemPlanValidationError{
			field:  "Country",
			reason: "value length must be between 2 and 3 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_SystemPlan_Country_Pattern.MatchString(m.GetCountry()) {
		err := SystemPlanValidationError{
			field:  "Country",
			reason: "value does not match regex pattern \"^[A-Z]+$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Currency

	if m.GetFeature1Total() < 1 {
		err := SystemPlanValidationError{
			field:  "Feature1Total",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetFeature2Total() < 1 {
		err := SystemPlanValidationError{
			field:  "Feature2Total",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetFeature3Total() < 0 {
		err := SystemPlanValidationError{
			field:  "Feature3Total",
			reason: "value must be greater than or equal to 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return SystemPlanMultiError(errors)
	}

	return nil
}

// SystemPlanMultiError is an error wrapping multiple validation errors
// returned by SystemPlan.ValidateAll() if the designated constraints aren't met.
type SystemPlanMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SystemPlanMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SystemPlanMultiError) AllErrors() []error { return m }

// SystemPlanValidationError is the validation error returned by
// SystemPlan.Validate if the designated constraints aren't met.
type SystemPlanValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SystemPlanValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SystemPlanValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SystemPlanValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SystemPlanValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SystemPlanValidationError) ErrorName() string { return "SystemPlanValidationError" }

// Error satisfies the builtin error interface
func (e SystemPlanValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSystemPlan.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SystemPlanValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SystemPlanValidationError{}

var _SystemPlan_Country_Pattern = regexp.MustCompile("^[A-Z]+$")

// Validate checks the field values on UserPlan with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UserPlan) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserPlan with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UserPlanMultiError, or nil
// if none found.
func (m *UserPlan) ValidateAll() error {
	return m.validate(true)
}

func (m *UserPlan) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PlanId

	// no validation rules for PlanType

	// no validation rules for ActivatedAt

	// no validation rules for ExpiredAt

	// no validation rules for Feature1Remain

	// no validation rules for Feature2Remain

	// no validation rules for Feature3Remain

	// no validation rules for Feature1Total

	// no validation rules for Feature2Total

	// no validation rules for Feature3Total

	if len(errors) > 0 {
		return UserPlanMultiError(errors)
	}

	return nil
}

// UserPlanMultiError is an error wrapping multiple validation errors returned
// by UserPlan.ValidateAll() if the designated constraints aren't met.
type UserPlanMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserPlanMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserPlanMultiError) AllErrors() []error { return m }

// UserPlanValidationError is the validation error returned by
// UserPlan.Validate if the designated constraints aren't met.
type UserPlanValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserPlanValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserPlanValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserPlanValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserPlanValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserPlanValidationError) ErrorName() string { return "UserPlanValidationError" }

// Error satisfies the builtin error interface
func (e UserPlanValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserPlan.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserPlanValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserPlanValidationError{}

// Validate checks the field values on ReduceBenefitRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReduceBenefitRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReduceBenefitRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReduceBenefitRequestMultiError, or nil if none found.
func (m *ReduceBenefitRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ReduceBenefitRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if val := m.GetBenefitType(); val < 0 || val > 3 {
		err := ReduceBenefitRequestValidationError{
			field:  "BenefitType",
			reason: "value must be inside range [0, 3]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if val := m.GetBenefitReduceCount(); val < 0 || val > 10000 {
		err := ReduceBenefitRequestValidationError{
			field:  "BenefitReduceCount",
			reason: "value must be inside range [0, 10000]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if val := m.GetPointReduceCount(); val < 0 || val > 10000 {
		err := ReduceBenefitRequestValidationError{
			field:  "PointReduceCount",
			reason: "value must be inside range [0, 10000]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetReason()); l < 1 || l > 88 {
		err := ReduceBenefitRequestValidationError{
			field:  "Reason",
			reason: "value length must be between 1 and 88 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ReduceBenefitRequestMultiError(errors)
	}

	return nil
}

// ReduceBenefitRequestMultiError is an error wrapping multiple validation
// errors returned by ReduceBenefitRequest.ValidateAll() if the designated
// constraints aren't met.
type ReduceBenefitRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReduceBenefitRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReduceBenefitRequestMultiError) AllErrors() []error { return m }

// ReduceBenefitRequestValidationError is the validation error returned by
// ReduceBenefitRequest.Validate if the designated constraints aren't met.
type ReduceBenefitRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReduceBenefitRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReduceBenefitRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReduceBenefitRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReduceBenefitRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReduceBenefitRequestValidationError) ErrorName() string {
	return "ReduceBenefitRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ReduceBenefitRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReduceBenefitRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReduceBenefitRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReduceBenefitRequestValidationError{}

// Validate checks the field values on BenefitOrder with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BenefitOrder) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BenefitOrder with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BenefitOrderMultiError, or
// nil if none found.
func (m *BenefitOrder) ValidateAll() error {
	return m.validate(true)
}

func (m *BenefitOrder) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OrderId

	if len(errors) > 0 {
		return BenefitOrderMultiError(errors)
	}

	return nil
}

// BenefitOrderMultiError is an error wrapping multiple validation errors
// returned by BenefitOrder.ValidateAll() if the designated constraints aren't met.
type BenefitOrderMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BenefitOrderMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BenefitOrderMultiError) AllErrors() []error { return m }

// BenefitOrderValidationError is the validation error returned by
// BenefitOrder.Validate if the designated constraints aren't met.
type BenefitOrderValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BenefitOrderValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BenefitOrderValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BenefitOrderValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BenefitOrderValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BenefitOrderValidationError) ErrorName() string { return "BenefitOrderValidationError" }

// Error satisfies the builtin error interface
func (e BenefitOrderValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBenefitOrder.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BenefitOrderValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BenefitOrderValidationError{}

// Validate checks the field values on DeviceInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DeviceInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeviceInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DeviceInfoMultiError, or
// nil if none found.
func (m *DeviceInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *DeviceInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DeviceId

	// no validation rules for DeviceName

	// no validation rules for DeviceType

	// no validation rules for OsName

	// no validation rules for OsVersion

	// no validation rules for AppVersion

	if len(errors) > 0 {
		return DeviceInfoMultiError(errors)
	}

	return nil
}

// DeviceInfoMultiError is an error wrapping multiple validation errors
// returned by DeviceInfo.ValidateAll() if the designated constraints aren't met.
type DeviceInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeviceInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeviceInfoMultiError) AllErrors() []error { return m }

// DeviceInfoValidationError is the validation error returned by
// DeviceInfo.Validate if the designated constraints aren't met.
type DeviceInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeviceInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeviceInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeviceInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeviceInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeviceInfoValidationError) ErrorName() string { return "DeviceInfoValidationError" }

// Error satisfies the builtin error interface
func (e DeviceInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeviceInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeviceInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeviceInfoValidationError{}

// Validate checks the field values on CreateUserRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateUserRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateUserRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateUserRequestMultiError, or nil if none found.
func (m *CreateUserRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateUserRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if err := m._validateEmail(m.GetEmail()); err != nil {
		err = CreateUserRequestValidationError{
			field:  "Email",
			reason: "value must be a valid email address",
			cause:  err,
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetPassword()); l < 6 || l > 188 {
		err := CreateUserRequestValidationError{
			field:  "Password",
			reason: "value length must be between 6 and 188 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetCountry()); l < 2 || l > 3 {
		err := CreateUserRequestValidationError{
			field:  "Country",
			reason: "value length must be between 2 and 3 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_CreateUserRequest_Country_Pattern.MatchString(m.GetCountry()) {
		err := CreateUserRequestValidationError{
			field:  "Country",
			reason: "value does not match regex pattern \"^[A-Z]+$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetLanguage()); l < 2 || l > 88 {
		err := CreateUserRequestValidationError{
			field:  "Language",
			reason: "value length must be between 2 and 88 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetDeviceInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateUserRequestValidationError{
					field:  "DeviceInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateUserRequestValidationError{
					field:  "DeviceInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeviceInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateUserRequestValidationError{
				field:  "DeviceInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for EmailVerifyCode

	// no validation rules for EmailVerifyCodeToken

	if len(errors) > 0 {
		return CreateUserRequestMultiError(errors)
	}

	return nil
}

func (m *CreateUserRequest) _validateHostname(host string) error {
	s := strings.ToLower(strings.TrimSuffix(host, "."))

	if len(host) > 253 {
		return errors.New("hostname cannot exceed 253 characters")
	}

	for _, part := range strings.Split(s, ".") {
		if l := len(part); l == 0 || l > 63 {
			return errors.New("hostname part must be non-empty and cannot exceed 63 characters")
		}

		if part[0] == '-' {
			return errors.New("hostname parts cannot begin with hyphens")
		}

		if part[len(part)-1] == '-' {
			return errors.New("hostname parts cannot end with hyphens")
		}

		for _, r := range part {
			if (r < 'a' || r > 'z') && (r < '0' || r > '9') && r != '-' {
				return fmt.Errorf("hostname parts can only contain alphanumeric characters or hyphens, got %q", string(r))
			}
		}
	}

	return nil
}

func (m *CreateUserRequest) _validateEmail(addr string) error {
	a, err := mail.ParseAddress(addr)
	if err != nil {
		return err
	}
	addr = a.Address

	if len(addr) > 254 {
		return errors.New("email addresses cannot exceed 254 characters")
	}

	parts := strings.SplitN(addr, "@", 2)

	if len(parts[0]) > 64 {
		return errors.New("email address local phrase cannot exceed 64 characters")
	}

	return m._validateHostname(parts[1])
}

// CreateUserRequestMultiError is an error wrapping multiple validation errors
// returned by CreateUserRequest.ValidateAll() if the designated constraints
// aren't met.
type CreateUserRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateUserRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateUserRequestMultiError) AllErrors() []error { return m }

// CreateUserRequestValidationError is the validation error returned by
// CreateUserRequest.Validate if the designated constraints aren't met.
type CreateUserRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateUserRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateUserRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateUserRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateUserRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateUserRequestValidationError) ErrorName() string {
	return "CreateUserRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateUserRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateUserRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateUserRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateUserRequestValidationError{}

var _CreateUserRequest_Country_Pattern = regexp.MustCompile("^[A-Z]+$")

// Validate checks the field values on LoginRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LoginRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoginRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LoginRequestMultiError, or
// nil if none found.
func (m *LoginRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *LoginRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if err := m._validateEmail(m.GetEmail()); err != nil {
		err = LoginRequestValidationError{
			field:  "Email",
			reason: "value must be a valid email address",
			cause:  err,
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Password

	if len(errors) > 0 {
		return LoginRequestMultiError(errors)
	}

	return nil
}

func (m *LoginRequest) _validateHostname(host string) error {
	s := strings.ToLower(strings.TrimSuffix(host, "."))

	if len(host) > 253 {
		return errors.New("hostname cannot exceed 253 characters")
	}

	for _, part := range strings.Split(s, ".") {
		if l := len(part); l == 0 || l > 63 {
			return errors.New("hostname part must be non-empty and cannot exceed 63 characters")
		}

		if part[0] == '-' {
			return errors.New("hostname parts cannot begin with hyphens")
		}

		if part[len(part)-1] == '-' {
			return errors.New("hostname parts cannot end with hyphens")
		}

		for _, r := range part {
			if (r < 'a' || r > 'z') && (r < '0' || r > '9') && r != '-' {
				return fmt.Errorf("hostname parts can only contain alphanumeric characters or hyphens, got %q", string(r))
			}
		}
	}

	return nil
}

func (m *LoginRequest) _validateEmail(addr string) error {
	a, err := mail.ParseAddress(addr)
	if err != nil {
		return err
	}
	addr = a.Address

	if len(addr) > 254 {
		return errors.New("email addresses cannot exceed 254 characters")
	}

	parts := strings.SplitN(addr, "@", 2)

	if len(parts[0]) > 64 {
		return errors.New("email address local phrase cannot exceed 64 characters")
	}

	return m._validateHostname(parts[1])
}

// LoginRequestMultiError is an error wrapping multiple validation errors
// returned by LoginRequest.ValidateAll() if the designated constraints aren't met.
type LoginRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoginRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoginRequestMultiError) AllErrors() []error { return m }

// LoginRequestValidationError is the validation error returned by
// LoginRequest.Validate if the designated constraints aren't met.
type LoginRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoginRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoginRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoginRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoginRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoginRequestValidationError) ErrorName() string { return "LoginRequestValidationError" }

// Error satisfies the builtin error interface
func (e LoginRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoginRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoginRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoginRequestValidationError{}

// Validate checks the field values on LoginResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LoginResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoginResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LoginResponseMultiError, or
// nil if none found.
func (m *LoginResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *LoginResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Seq

	// no validation rules for Token

	// no validation rules for ExpireAt

	// no validation rules for UserId

	if len(errors) > 0 {
		return LoginResponseMultiError(errors)
	}

	return nil
}

// LoginResponseMultiError is an error wrapping multiple validation errors
// returned by LoginResponse.ValidateAll() if the designated constraints
// aren't met.
type LoginResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoginResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoginResponseMultiError) AllErrors() []error { return m }

// LoginResponseValidationError is the validation error returned by
// LoginResponse.Validate if the designated constraints aren't met.
type LoginResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoginResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoginResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoginResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoginResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoginResponseValidationError) ErrorName() string { return "LoginResponseValidationError" }

// Error satisfies the builtin error interface
func (e LoginResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoginResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoginResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoginResponseValidationError{}

// Validate checks the field values on LoginWithCodeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LoginWithCodeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoginWithCodeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LoginWithCodeRequestMultiError, or nil if none found.
func (m *LoginWithCodeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *LoginWithCodeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if err := m._validateEmail(m.GetEmail()); err != nil {
		err = LoginWithCodeRequestValidationError{
			field:  "Email",
			reason: "value must be a valid email address",
			cause:  err,
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for EmailVerifyCode

	// no validation rules for EmailVerifyCodeToken

	if len(errors) > 0 {
		return LoginWithCodeRequestMultiError(errors)
	}

	return nil
}

func (m *LoginWithCodeRequest) _validateHostname(host string) error {
	s := strings.ToLower(strings.TrimSuffix(host, "."))

	if len(host) > 253 {
		return errors.New("hostname cannot exceed 253 characters")
	}

	for _, part := range strings.Split(s, ".") {
		if l := len(part); l == 0 || l > 63 {
			return errors.New("hostname part must be non-empty and cannot exceed 63 characters")
		}

		if part[0] == '-' {
			return errors.New("hostname parts cannot begin with hyphens")
		}

		if part[len(part)-1] == '-' {
			return errors.New("hostname parts cannot end with hyphens")
		}

		for _, r := range part {
			if (r < 'a' || r > 'z') && (r < '0' || r > '9') && r != '-' {
				return fmt.Errorf("hostname parts can only contain alphanumeric characters or hyphens, got %q", string(r))
			}
		}
	}

	return nil
}

func (m *LoginWithCodeRequest) _validateEmail(addr string) error {
	a, err := mail.ParseAddress(addr)
	if err != nil {
		return err
	}
	addr = a.Address

	if len(addr) > 254 {
		return errors.New("email addresses cannot exceed 254 characters")
	}

	parts := strings.SplitN(addr, "@", 2)

	if len(parts[0]) > 64 {
		return errors.New("email address local phrase cannot exceed 64 characters")
	}

	return m._validateHostname(parts[1])
}

// LoginWithCodeRequestMultiError is an error wrapping multiple validation
// errors returned by LoginWithCodeRequest.ValidateAll() if the designated
// constraints aren't met.
type LoginWithCodeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoginWithCodeRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoginWithCodeRequestMultiError) AllErrors() []error { return m }

// LoginWithCodeRequestValidationError is the validation error returned by
// LoginWithCodeRequest.Validate if the designated constraints aren't met.
type LoginWithCodeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoginWithCodeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoginWithCodeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoginWithCodeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoginWithCodeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoginWithCodeRequestValidationError) ErrorName() string {
	return "LoginWithCodeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e LoginWithCodeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoginWithCodeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoginWithCodeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoginWithCodeRequestValidationError{}

// Validate checks the field values on UpdatePasswordRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdatePasswordRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdatePasswordRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdatePasswordRequestMultiError, or nil if none found.
func (m *UpdatePasswordRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdatePasswordRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OldPassword

	// no validation rules for NewPassword

	if len(errors) > 0 {
		return UpdatePasswordRequestMultiError(errors)
	}

	return nil
}

// UpdatePasswordRequestMultiError is an error wrapping multiple validation
// errors returned by UpdatePasswordRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdatePasswordRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdatePasswordRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdatePasswordRequestMultiError) AllErrors() []error { return m }

// UpdatePasswordRequestValidationError is the validation error returned by
// UpdatePasswordRequest.Validate if the designated constraints aren't met.
type UpdatePasswordRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdatePasswordRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdatePasswordRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdatePasswordRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdatePasswordRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdatePasswordRequestValidationError) ErrorName() string {
	return "UpdatePasswordRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdatePasswordRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdatePasswordRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdatePasswordRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdatePasswordRequestValidationError{}

// Validate checks the field values on ResetPasswordRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ResetPasswordRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ResetPasswordRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ResetPasswordRequestMultiError, or nil if none found.
func (m *ResetPasswordRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ResetPasswordRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if err := m._validateEmail(m.GetEmail()); err != nil {
		err = ResetPasswordRequestValidationError{
			field:  "Email",
			reason: "value must be a valid email address",
			cause:  err,
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for EmailVerifyCode

	// no validation rules for EmailVerifyCodeToken

	// no validation rules for NewPassword

	if len(errors) > 0 {
		return ResetPasswordRequestMultiError(errors)
	}

	return nil
}

func (m *ResetPasswordRequest) _validateHostname(host string) error {
	s := strings.ToLower(strings.TrimSuffix(host, "."))

	if len(host) > 253 {
		return errors.New("hostname cannot exceed 253 characters")
	}

	for _, part := range strings.Split(s, ".") {
		if l := len(part); l == 0 || l > 63 {
			return errors.New("hostname part must be non-empty and cannot exceed 63 characters")
		}

		if part[0] == '-' {
			return errors.New("hostname parts cannot begin with hyphens")
		}

		if part[len(part)-1] == '-' {
			return errors.New("hostname parts cannot end with hyphens")
		}

		for _, r := range part {
			if (r < 'a' || r > 'z') && (r < '0' || r > '9') && r != '-' {
				return fmt.Errorf("hostname parts can only contain alphanumeric characters or hyphens, got %q", string(r))
			}
		}
	}

	return nil
}

func (m *ResetPasswordRequest) _validateEmail(addr string) error {
	a, err := mail.ParseAddress(addr)
	if err != nil {
		return err
	}
	addr = a.Address

	if len(addr) > 254 {
		return errors.New("email addresses cannot exceed 254 characters")
	}

	parts := strings.SplitN(addr, "@", 2)

	if len(parts[0]) > 64 {
		return errors.New("email address local phrase cannot exceed 64 characters")
	}

	return m._validateHostname(parts[1])
}

// ResetPasswordRequestMultiError is an error wrapping multiple validation
// errors returned by ResetPasswordRequest.ValidateAll() if the designated
// constraints aren't met.
type ResetPasswordRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResetPasswordRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResetPasswordRequestMultiError) AllErrors() []error { return m }

// ResetPasswordRequestValidationError is the validation error returned by
// ResetPasswordRequest.Validate if the designated constraints aren't met.
type ResetPasswordRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResetPasswordRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResetPasswordRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResetPasswordRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResetPasswordRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResetPasswordRequestValidationError) ErrorName() string {
	return "ResetPasswordRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ResetPasswordRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResetPasswordRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResetPasswordRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResetPasswordRequestValidationError{}

// Validate checks the field values on GetProfileRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetProfileRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetProfileRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetProfileRequestMultiError, or nil if none found.
func (m *GetProfileRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetProfileRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetProfileRequestMultiError(errors)
	}

	return nil
}

// GetProfileRequestMultiError is an error wrapping multiple validation errors
// returned by GetProfileRequest.ValidateAll() if the designated constraints
// aren't met.
type GetProfileRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetProfileRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetProfileRequestMultiError) AllErrors() []error { return m }

// GetProfileRequestValidationError is the validation error returned by
// GetProfileRequest.Validate if the designated constraints aren't met.
type GetProfileRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetProfileRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetProfileRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetProfileRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetProfileRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetProfileRequestValidationError) ErrorName() string {
	return "GetProfileRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetProfileRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetProfileRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetProfileRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetProfileRequestValidationError{}

// Validate checks the field values on UserProfile with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UserProfile) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserProfile with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UserProfileMultiError, or
// nil if none found.
func (m *UserProfile) ValidateAll() error {
	return m.validate(true)
}

func (m *UserProfile) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Nickname

	// no validation rules for Email

	// no validation rules for Status

	// no validation rules for Country

	// no validation rules for Lang

	// no validation rules for Birthday

	// no validation rules for Points

	// no validation rules for DeviceId

	// no validation rules for LastLoginAt

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return UserProfileMultiError(errors)
	}

	return nil
}

// UserProfileMultiError is an error wrapping multiple validation errors
// returned by UserProfile.ValidateAll() if the designated constraints aren't met.
type UserProfileMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserProfileMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserProfileMultiError) AllErrors() []error { return m }

// UserProfileValidationError is the validation error returned by
// UserProfile.Validate if the designated constraints aren't met.
type UserProfileValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserProfileValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserProfileValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserProfileValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserProfileValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserProfileValidationError) ErrorName() string { return "UserProfileValidationError" }

// Error satisfies the builtin error interface
func (e UserProfileValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserProfile.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserProfileValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserProfileValidationError{}

// Validate checks the field values on DetailProfile with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DetailProfile) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DetailProfile with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DetailProfileMultiError, or
// nil if none found.
func (m *DetailProfile) ValidateAll() error {
	return m.validate(true)
}

func (m *DetailProfile) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserId

	if all {
		switch v := interface{}(m.GetProfile()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DetailProfileValidationError{
					field:  "Profile",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DetailProfileValidationError{
					field:  "Profile",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProfile()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DetailProfileValidationError{
				field:  "Profile",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DetailProfileMultiError(errors)
	}

	return nil
}

// DetailProfileMultiError is an error wrapping multiple validation errors
// returned by DetailProfile.ValidateAll() if the designated constraints
// aren't met.
type DetailProfileMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DetailProfileMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DetailProfileMultiError) AllErrors() []error { return m }

// DetailProfileValidationError is the validation error returned by
// DetailProfile.Validate if the designated constraints aren't met.
type DetailProfileValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DetailProfileValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DetailProfileValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DetailProfileValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DetailProfileValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DetailProfileValidationError) ErrorName() string { return "DetailProfileValidationError" }

// Error satisfies the builtin error interface
func (e DetailProfileValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDetailProfile.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DetailProfileValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DetailProfileValidationError{}

// Validate checks the field values on UpdateProfileRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateProfileRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateProfileRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateProfileRequestMultiError, or nil if none found.
func (m *UpdateProfileRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateProfileRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Nickname

	// no validation rules for Lang

	// no validation rules for Birthday

	if len(errors) > 0 {
		return UpdateProfileRequestMultiError(errors)
	}

	return nil
}

// UpdateProfileRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateProfileRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateProfileRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateProfileRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateProfileRequestMultiError) AllErrors() []error { return m }

// UpdateProfileRequestValidationError is the validation error returned by
// UpdateProfileRequest.Validate if the designated constraints aren't met.
type UpdateProfileRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateProfileRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateProfileRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateProfileRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateProfileRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateProfileRequestValidationError) ErrorName() string {
	return "UpdateProfileRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateProfileRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateProfileRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateProfileRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateProfileRequestValidationError{}

// Validate checks the field values on GetEmailVerifyCodeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetEmailVerifyCodeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetEmailVerifyCodeRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetEmailVerifyCodeRequestMultiError, or nil if none found.
func (m *GetEmailVerifyCodeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetEmailVerifyCodeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if err := m._validateEmail(m.GetEmail()); err != nil {
		err = GetEmailVerifyCodeRequestValidationError{
			field:  "Email",
			reason: "value must be a valid email address",
			cause:  err,
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetEmailVerifyCodeRequestMultiError(errors)
	}

	return nil
}

func (m *GetEmailVerifyCodeRequest) _validateHostname(host string) error {
	s := strings.ToLower(strings.TrimSuffix(host, "."))

	if len(host) > 253 {
		return errors.New("hostname cannot exceed 253 characters")
	}

	for _, part := range strings.Split(s, ".") {
		if l := len(part); l == 0 || l > 63 {
			return errors.New("hostname part must be non-empty and cannot exceed 63 characters")
		}

		if part[0] == '-' {
			return errors.New("hostname parts cannot begin with hyphens")
		}

		if part[len(part)-1] == '-' {
			return errors.New("hostname parts cannot end with hyphens")
		}

		for _, r := range part {
			if (r < 'a' || r > 'z') && (r < '0' || r > '9') && r != '-' {
				return fmt.Errorf("hostname parts can only contain alphanumeric characters or hyphens, got %q", string(r))
			}
		}
	}

	return nil
}

func (m *GetEmailVerifyCodeRequest) _validateEmail(addr string) error {
	a, err := mail.ParseAddress(addr)
	if err != nil {
		return err
	}
	addr = a.Address

	if len(addr) > 254 {
		return errors.New("email addresses cannot exceed 254 characters")
	}

	parts := strings.SplitN(addr, "@", 2)

	if len(parts[0]) > 64 {
		return errors.New("email address local phrase cannot exceed 64 characters")
	}

	return m._validateHostname(parts[1])
}

// GetEmailVerifyCodeRequestMultiError is an error wrapping multiple validation
// errors returned by GetEmailVerifyCodeRequest.ValidateAll() if the
// designated constraints aren't met.
type GetEmailVerifyCodeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetEmailVerifyCodeRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetEmailVerifyCodeRequestMultiError) AllErrors() []error { return m }

// GetEmailVerifyCodeRequestValidationError is the validation error returned by
// GetEmailVerifyCodeRequest.Validate if the designated constraints aren't met.
type GetEmailVerifyCodeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetEmailVerifyCodeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetEmailVerifyCodeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetEmailVerifyCodeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetEmailVerifyCodeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetEmailVerifyCodeRequestValidationError) ErrorName() string {
	return "GetEmailVerifyCodeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetEmailVerifyCodeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetEmailVerifyCodeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetEmailVerifyCodeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetEmailVerifyCodeRequestValidationError{}

// Validate checks the field values on GetEmailVerifyCodeResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetEmailVerifyCodeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetEmailVerifyCodeResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetEmailVerifyCodeResponseMultiError, or nil if none found.
func (m *GetEmailVerifyCodeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetEmailVerifyCodeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for EmailVerifyCodeToken

	// no validation rules for EmailVerifyCode

	if len(errors) > 0 {
		return GetEmailVerifyCodeResponseMultiError(errors)
	}

	return nil
}

// GetEmailVerifyCodeResponseMultiError is an error wrapping multiple
// validation errors returned by GetEmailVerifyCodeResponse.ValidateAll() if
// the designated constraints aren't met.
type GetEmailVerifyCodeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetEmailVerifyCodeResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetEmailVerifyCodeResponseMultiError) AllErrors() []error { return m }

// GetEmailVerifyCodeResponseValidationError is the validation error returned
// by GetEmailVerifyCodeResponse.Validate if the designated constraints aren't met.
type GetEmailVerifyCodeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetEmailVerifyCodeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetEmailVerifyCodeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetEmailVerifyCodeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetEmailVerifyCodeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetEmailVerifyCodeResponseValidationError) ErrorName() string {
	return "GetEmailVerifyCodeResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetEmailVerifyCodeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetEmailVerifyCodeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetEmailVerifyCodeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetEmailVerifyCodeResponseValidationError{}
