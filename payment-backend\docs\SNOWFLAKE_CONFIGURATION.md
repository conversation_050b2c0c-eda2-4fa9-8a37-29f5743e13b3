# 雪花算法配置说明

## 概述

本文档说明了如何配置和使用雪花算法（Snowflake）来生成唯一的订单ID。

## 配置项

### 配置文件

在配置文件中添加 `snowflake` 配置节：

```yaml
# 雪花算法配置
snowflake:
  node_id: 1 # 节点ID，用于生成唯一的雪花算法ID，在分布式环境中每个节点应该有不同的ID
```

### 环境变量

可以通过环境变量覆盖配置文件中的设置：

```bash
export PAYMENT_SNOWFLAKE_NODE_ID=2
```

### 默认值

如果没有在配置文件或环境变量中指定，默认的 `node_id` 为 `1`。

## 使用方式

### 自动初始化

雪花算法会在应用启动时自动初始化，无需手动调用。初始化顺序：

1. 加载配置
2. 初始化雪花算法（使用配置中的 `node_id`）
3. 初始化数据库连接
4. 启动其他服务

### 生成订单ID

使用 `GenerateOrderID` 函数生成唯一的订单ID：

```go
import "payment-backend/internal/db"

// 生成订单ID
orderID := db.GenerateOrderID("STRIPE")
// 输出示例: 20250712090228STRIPE1943838336493817856
```

### 订单ID格式

生成的订单ID格式为：`{时间戳}{PSP提供商}{雪花算法ID}`

- **时间戳**: `YYYYMMDDHHMMSS` 格式的订单创建时间
- **PSP提供商**: 支付服务提供商名称（如 STRIPE、PAYPAL）
- **雪花算法ID**: 64位唯一标识符

## 分布式部署

在分布式环境中，每个节点应该配置不同的 `node_id` 以确保生成的ID全局唯一：

```yaml
# 节点1
snowflake:
  node_id: 1

# 节点2  
snowflake:
  node_id: 2

# 节点3
snowflake:
  node_id: 3
```

## 注意事项

1. **节点ID范围**: 节点ID应该在 0-1023 范围内
2. **唯一性**: 在分布式环境中，确保每个节点的 `node_id` 都不相同
3. **时钟同步**: 确保所有节点的系统时钟同步，避免时钟回拨问题
4. **线程安全**: 雪花算法实现是线程安全的，可以在多个 goroutine 中并发使用

## 测试

运行雪花算法相关测试：

```bash
# 测试配置加载
go test ./internal/config/ -v -run="TestSnowflake"

# 测试雪花算法功能
go test ./internal/db/ -v -run="TestSnowflake|TestGenerate"
```

## 故障排除

### 常见问题

1. **初始化失败**: 检查 `node_id` 是否在有效范围内（0-1023）
2. **ID重复**: 确保分布式环境中每个节点的 `node_id` 唯一
3. **时钟回拨**: 如果系统时钟回拨，可能会导致ID生成异常

### 日志检查

应用启动时会记录雪花算法初始化信息，可以通过日志确认配置是否正确加载。
