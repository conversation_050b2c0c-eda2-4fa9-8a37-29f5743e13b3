@echo off
setlocal

set ENV=%1
if "%ENV%"=="" set ENV=all

echo Running API tests for environment: %ENV%
echo.

cd test\testapi

if not exist test_api.exe (
    echo test_api.exe not found. Please run build.bat first.
    exit /b 1
)

echo Starting API tests...
echo =====================================
.\test_api.exe %ENV%

echo.
echo Test completed.
echo.
echo Available environments:
echo   dev1 - Development environment 1
echo   dev2 - Development environment 2  
echo   sit  - System Integration Test environment
echo   all  - Run tests on all environments (default)
echo.
echo Usage: run_test.bat [env]
echo Example: run_test.bat dev1
