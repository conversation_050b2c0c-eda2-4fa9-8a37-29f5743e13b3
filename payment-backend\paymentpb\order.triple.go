// Code generated by protoc-gen-triple. DO NOT EDIT.
//
// Source: protos/order.proto
package paymentpb

import (
	"context"
)

import (
	"dubbo.apache.org/dubbo-go/v3"
	"dubbo.apache.org/dubbo-go/v3/client"
	"dubbo.apache.org/dubbo-go/v3/common"
	"dubbo.apache.org/dubbo-go/v3/common/constant"
	"dubbo.apache.org/dubbo-go/v3/protocol/triple/triple_protocol"
	"dubbo.apache.org/dubbo-go/v3/server"
)

// This is a compile-time assertion to ensure that this generated file and the Triple package
// are compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of Triple newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of Triple or updating the Triple
// version compiled into your binary.
const _ = triple_protocol.IsAtLeastVersion0_1_0

const (
	// OrderServiceName is the fully-qualified name of the OrderService service.
	OrderServiceName = "com.aibook.payment.grpc.OrderService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// OrderServiceListAllOrdersProcedure is the fully-qualified name of the OrderService's ListAllOrders RPC.
	OrderServiceListAllOrdersProcedure = "/com.aibook.payment.grpc.OrderService/ListAllOrders"
	// OrderServiceForceRefundProcedure is the fully-qualified name of the OrderService's ForceRefund RPC.
	OrderServiceForceRefundProcedure = "/com.aibook.payment.grpc.OrderService/ForceRefund"
)

var (
	_ OrderService = (*OrderServiceImpl)(nil)
)

// OrderService is a client for the com.aibook.payment.grpc.OrderService service.
type OrderService interface {
	ListAllOrders(ctx context.Context, req *ListAllOrdersRequest, opts ...client.CallOption) (*ListAllOrdersResponse, error)
	ForceRefund(ctx context.Context, req *ForceRefundRequest, opts ...client.CallOption) (*ForceRefundResponse, error)
}

// NewOrderService constructs a client for the paymentpb.OrderService service.
func NewOrderService(cli *client.Client, opts ...client.ReferenceOption) (OrderService, error) {
	conn, err := cli.DialWithInfo("com.aibook.payment.grpc.OrderService", &OrderService_ClientInfo, opts...)
	if err != nil {
		return nil, err
	}
	return &OrderServiceImpl{
		conn: conn,
	}, nil
}

func SetConsumerOrderService(srv common.RPCService) {
	dubbo.SetConsumerServiceWithInfo(srv, &OrderService_ClientInfo)
}

// OrderServiceImpl implements OrderService.
type OrderServiceImpl struct {
	conn *client.Connection
}

func (c *OrderServiceImpl) ListAllOrders(ctx context.Context, req *ListAllOrdersRequest, opts ...client.CallOption) (*ListAllOrdersResponse, error) {
	resp := new(ListAllOrdersResponse)
	if err := c.conn.CallUnary(ctx, []interface{}{req}, resp, "ListAllOrders", opts...); err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *OrderServiceImpl) ForceRefund(ctx context.Context, req *ForceRefundRequest, opts ...client.CallOption) (*ForceRefundResponse, error) {
	resp := new(ForceRefundResponse)
	if err := c.conn.CallUnary(ctx, []interface{}{req}, resp, "ForceRefund", opts...); err != nil {
		return nil, err
	}
	return resp, nil
}

var OrderService_ClientInfo = client.ClientInfo{
	InterfaceName: "com.aibook.payment.grpc.OrderService",
	MethodNames:   []string{"ListAllOrders", "ForceRefund"},
	ConnectionInjectFunc: func(dubboCliRaw interface{}, conn *client.Connection) {
		dubboCli := dubboCliRaw.(*OrderServiceImpl)
		dubboCli.conn = conn
	},
}

// OrderServiceHandler is an implementation of the com.aibook.payment.grpc.OrderService service.
type OrderServiceHandler interface {
	ListAllOrders(context.Context, *ListAllOrdersRequest) (*ListAllOrdersResponse, error)
	ForceRefund(context.Context, *ForceRefundRequest) (*ForceRefundResponse, error)
}

func RegisterOrderServiceHandler(srv *server.Server, hdlr OrderServiceHandler, opts ...server.ServiceOption) error {
	return srv.Register(hdlr, &OrderService_ServiceInfo, opts...)
}

func SetProviderOrderService(srv common.RPCService) {
	dubbo.SetProviderServiceWithInfo(srv, &OrderService_ServiceInfo)
}

var OrderService_ServiceInfo = server.ServiceInfo{
	InterfaceName: "com.aibook.payment.grpc.OrderService",
	ServiceType:   (*OrderServiceHandler)(nil),
	Methods: []server.MethodInfo{
		{
			Name: "ListAllOrders",
			Type: constant.CallUnary,
			ReqInitFunc: func() interface{} {
				return new(ListAllOrdersRequest)
			},
			MethodFunc: func(ctx context.Context, args []interface{}, handler interface{}) (interface{}, error) {
				req := args[0].(*ListAllOrdersRequest)
				res, err := handler.(OrderServiceHandler).ListAllOrders(ctx, req)
				if err != nil {
					return nil, err
				}
				return triple_protocol.NewResponse(res), nil
			},
		},
		{
			Name: "ForceRefund",
			Type: constant.CallUnary,
			ReqInitFunc: func() interface{} {
				return new(ForceRefundRequest)
			},
			MethodFunc: func(ctx context.Context, args []interface{}, handler interface{}) (interface{}, error) {
				req := args[0].(*ForceRefundRequest)
				res, err := handler.(OrderServiceHandler).ForceRefund(ctx, req)
				if err != nil {
					return nil, err
				}
				return triple_protocol.NewResponse(res), nil
			},
		},
	},
}
