# payment-backend

payment-service 的配置

1. 环境变量示例

PAYMENT_ENV=prod  # 生产环境(默认)
PAYMENT_ENV=dev1 # 集成开发1
PAYMENT_ENV=dev2 # 集成开发2
PAYMENT_ENV=sit  # 集成测试
PAYMENT_ENV=dev # 本地开发调试

# Dubbo配置
PAYMENT_DUBBO_ENABLED=true
PAYMENT_DUBBO_PORT=20000
PAYMENT_DUBBO_IP=0.0.0.0

# Nacos配置（统一配置中心和注册中心）
PAYMENT_NACOS_ENABLED=true
PAYMENT_NACOS_ENDPOINTS=127.0.0.1:8848
PAYMENT_NACOS_NAMESPACE=payment-service
PAYMENT_NACOS_USERNAME=nacos
PAYMENT_NACOS_PASSWORD=nacos

# Nacos配置中心设置
PAYMENT_NACOS_CONFIG_ENABLED=true
PAYMENT_NACOS_CONFIG_DATA_ID=payment-backend.yaml
PAYMENT_NACOS_CONFIG_GROUP=DEFAULT_GROUP
PAYMENT_NACOS_CONFIG_TIMEOUT=30
