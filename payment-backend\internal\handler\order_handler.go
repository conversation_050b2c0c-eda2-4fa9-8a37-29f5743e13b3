package handler

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"payment-backend/internal/config"
	"payment-backend/internal/domain"
	"payment-backend/internal/logger"
	"payment-backend/internal/middleware"
)

// OrderHandler 订单处理器
type OrderHandler struct {
	orderService domain.OrderService
	logger       logger.Logger
	config       *config.Config
}

// NewOrderHandler 创建订单处理器
func NewOrderHandler(orderService domain.OrderService, logger logger.Logger, config *config.Config) *OrderHandler {
	return &OrderHandler{
		orderService: orderService,
		logger:       logger,
		config:       config,
	}
}

// CreateOrder 创建订单
// @Summary 创建订单
// @Description 创建新的订单并生成支付链接，需要用户认证
// @Tags 订单管理
// @Accept json
// @Produce json
// @Param x-user-id header string true "用户ID" example("user123")
// @Param x-role header string true "用户角色" example("customer")
// @Param request body domain.CreateOrderRequest true "创建订单请求"
// @Success 303 {object} domain.CreateOrderResponse "订单创建成功，返回支付链接"
// @Failure 400 {object} domain.ErrorResponse "请求参数错误, code=10001"
// @Failure 500 {object} domain.ErrorResponse "服务器内部错误, code=10003"
// @Router /api/v1/pay-service/order-service/orders [post]
func (h *OrderHandler) CreateOrder(c *gin.Context) {
	// 获取用户上下文
	userContext, err := middleware.MustGetUserContext(c)
	if err != nil {
		h.logger.Warn("Invalid user context type", logger.Error(err))
		c.JSON(http.StatusBadRequest, domain.NewErrorResponse(domain.ErrCodeInvalidRequest, domain.MsgUserContext))
		return
	}

	// 解析请求
	var req domain.CreateOrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn("Failed to bind request", zap.Error(err))
		c.JSON(http.StatusBadRequest, domain.NewErrorResponse(domain.ErrCodeInvalidRequest, domain.MsgInvalidRequest+err.Error()))
		return
	}

	// 创建订单
	response, err := h.orderService.CreateOrder(userContext, &req)
	if err != nil {
		h.logger.Error("Failed to create order", zap.Error(err))
		c.JSON(http.StatusInternalServerError, domain.NewErrorResponse(domain.ErrCodeCreateOrderFailed, "Failed to create order."+err.Error()))
		return
	}

	c.JSON(http.StatusSeeOther, response)
}

// GetOrder 获取订单详情
func (h *OrderHandler) GetOrder(c *gin.Context) {
	orderID := c.Param("order_id")
	if orderID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Order ID is required"})
		return
	}

	order, err := h.orderService.GetOrder(orderID)
	if err != nil {
		h.logger.Error("Failed to get order", zap.String("order_id", orderID), zap.Error(err))
		c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, order)
}

// GetOrderByID 根据数据库ID获取订单
func (h *OrderHandler) GetOrderByID(c *gin.Context) {
	idStr := c.Param("id")
	if idStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID is required"})
		return
	}

	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID format"})
		return
	}

	order, err := h.orderService.GetOrderByID(id)
	if err != nil {
		h.logger.Error("Failed to get order by ID", zap.Uint64("id", id), zap.Error(err))
		c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, order)
}

// GetUserOrders 获取用户订单列表(终端用户接口)
// @Summary 获取用户订单列表
// @Description
// @Description
// @Description ----------------------------------------------------
// @Description ### 详细 curl 测试命令请见接口自测文档:  https://isrc.iscas.ac.cn/gitlab/aibook/platform/payment-service/-/blob/dev/payment-backend/docs/test_curl.md?ref_type=heads#4-%E8%8E%B7%E5%8F%96%E7%94%A8%E6%88%B7%E8%AE%A2%E5%8D%95%E7%BB%88%E7%AB%AF%E8%B0%83%E7%94%A8-%E5%A4%96%E7%BD%91
// @Description ----------------------------------------------------
// @Tags 订单管理
// @Accept json
// @Produce json
// @Param limit query int false "每页数量，默认50，最大500" minimum(1) maximum(500) default(50)
// @Param offset query int false "偏移量，从0开始" minimum(0) default(0)
// @Param currency query string false "货币代码过滤，ISO-4217 币种, 如USD、CNY等"
// @Param pay_status query string false "支付状态过滤，如 'created'(订单创建),'paid'(支付中),'succeeded'(支付成功),'failed'(支付失败),'expired'(支付过期),'cancelled'(取消支付) 等"
// @Param payed_method query string false "支付方式过滤，待定"
// @Param psp_provider query string false "支付服务提供商过滤，如stripe、paypal 等"
// @Param payed_at_start query string false "支付时间开始，RFC3339格式，如2023-01-01T00:00:00Z"
// @Param payed_at_end query string false "支付时间结束，RFC3339格式，如2023-12-31T23:59:59Z"
// @Param refund_status query string false "退款状态过滤，如 'none'(未申请退款),'requested'(发起申请退款),'succeeded'(退款成功),'failed'(退款失败) 等"
// @Param refunded_at_start query string false "退款时间开始，RFC3339格式"
// @Param refunded_at_end query string false "退款时间结束，RFC3339格式"
// @Param psp_price_id query string false "PSP价格ID过滤"
// @Param psp_customer_email query string false "PSP客户邮箱过滤"
// @Param psp_subscription_id query string false "PSP订阅ID过滤"
// @Success 200 {object} domain.ListOrdersResponse "成功返回订单列表"
// @Failure 400 {object} domain.ErrorResponse "请求参数错误, code=10001"
// @Failure 500 {object} domain.ErrorResponse "服务器内部错误, code=10003"
// @Router /api/v1/pay-service/order-service/orders [get]
func (h *OrderHandler) GetUserOrders(c *gin.Context) {
	// 获取用户上下文
	userContext, err := middleware.MustGetUserContext(c)
	if err != nil {
		h.logger.Error("Invalid user context type")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid user context"})
		return
	}

	// 解析分页参数
	filter, pagination := h.getPageParmsByContext(c)
	// 获取订单列表
	response, err := h.orderService.GetUserOrders(userContext, filter, pagination)
	if err != nil {
		h.logger.Error("Failed to get user orders",
			zap.String("user_id", userContext.UserID),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, domain.NewErrorResponse(domain.ErrCodeInternalError, err.Error()))
		return
	}

	c.JSON(http.StatusOK, response)
}

// ListAllOrders 获取所有订单列表（管理员接口）
// @Summary 获取所有订单列表
// @Description
// @Description
// @Description ----------------------------------------------------
// @Description ### 详细 curl 测试命令请见接口自测文档:  https://isrc.iscas.ac.cn/gitlab/aibook/platform/payment-service/-/blob/dev/payment-backend/docs/test_curl.md?ref_type=heads
// @Description ----------------------------------------------------
// @Tags 订单管理
// @Accept json
// @Produce json
// @Param limit query int false "每页数量，默认50，最大500" minimum(1) maximum(500) default(50)
// @Param offset query int false "偏移量，从0开始" minimum(0) default(0)
// @Param user_id query string false "用户ID过滤"
// @Param currency query string false "货币代码过滤，ISO-4217 币种, 如USD、CNY等"
// @Param pay_status query string false "支付状态过滤，如 'created'(订单创建),'paid'(支付中),'succeeded'(支付成功),'failed'(支付失败),'expired'(支付过期),'cancelled'(取消支付) 等"
// @Param payed_method query string false "支付方式过滤，待定"
// @Param psp_provider query string false "支付服务提供商过滤，如stripe、paypal 等"
// @Param payed_at_start query string false "支付时间开始，RFC3339格式，如2023-01-01T00:00:00Z"
// @Param payed_at_end query string false "支付时间结束，RFC3339格式，如2023-12-31T23:59:59Z"
// @Param refund_status query string false "退款状态过滤，如 'none'(未申请退款),'requested'(发起申请退款),'succeeded'(退款成功),'failed'(退款失败) 等"
// @Param refunded_at_start query string false "退款时间开始，RFC3339格式"
// @Param refunded_at_end query string false "退款时间结束，RFC3339格式"
// @Param psp_price_id query string false "PSP价格ID过滤"
// @Param psp_customer_email query string false "PSP客户邮箱过滤"
// @Param psp_subscription_id query string false "PSP订阅ID过滤"
// @Success 200 {object} domain.ListOrdersResponse "成功返回订单列表"
// @Failure 400 {object} domain.ErrorResponse "请求参数错误, code=10001"
// @Failure 500 {object} domain.ErrorResponse "服务器内部错误, code=10003"
// @Router /api/v1/pay-service/admin/orders [get]
func (h *OrderHandler) ListAllOrders(c *gin.Context) {

	filter, pagination := h.getPageParmsByContext(c)
	// 调用服务层
	response, err := h.orderService.ListAllOrders(filter, pagination)
	if err != nil {
		h.logger.Error("Failed to list all orders",
			zap.Any("filter", filter),
			zap.Any("pagination", pagination),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, domain.NewErrorResponse(domain.ErrCodeInternalError, err.Error()))
		return
	}

	c.JSON(http.StatusOK, response)
}

// UpdateOrder 更新订单
func (h *OrderHandler) UpdateOrder(c *gin.Context) {
	orderID := c.Param("order_id")
	if orderID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Order ID is required"})
		return
	}

	// 解析请求
	var req domain.UpdateOrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("Failed to bind request", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 更新订单
	if err := h.orderService.UpdateOrder(orderID, &req); err != nil {
		h.logger.Error("Failed to update order", zap.String("order_id", orderID), zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Order updated successfully"})
}

// CancelOrder 取消订单
func (h *OrderHandler) CancelOrder(c *gin.Context) {
	orderID := c.Param("order_id")
	if orderID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Order ID is required"})
		return
	}

	if err := h.orderService.CancelOrder(orderID); err != nil {
		h.logger.Error("Failed to cancel order", zap.String("order_id", orderID), zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Order cancelled successfully"})
}

// ForceRefund 管理员强制订单退款
// @Summary 管理员强制订单退款
// @Description 管理员强制退款，不论订单是否已支付，都可以退款
// @Tags 订单管理
// @Accept json
// @Produce json
// @Param order_id path string true "订单ID"
// @Param request body domain.RefundOrderRequest false "退款请求"
// @Success 200 {object} domain.SuccessResponse "成功退款"
// @Failure 400 {object} domain.ErrorResponse "请求参数错误, code=10001"
// @Failure 500 {object} domain.ErrorResponse "服务器内部错误, code=10003"
// @Router /api/v1/pay-service/admin/orders/:order_id/force-refund [post]
func (h *OrderHandler) ForceRefund(c *gin.Context) {
	orderID := c.Param("order_id")
	if orderID == "" {
		c.JSON(http.StatusBadRequest,
			domain.NewErrorResponse(domain.ErrCodeInvalidRequest, "Order ID is required"))
		return
	}

	// 解析退款金额（可选）
	var req domain.RefundOrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("Failed to bind request", zap.Error(err))
		c.JSON(http.StatusBadRequest,
			domain.NewErrorResponse(domain.ErrCodeInvalidRequest, err.Error()))
		return
	}

	if err := h.orderService.RefundOrder(orderID, req.Amount); err != nil {
		h.logger.Error("Failed to refund order", zap.String("order_id", orderID), zap.Error(err))
		c.JSON(http.StatusBadRequest,
			domain.NewErrorResponse(domain.ErrCodeInternalError, err.Error()))
		return
	}

	c.JSON(http.StatusOK, domain.NewSuccessResponse("Order refunded successfully"))
}

// ProcessWebhook 处理支付网关 stripe 的 webhook
func (h *OrderHandler) ProcessWebhookStripe(c *gin.Context) {
	// 获取请求体
	payload, err := c.GetRawData()
	if err != nil {
		h.logger.Error("Failed to get request body", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to read request body"})
		return
	}

	// 获取签名等 HEADER
	httpHeader := c.Request.Header

	// 处理webhook
	if err := h.orderService.ProcessWebhook(domain.PSPProviderStripe, httpHeader, payload); err != nil {
		h.logger.Error("Failed to process webhook stripe",
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Webhook processed successfully"})
}

// HealthCheck 健康检查
func (h *OrderHandler) HealthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":  "ok",
		"service": "order-service",
	})
}

func (h *OrderHandler) getPageParmsByContext(c *gin.Context) (*domain.OrderFilter, *domain.PaginationRequest) {

	// 获取分页配置
	defaultLimit := h.config.Admin.Pagination.DefaultLimit
	maxLimit := h.config.Admin.Pagination.MaxLimit

	// 解析分页参数
	limitStr := c.DefaultQuery("limit", strconv.Itoa(defaultLimit))
	offsetStr := c.DefaultQuery("offset", "0")

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 || limit > maxLimit {
		limit = defaultLimit
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil || offset < 0 {
		offset = 0
	}

	pagination := &domain.PaginationRequest{
		Limit:  limit,
		Offset: offset,
	}

	// 解析过滤条件
	filter := &domain.OrderFilter{}

	// 用户ID过滤
	if userID := c.Query("user_id"); userID != "" {
		filter.UserID = &userID
	}

	// 货币过滤
	if currency := c.Query("currency"); currency != "" {
		filter.Currency = &currency
	}

	// 支付状态过滤
	if payStatus := c.Query("pay_status"); payStatus != "" {
		filter.PayStatus = &payStatus
	}

	// 支付方式过滤
	if payedMethod := c.Query("payed_method"); payedMethod != "" {
		filter.PayedMethod = &payedMethod
	}

	// PSP提供商过滤
	if pspProvider := c.Query("psp_provider"); pspProvider != "" {
		filter.PSPProvider = &pspProvider
	}

	// 支付时间范围过滤
	if payedAtStart := c.Query("payed_at_start"); payedAtStart != "" {
		if t, err := time.Parse(time.RFC3339, payedAtStart); err == nil {
			filter.PayedAtStart = &t
		}
	}
	if payedAtEnd := c.Query("payed_at_end"); payedAtEnd != "" {
		if t, err := time.Parse(time.RFC3339, payedAtEnd); err == nil {
			filter.PayedAtEnd = &t
		}
	}

	// 退款状态过滤
	if refundStatus := c.Query("refund_status"); refundStatus != "" {
		filter.RefundStatus = &refundStatus
	}

	// 退款时间范围过滤
	if refundedAtStart := c.Query("refunded_at_start"); refundedAtStart != "" {
		if t, err := time.Parse(time.RFC3339, refundedAtStart); err == nil {
			filter.RefundedAtStart = &t
		}
	}
	if refundedAtEnd := c.Query("refunded_at_end"); refundedAtEnd != "" {
		if t, err := time.Parse(time.RFC3339, refundedAtEnd); err == nil {
			filter.RefundedAtEnd = &t
		}
	}

	// PSP价格ID过滤
	if pspPriceID := c.Query("psp_price_id"); pspPriceID != "" {
		filter.PSPPriceID = &pspPriceID
	}

	// PSP客户邮箱过滤
	if pspCustomerEmail := c.Query("psp_customer_email"); pspCustomerEmail != "" {
		filter.PSPCustomerEmail = &pspCustomerEmail
	}

	// PSP订阅ID过滤
	if pspSubscriptionID := c.Query("psp_subscription_id"); pspSubscriptionID != "" {
		filter.PSPSubscriptionID = &pspSubscriptionID
	}

	return filter, pagination
}
