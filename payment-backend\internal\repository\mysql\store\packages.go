package store

import (
	"fmt"

	"gorm.io/gorm"

	dbStore "payment-backend/internal/db/store"
	"payment-backend/internal/domain/store"
)

// packageRepository MySQL流量包仓储实现
type packageRepository struct {
	db *gorm.DB
}

// NewPackageRepository 创建MySQL流量包仓储
func NewPackageRepository(database *gorm.DB) store.PackageRepository {
	return &packageRepository{
		db: database,
	}
}

// Create 创建流量包记录
func (r *packageRepository) Create(pkg *store.Package) error {
	model := &dbStore.PackageModel{}
	model.FromDomain(pkg)

	if err := r.db.Create(model).Error; err != nil {
		return fmt.Errorf("failed to create package: %w", err)
	}

	// 更新domain对象的ID
	pkg.ID = model.ID
	pkg.PackageID = model.PackageID
	pkg.CreatedAt = model.CreatedAt
	pkg.UpdatedAt = model.UpdatedAt

	return nil
}

// GetByID 根据ID获取流量包记录
func (r *packageRepository) GetByID(id uint64) (*store.Package, error) {
	var model dbStore.PackageModel

	if err := r.db.Where("id = ? AND deleted = 0", id).First(&model).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("package with ID %d not found", id)
		}
		return nil, fmt.Errorf("failed to get package by ID: %w", err)
	}

	return model.ToDomain()
}

// GetByPackageID 根据PackageID获取流量包记录
func (r *packageRepository) GetByPackageID(packageID string) (*store.Package, error) {
	var model dbStore.PackageModel

	if err := r.db.Where("package_id = ? AND deleted = 0", packageID).First(&model).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("package with PackageID %s not found", packageID)
		}
		return nil, fmt.Errorf("failed to get package by PackageID: %w", err)
	}

	return model.ToDomain()
}

// Update 更新流量包记录
func (r *packageRepository) Update(pkg *store.Package) error {
	model := &dbStore.PackageModel{}
	model.FromDomain(pkg)

	if err := r.db.Where("id = ? AND deleted = 0", pkg.ID).Updates(model).Error; err != nil {
		return fmt.Errorf("failed to update package: %w", err)
	}

	return nil
}

// SoftDelete 软删除流量包记录
func (r *packageRepository) SoftDelete(packageID string) error {
	if err := r.db.Model(&dbStore.PackageModel{}).
		Where("package_id = ? AND deleted = 0", packageID).
		Updates(map[string]interface{}{
			"deleted":    1,
			"deleted_at": gorm.Expr("NOW()"),
		}).Error; err != nil {
		return fmt.Errorf("failed to soft delete package: %w", err)
	}

	return nil
}

// List 获取流量包列表
func (r *packageRepository) List(filter *store.PackageFilter, pagination *store.PaginationRequest) ([]*store.Package, int64, error) {
	var models []dbStore.PackageModel
	var total int64

	query := r.db.Model(&dbStore.PackageModel{}).Where("deleted = 0")

	// 应用过滤条件
	if filter != nil {
		if filter.Currency != nil {
			query = query.Where("currency = ?", *filter.Currency)
		}
		if filter.Country != nil {
			query = query.Where("country = ?", *filter.Country)
		}
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count packages: %w", err)
	}

	// 应用分页
	if pagination != nil {
		query = query.Offset(pagination.Offset).Limit(pagination.Limit)
	}

	// 获取数据
	if err := query.Order("created_at DESC").Find(&models).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to list packages: %w", err)
	}

	// 转换为domain对象
	packages := make([]*store.Package, 0, len(models))
	for _, model := range models {
		pkg, err := model.ToDomain()
		if err != nil {
			return nil, 0, fmt.Errorf("failed to convert model to domain: %w", err)
		}
		packages = append(packages, pkg)
	}

	return packages, total, nil
}

// ListByCountryOrCurrency 根据国家或货币获取流量包列表
func (r *packageRepository) ListByCountryOrCurrency(country, currency string, pagination *store.PaginationRequest) ([]*store.Package, int64, error) {
	var models []dbStore.PackageModel
	var total int64

	query := r.db.Model(&dbStore.PackageModel{}).Where("deleted = 0")

	// 优先使用货币，其次使用国家
	if currency != "" {
		query = query.Where("currency = ?", currency)
	} else if country != "" {
		query = query.Where("country = ?", country)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count packages: %w", err)
	}

	// 应用分页
	if pagination != nil {
		query = query.Offset(pagination.Offset).Limit(pagination.Limit)
	}

	// 获取数据
	if err := query.Order("created_at DESC").Find(&models).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to list packages: %w", err)
	}

	// 转换为domain对象
	packages := make([]*store.Package, 0, len(models))
	for _, model := range models {
		pkg, err := model.ToDomain()
		if err != nil {
			return nil, 0, fmt.Errorf("failed to convert model to domain: %w", err)
		}
		packages = append(packages, pkg)
	}

	return packages, total, nil
}
