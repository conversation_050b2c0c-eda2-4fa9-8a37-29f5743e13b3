// Code generated by protoc-gen-triple. DO NOT EDIT.
//
// Source: protos/market.proto
package marketpb

import (
	"context"
)

import (
	"dubbo.apache.org/dubbo-go/v3"
	"dubbo.apache.org/dubbo-go/v3/client"
	"dubbo.apache.org/dubbo-go/v3/common"
	"dubbo.apache.org/dubbo-go/v3/common/constant"
	"dubbo.apache.org/dubbo-go/v3/protocol/triple/triple_protocol"
	"dubbo.apache.org/dubbo-go/v3/server"
)

// This is a compile-time assertion to ensure that this generated file and the Triple package
// are compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of Triple newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of Triple or updating the Triple
// version compiled into your binary.
const _ = triple_protocol.IsAtLeastVersion0_1_0

const (
	// MarketServiceName is the fully-qualified name of the MarketService service.
	MarketServiceName = "user.MarketService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// MarketServiceShareBookProcedure is the fully-qualified name of the MarketService's ShareBook RPC.
	MarketServiceShareBookProcedure = "/user.MarketService/ShareBook"
	// MarketServiceBookDetailProcedure is the fully-qualified name of the MarketService's BookDetail RPC.
	MarketServiceBookDetailProcedure = "/user.MarketService/BookDetail"
	// MarketServiceBookModifyProcedure is the fully-qualified name of the MarketService's BookModify RPC.
	MarketServiceBookModifyProcedure = "/user.MarketService/BookModify"
	// MarketServiceSearchBookProcedure is the fully-qualified name of the MarketService's SearchBook RPC.
	MarketServiceSearchBookProcedure = "/user.MarketService/SearchBook"
	// MarketServiceThemeTopkProcedure is the fully-qualified name of the MarketService's ThemeTopk RPC.
	MarketServiceThemeTopkProcedure = "/user.MarketService/ThemeTopk"
	// MarketServiceThemeBookTopkProcedure is the fully-qualified name of the MarketService's ThemeBookTopk RPC.
	MarketServiceThemeBookTopkProcedure = "/user.MarketService/ThemeBookTopk"
)

var (
	_ MarketService = (*MarketServiceImpl)(nil)
)

// MarketService is a client for the user.MarketService service.
type MarketService interface {
	ShareBook(ctx context.Context, req *ShareBookRequest, opts ...client.CallOption) (*Empty, error)
	BookDetail(ctx context.Context, req *BookDetailRequest, opts ...client.CallOption) (*BookDetailResponse, error)
	BookModify(ctx context.Context, req *BookModifyRequest, opts ...client.CallOption) (*Empty, error)
	SearchBook(ctx context.Context, req *SearchBookRequest, opts ...client.CallOption) (*SearchBookResponse, error)
	ThemeTopk(ctx context.Context, req *Empty, opts ...client.CallOption) (*ThemeTopkResponse, error)
	ThemeBookTopk(ctx context.Context, req *ThemeBookTopkRequest, opts ...client.CallOption) (*ThemeBookTopkResponse, error)
}

// NewMarketService constructs a client for the marketpb.MarketService service.
func NewMarketService(cli *client.Client, opts ...client.ReferenceOption) (MarketService, error) {
	conn, err := cli.DialWithInfo("user.MarketService", &MarketService_ClientInfo, opts...)
	if err != nil {
		return nil, err
	}
	return &MarketServiceImpl{
		conn: conn,
	}, nil
}

func SetConsumerMarketService(srv common.RPCService) {
	dubbo.SetConsumerServiceWithInfo(srv, &MarketService_ClientInfo)
}

// MarketServiceImpl implements MarketService.
type MarketServiceImpl struct {
	conn *client.Connection
}

func (c *MarketServiceImpl) ShareBook(ctx context.Context, req *ShareBookRequest, opts ...client.CallOption) (*Empty, error) {
	resp := new(Empty)
	if err := c.conn.CallUnary(ctx, []interface{}{req}, resp, "ShareBook", opts...); err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *MarketServiceImpl) BookDetail(ctx context.Context, req *BookDetailRequest, opts ...client.CallOption) (*BookDetailResponse, error) {
	resp := new(BookDetailResponse)
	if err := c.conn.CallUnary(ctx, []interface{}{req}, resp, "BookDetail", opts...); err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *MarketServiceImpl) BookModify(ctx context.Context, req *BookModifyRequest, opts ...client.CallOption) (*Empty, error) {
	resp := new(Empty)
	if err := c.conn.CallUnary(ctx, []interface{}{req}, resp, "BookModify", opts...); err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *MarketServiceImpl) SearchBook(ctx context.Context, req *SearchBookRequest, opts ...client.CallOption) (*SearchBookResponse, error) {
	resp := new(SearchBookResponse)
	if err := c.conn.CallUnary(ctx, []interface{}{req}, resp, "SearchBook", opts...); err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *MarketServiceImpl) ThemeTopk(ctx context.Context, req *Empty, opts ...client.CallOption) (*ThemeTopkResponse, error) {
	resp := new(ThemeTopkResponse)
	if err := c.conn.CallUnary(ctx, []interface{}{req}, resp, "ThemeTopk", opts...); err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *MarketServiceImpl) ThemeBookTopk(ctx context.Context, req *ThemeBookTopkRequest, opts ...client.CallOption) (*ThemeBookTopkResponse, error) {
	resp := new(ThemeBookTopkResponse)
	if err := c.conn.CallUnary(ctx, []interface{}{req}, resp, "ThemeBookTopk", opts...); err != nil {
		return nil, err
	}
	return resp, nil
}

var MarketService_ClientInfo = client.ClientInfo{
	InterfaceName: "user.MarketService",
	MethodNames:   []string{"ShareBook", "BookDetail", "BookModify", "SearchBook", "ThemeTopk", "ThemeBookTopk"},
	ConnectionInjectFunc: func(dubboCliRaw interface{}, conn *client.Connection) {
		dubboCli := dubboCliRaw.(*MarketServiceImpl)
		dubboCli.conn = conn
	},
}

// MarketServiceHandler is an implementation of the user.MarketService service.
type MarketServiceHandler interface {
	ShareBook(context.Context, *ShareBookRequest) (*Empty, error)
	BookDetail(context.Context, *BookDetailRequest) (*BookDetailResponse, error)
	BookModify(context.Context, *BookModifyRequest) (*Empty, error)
	SearchBook(context.Context, *SearchBookRequest) (*SearchBookResponse, error)
	ThemeTopk(context.Context, *Empty) (*ThemeTopkResponse, error)
	ThemeBookTopk(context.Context, *ThemeBookTopkRequest) (*ThemeBookTopkResponse, error)
}

func RegisterMarketServiceHandler(srv *server.Server, hdlr MarketServiceHandler, opts ...server.ServiceOption) error {
	return srv.Register(hdlr, &MarketService_ServiceInfo, opts...)
}

func SetProviderMarketService(srv common.RPCService) {
	dubbo.SetProviderServiceWithInfo(srv, &MarketService_ServiceInfo)
}

var MarketService_ServiceInfo = server.ServiceInfo{
	InterfaceName: "user.MarketService",
	ServiceType:   (*MarketServiceHandler)(nil),
	Methods: []server.MethodInfo{
		{
			Name: "ShareBook",
			Type: constant.CallUnary,
			ReqInitFunc: func() interface{} {
				return new(ShareBookRequest)
			},
			MethodFunc: func(ctx context.Context, args []interface{}, handler interface{}) (interface{}, error) {
				req := args[0].(*ShareBookRequest)
				res, err := handler.(MarketServiceHandler).ShareBook(ctx, req)
				if err != nil {
					return nil, err
				}
				return triple_protocol.NewResponse(res), nil
			},
		},
		{
			Name: "BookDetail",
			Type: constant.CallUnary,
			ReqInitFunc: func() interface{} {
				return new(BookDetailRequest)
			},
			MethodFunc: func(ctx context.Context, args []interface{}, handler interface{}) (interface{}, error) {
				req := args[0].(*BookDetailRequest)
				res, err := handler.(MarketServiceHandler).BookDetail(ctx, req)
				if err != nil {
					return nil, err
				}
				return triple_protocol.NewResponse(res), nil
			},
		},
		{
			Name: "BookModify",
			Type: constant.CallUnary,
			ReqInitFunc: func() interface{} {
				return new(BookModifyRequest)
			},
			MethodFunc: func(ctx context.Context, args []interface{}, handler interface{}) (interface{}, error) {
				req := args[0].(*BookModifyRequest)
				res, err := handler.(MarketServiceHandler).BookModify(ctx, req)
				if err != nil {
					return nil, err
				}
				return triple_protocol.NewResponse(res), nil
			},
		},
		{
			Name: "SearchBook",
			Type: constant.CallUnary,
			ReqInitFunc: func() interface{} {
				return new(SearchBookRequest)
			},
			MethodFunc: func(ctx context.Context, args []interface{}, handler interface{}) (interface{}, error) {
				req := args[0].(*SearchBookRequest)
				res, err := handler.(MarketServiceHandler).SearchBook(ctx, req)
				if err != nil {
					return nil, err
				}
				return triple_protocol.NewResponse(res), nil
			},
		},
		{
			Name: "ThemeTopk",
			Type: constant.CallUnary,
			ReqInitFunc: func() interface{} {
				return new(Empty)
			},
			MethodFunc: func(ctx context.Context, args []interface{}, handler interface{}) (interface{}, error) {
				req := args[0].(*Empty)
				res, err := handler.(MarketServiceHandler).ThemeTopk(ctx, req)
				if err != nil {
					return nil, err
				}
				return triple_protocol.NewResponse(res), nil
			},
		},
		{
			Name: "ThemeBookTopk",
			Type: constant.CallUnary,
			ReqInitFunc: func() interface{} {
				return new(ThemeBookTopkRequest)
			},
			MethodFunc: func(ctx context.Context, args []interface{}, handler interface{}) (interface{}, error) {
				req := args[0].(*ThemeBookTopkRequest)
				res, err := handler.(MarketServiceHandler).ThemeBookTopk(ctx, req)
				if err != nil {
					return nil, err
				}
				return triple_protocol.NewResponse(res), nil
			},
		},
	},
}
