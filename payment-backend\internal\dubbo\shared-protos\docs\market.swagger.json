{"swagger": "2.0", "info": {"title": "protos/market.proto", "version": "version not set"}, "tags": [{"name": "MarketService"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/v1/market/book": {"get": {"summary": "搜索绘本（按主题、按标题）， 按热度排序返回", "operationId": "MarketService_SearchBook", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/userSearchBookResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "searchStr", "in": "query", "required": false, "type": "string"}, {"name": "page", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "pageSize", "in": "query", "required": false, "type": "integer", "format": "int64"}], "tags": ["MarketService"]}}, "/api/v1/market/book/{bookId}": {"get": {"summary": "绘本详情", "operationId": "MarketService_BookDetail", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/userBookDetailResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "bookId", "in": "path", "required": true, "type": "string", "format": "uint64"}], "tags": ["MarketService"]}, "post": {"summary": "绘本属性修改(如审核，下架, 删除)", "operationId": "MarketService_BookModify", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/userEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "bookId", "description": "绘本ID", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/MarketServiceBookModifyBody"}}], "tags": ["MarketService"]}}, "/api/v1/market/share": {"post": {"summary": "共享绘本", "operationId": "MarketService_ShareBook", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/userEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/userShareBookRequest"}}], "tags": ["MarketService"]}}, "/api/v1/market/theme/book/topk": {"get": {"summary": "指定主题下的绘本 topK", "operationId": "MarketService_ThemeBookTopk", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/userThemeBookTopkResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "themeId", "in": "query", "required": false, "type": "string", "format": "uint64"}], "tags": ["MarketService"]}}, "/api/v1/market/theme/topk": {"get": {"summary": "绘本主题 topK", "operationId": "MarketService_ThemeTopk", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/userThemeTopkResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "tags": ["MarketService"]}}}, "definitions": {"MarketServiceBookModifyBody": {"type": "object", "properties": {"title": {"type": "string", "title": "绘本标题"}, "description": {"type": "string", "title": "绘本描述"}, "cover": {"type": "string", "title": "绘本封面"}, "themeids": {"type": "array", "items": {"type": "integer", "format": "int32"}, "title": "绘本主题"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}, "userBookDetailResponse": {"type": "object", "properties": {"bookInfo": {"$ref": "#/definitions/userBookInfo"}}}, "userBookInfo": {"type": "object", "properties": {"bookId": {"type": "string", "format": "uint64", "title": "绘本ID"}, "title": {"type": "string", "title": "绘本标题"}, "description": {"type": "string", "title": "绘本描述"}, "cover": {"type": "string", "title": "绘本封面"}, "recommendScore": {"type": "integer", "format": "int64", "title": "绘本推荐分数"}, "downloadCount": {"type": "integer", "format": "int64", "title": "绘本下载次数"}, "createAt": {"type": "integer", "format": "int64", "title": "绘本进入绘本市场时间"}, "updateAt": {"type": "integer", "format": "int64", "title": "最后更新时间"}}}, "userEmpty": {"type": "object"}, "userSearchBookResponse": {"type": "object", "properties": {"books": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/userBookInfo"}}, "page": {"type": "integer", "format": "int64"}, "pageSize": {"type": "integer", "format": "int64"}, "total": {"type": "integer", "format": "int64"}}}, "userShareBookRequest": {"type": "object", "properties": {"bookId": {"type": "string", "format": "uint64", "title": "绘本ID"}}}, "userTheme": {"type": "object", "properties": {"themeId": {"type": "string", "format": "uint64", "title": "绘本主题ID"}, "name": {"type": "string", "title": "绘本主题名称"}, "description": {"type": "string", "title": "绘本主题描述"}, "cover": {"type": "string", "title": "绘本主题封面"}, "recommendScore": {"type": "integer", "format": "int64", "title": "绘本主题推荐分数"}, "bookCount": {"type": "integer", "format": "int64", "title": "绘本数量"}}}, "userThemeBookTopkResponse": {"type": "object", "properties": {"books": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/userBookInfo"}, "title": "绘本数组"}}}, "userThemeTopkResponse": {"type": "object", "properties": {"themes": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/userTheme"}, "title": "绘本主题数组"}}}}}