# Payment Backend Environment Variables Example
# 支付后端环境变量配置示例

# 服务器配置
PAYMENT_SERVER_HOST=0.0.0.0
PAYMENT_SERVER_PORT=8080
PAYMENT_SERVER_READ_TIMEOUT=30
PAYMENT_SERVER_WRITE_TIMEOUT=30
PAYMENT_SERVER_MODE=release

# 数据库配置
PAYMENT_DATABASE_DRIVER=mysql
PAYMENT_DATABASE_HOST=localhost
PAYMENT_DATABASE_PORT=3306
PAYMENT_DATABASE_USERNAME=payment_user
PAYMENT_DATABASE_PASSWORD=payment_password
PAYMENT_DATABASE_DATABASE=payment_db
PAYMENT_DATABASE_SSL_MODE=disable

# 支付配置 - PayPal
PAYMENT_PAYMENT_PROVIDERS_PAYPAL_ENABLED=true
PAYMENT_PAYMENT_PROVIDERS_PAYPAL_API_KEY=your_paypal_api_key
PAYMENT_PAYMENT_PROVIDERS_PAYPAL_SECRET_KEY=your_paypal_secret_key
PAYMENT_PAYMENT_PROVIDERS_PAYPAL_SUCCESS_URL=https://your-domain.com/success
PAYMENT_PAYMENT_PROVIDERS_PAYPAL_CANCEL_URL=https://your-domain.com/cancel
PAYMENT_PAYMENT_PROVIDERS_PAYPAL_WEBHOOK_URL=https://your-domain.com/api/v1/pay-service/webhooks/paypal
PAYMENT_PAYMENT_PROVIDERS_PAYPAL_WEBHOOK_SECRET=your_paypal_webhook_secret
PAYMENT_PAYMENT_PROVIDERS_PAYPAL_SETTINGS_ENVIRONMENT=sandbox

# 支付配置 - Stripe
PAYMENT_PAYMENT_PROVIDERS_STRIPE_ENABLED=true
PAYMENT_PAYMENT_PROVIDERS_STRIPE_API_KEY=your_stripe_api_key
PAYMENT_PAYMENT_PROVIDERS_STRIPE_SECRET_KEY=your_stripe_secret_key
PAYMENT_PAYMENT_PROVIDERS_STRIPE_SUCCESS_URL=https://your-domain.com/success
PAYMENT_PAYMENT_PROVIDERS_STRIPE_CANCEL_URL=https://your-domain.com/cancel
PAYMENT_PAYMENT_PROVIDERS_STRIPE_WEBHOOK_URL=https://your-domain.com/api/v1/pay-service/webhooks/stripe
PAYMENT_PAYMENT_PROVIDERS_STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret
PAYMENT_PAYMENT_PROVIDERS_STRIPE_SETTINGS_ENVIRONMENT=test

# 日志配置
PAYMENT_LOG_LEVEL=info
PAYMENT_LOG_FORMAT=json
PAYMENT_LOG_OUTPUT=stdout
PAYMENT_LOG_FILENAME=logs/payment-backend.log
PAYMENT_LOG_MAX_SIZE=50
PAYMENT_LOG_MAX_BACKUPS=14
PAYMENT_LOG_MAX_AGE=28

# 生产环境示例
# PAYMENT_SERVER_MODE=release
# PAYMENT_LOG_LEVEL=warn
# PAYMENT_LOG_FORMAT=json
# PAYMENT_PAYMENT_PROVIDERS_STRIPE_SETTINGS_ENVIRONMENT=live
# PAYMENT_PAYMENT_PROVIDERS_PAYPAL_SETTINGS_ENVIRONMENT=live

# 雪花算法配置
PAYMENT_SNOWFLAKE_NODE_ID=-1

# 管理员配置
PAYMENT_ADMIN_ALLOWED_ROLES=admin,super_admin
PAYMENT_ADMIN_PAGINATION_DEFAULT_LIMIT=50
PAYMENT_ADMIN_PAGINATION_MAX_LIMIT=500

# Dubbo配置
PAYMENT_DUBBO_ENABLED=false
PAYMENT_DUBBO_PORT=20000
PAYMENT_DUBBO_IP=0.0.0.0

# Nacos配置（统一配置中心和注册中心）
PAYMENT_NACOS_ENABLED=false
PAYMENT_NACOS_ENDPOINTS=127.0.0.1:8848
PAYMENT_NACOS_NAMESPACE=payment-service
PAYMENT_NACOS_USERNAME=nacos
PAYMENT_NACOS_PASSWORD=nacos

# Nacos配置中心设置
PAYMENT_NACOS_CONFIG_ENABLED=false
PAYMENT_NACOS_CONFIG_DATA_ID=payment-backend.yaml
PAYMENT_NACOS_CONFIG_GROUP=DEFAULT_GROUP
PAYMENT_NACOS_CONFIG_TIMEOUT=30

# Nacos注册中心设置
PAYMENT_NACOS_REGISTRY_ENABLED=false


