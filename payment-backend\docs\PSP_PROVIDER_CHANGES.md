# PSP Provider 字段和配置 URL 修改

## 概述

本次修改为订单系统添加了 PSP Provider 字段，并修改了 success_url 和 cancel_url 的获取方式，从请求参数改为从配置文件中获取。

## 主要修改

### 1. 数据库表结构修改

在 `orders` 表中添加了 `psp_provider` 字段：

```sql
ALTER TABLE orders ADD COLUMN psp_provider VARCHAR(32) NOT NULL;
```

### 2. Domain 层修改

#### Order 实体 (`internal/domain/order.go`)
- 在 `Order` 结构体中添加了 `PSPProvider` 字段
- 在 `CreateOrderRequest` 结构体中添加了 `PSPProvider` 字段和 `Currency` 字段
- 更新了 `NewOrder` 函数以设置 `PSPProvider` 字段

```go
type Order struct {
    // ... 其他字段
    PSPProvider       string     `json:"psp_provider"`
    // ... 其他字段
}

type CreateOrderRequest struct {
    // ... 其他字段
    Currency    string `json:"currency" binding:"required"`
    PSPProvider string `json:"psp_provider" binding:"required"`
    // ... 其他字段
}
```

### 3. 配置层修改

#### ProviderConfig 结构体 (`internal/config/config.go`)
- 添加了 `SuccessURL` 和 `CancelURL` 字段

```go
type ProviderConfig struct {
    Enabled    bool              `mapstructure:"enabled"`
    APIKey     string            `mapstructure:"api_key"`
    SecretKey  string            `mapstructure:"secret_key"`
    SuccessURL string            `mapstructure:"success_url"`
    CancelURL  string            `mapstructure:"cancel_url"`
    Webhook    WebhookConfig     `mapstructure:"webhook"`
    Settings   map[string]string `mapstructure:"settings"`
}
```

### 4. 数据库模型修改

#### OrderModel (`internal/db/models.go`)
- 添加了 `PSPProvider` 字段
- 更新了 `ToDomain` 和 `FromDomain` 方法

```go
type OrderModel struct {
    // ... 其他字段
    PSPProvider       string     `gorm:"type:varchar(32);not null" json:"psp_provider"`
    // ... 其他字段
}
```

### 5. 服务层修改

#### OrderService (`internal/service/order_service.go`)
- 在 `orderService` 结构体中添加了 `config` 字段
- 更新了 `NewOrderService` 构造函数以接受配置参数
- 修改了 `CreateOrder` 方法，从配置中获取 `SuccessURL` 和 `CancelURL`

```go
type orderService struct {
    orderRepo domain.OrderRepository
    gateways  map[string]payment.PaymentGateway
    config    *config.Config
    logger    logger.Logger
}

func (s *orderService) CreateOrder(userCtx *middleware.UserContext, req *domain.CreateOrderRequest) (*domain.CreateOrderResponse, error) {
    // ... 其他代码
    
    // 从配置中获取 SuccessURL 和 CancelURL
    providerConfig, exists := s.config.Payment.Providers[req.PSPProvider]
    if !exists {
        return fmt.Errorf("payment provider %s not configured", req.PSPProvider)
    }

    checkoutURL, err = gateway.CreateCheckoutSession(order, providerConfig.SuccessURL, providerConfig.CancelURL)
    
    // ... 其他代码
}
```

### 6. 依赖注入修改

#### App 模块 (`internal/app/app.go`)
- 更新了 `ServiceModule` 以将配置传递给 `OrderService`

```go
var ServiceModule = fx.Module("service",
    fx.Provide(
        fx.Annotate(
            func(orderRepo domain.OrderRepository, gateways map[string]payment.PaymentGateway, cfg *config.Config, logger logger.Logger) domain.OrderService {
                return service.NewOrderService(orderRepo, gateways, cfg, logger)
            },
            fx.As(new(domain.OrderService)),
        ),
    ),
)
```

## 配置文件示例

### configs/config.yaml
```yaml
payment:
  providers:
    stripe:
      enabled: true
      api_key: "your_stripe_api_key"
      secret_key: "your_stripe_secret_key"
      success_url: "https://your-domain.com/success"
      cancel_url: "https://your-domain.com/cancel"
      webhook:
        url: "https://your-domain.com/api/v1/pay-service/webhooks/stripe"
        secret: "your_stripe_webhook_secret"
      settings:
        environment: "test"

    paypal:
      enabled: true
      api_key: "your_paypal_api_key"
      secret_key: "your_paypal_secret_key"
      success_url: "https://your-domain.com/success"
      cancel_url: "https://your-domain.com/cancel"
      webhook:
        url: "https://your-domain.com/api/v1/pay-service/webhooks/paypal"
        secret: "your_paypal_webhook_secret"
      settings:
        environment: "sandbox"
```

## API 使用示例

### 创建订单请求
```bash
curl -X POST http://localhost:8080/api/v1/order-service/orders \
  -H "Content-Type: application/json" \
  -H "X-User-ID: user123" \
  -H "X-Role: customer" \
  -d '{
    "product_id": "prod_123",
    "product_desc": "Premium Subscription",
    "price_id": "price_456",
    "quantity": 1,
    "currency": "USD",
    "payed_method": "stripe",
    "psp_provider": "stripe",
    "description": "Monthly premium subscription"
  }'
```

注意：
- 移除了 `success_url` 和 `cancel_url` 字段，这些现在从配置中获取
- 添加了 `psp_provider` 字段，用于指定使用的支付提供商
- 添加了 `currency` 字段，用于指定货币类型

## 向后兼容性

这些修改不向后兼容，因为：
1. 数据库表结构发生了变化（添加了新的必需字段）
2. API 请求格式发生了变化（移除了 URL 字段，添加了新的必需字段）

在部署前需要：
1. 更新数据库表结构
2. 更新客户端代码以使用新的 API 格式
3. 确保配置文件包含所有必需的 URL 配置

## 测试

所有相关的测试已经更新以反映这些更改：
- `internal/service/order_service_test.go` 已更新以使用新的构造函数签名
- 编译和测试都通过

## 部署注意事项

1. 确保配置文件中包含所有支付提供商的 `success_url` 和 `cancel_url` 配置
2. 更新客户端代码以在创建订单时提供 `psp_provider` 和 `currency` 字段
3. 如果需要，可以通过数据库迁移脚本添加 `psp_provider` 字段，或者在开发环境中重新创建表
