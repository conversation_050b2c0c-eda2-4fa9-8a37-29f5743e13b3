package config

import (
	"os"
	"strings"
	"testing"

	"github.com/spf13/viper"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestParseNacosEndpoints(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected []string
	}{
		{
			name:     "empty string",
			input:    "",
			expected: []string{"127.0.0.1:8848"},
		},
		{
			name:     "single endpoint",
			input:    "*************:8848",
			expected: []string{"*************:8848"},
		},
		{
			name:     "multiple endpoints",
			input:    "*************:8848,*************:8848,*************:8848",
			expected: []string{"*************:8848", "*************:8848", "*************:8848"},
		},
		{
			name:     "endpoints with spaces",
			input:    " *************:8848 , *************:8848 ",
			expected: []string{"*************:8848", "*************:8848"},
		},
		{
			name:     "endpoints with empty parts",
			input:    "*************:8848,,*************:8848,",
			expected: []string{"*************:8848", "*************:8848"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ParseNacosEndpoints(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestNewNacosClient_Disabled(t *testing.T) {
	config := NacosConfig{
		Enabled: false,
	}

	client, err := NewNacosClient(config)
	require.NoError(t, err)
	assert.Nil(t, client)
}

func TestNewNacosClient_InvalidEndpoint(t *testing.T) {
	config := NacosConfig{
		Enabled:   true,
		Endpoints: []string{"invalid-endpoint"},
		Namespace: "test",
		Username:  "nacos",
		Password:  "nacos",
		Config: NacosConfigCenter{
			Enabled: true,
			DataID:  "test.yaml",
			Group:   "DEFAULT_GROUP",
			Timeout: 30,
		},
	}

	client, err := NewNacosClient(config)
	assert.Error(t, err)
	assert.Nil(t, client)
	assert.Contains(t, err.Error(), "invalid nacos endpoint format")
}

func TestNewNacosClient_InvalidPort(t *testing.T) {
	config := NacosConfig{
		Enabled:   true,
		Endpoints: []string{"127.0.0.1:invalid"},
		Namespace: "test",
		Username:  "nacos",
		Password:  "nacos",
		Config: NacosConfigCenter{
			Enabled: true,
			DataID:  "test.yaml",
			Group:   "DEFAULT_GROUP",
			Timeout: 30,
		},
	}

	client, err := NewNacosClient(config)
	assert.Error(t, err)
	assert.Nil(t, client)
	assert.Contains(t, err.Error(), "invalid port")
}

func TestLoadConfigFromNacos_Disabled(t *testing.T) {
	// 清理环境变量
	clearPaymentEnvVars()

	config := NacosConfig{
		Enabled: false,
	}

	// 创建一个新的viper实例
	v := createTestViper()

	err := LoadConfigFromNacos(v, config)
	require.NoError(t, err)
}

func TestNacosConfig_EnvironmentVariables(t *testing.T) {
	// 清理环境变量
	clearPaymentEnvVars()

	// 设置Nacos相关环境变量
	os.Setenv("PAYMENT_NACOS_ENABLED", "true")
	os.Setenv("PAYMENT_NACOS_ENDPOINTS", "*************:8848,*************:8848")
	os.Setenv("PAYMENT_NACOS_NAMESPACE", "test-namespace")
	os.Setenv("PAYMENT_NACOS_USERNAME", "test-user")
	os.Setenv("PAYMENT_NACOS_PASSWORD", "test-password")
	os.Setenv("PAYMENT_NACOS_CONFIG_ENABLED", "true")
	os.Setenv("PAYMENT_NACOS_CONFIG_DATA_ID", "test-config.yaml")
	os.Setenv("PAYMENT_NACOS_CONFIG_GROUP", "TEST_GROUP")
	os.Setenv("PAYMENT_NACOS_CONFIG_TIMEOUT", "60")

	defer func() {
		os.Unsetenv("PAYMENT_NACOS_ENABLED")
		os.Unsetenv("PAYMENT_NACOS_ENDPOINTS")
		os.Unsetenv("PAYMENT_NACOS_NAMESPACE")
		os.Unsetenv("PAYMENT_NACOS_USERNAME")
		os.Unsetenv("PAYMENT_NACOS_PASSWORD")
		os.Unsetenv("PAYMENT_NACOS_CONFIG_ENABLED")
		os.Unsetenv("PAYMENT_NACOS_CONFIG_DATA_ID")
		os.Unsetenv("PAYMENT_NACOS_CONFIG_GROUP")
		os.Unsetenv("PAYMENT_NACOS_CONFIG_TIMEOUT")
	}()

	// 加载配置
	cfg, err := LoadConfig("nonexistent.yaml")
	require.NoError(t, err)

	// 验证Nacos配置
	assert.True(t, cfg.Nacos.Enabled)
	assert.Equal(t, []string{"*************:8848", "*************:8848"}, cfg.Nacos.Endpoints)
	assert.Equal(t, "test-namespace", cfg.Nacos.Namespace)
	assert.Equal(t, "test-user", cfg.Nacos.Username)
	assert.Equal(t, "test-password", cfg.Nacos.Password)
	assert.True(t, cfg.Nacos.Config.Enabled)
	assert.Equal(t, "test-config.yaml", cfg.Nacos.Config.DataID)
	assert.Equal(t, "TEST_GROUP", cfg.Nacos.Config.Group)
	assert.Equal(t, 60, cfg.Nacos.Config.Timeout)
}

func TestNacosConfig_DefaultValues(t *testing.T) {
	// 清理环境变量
	clearPaymentEnvVars()

	// 加载配置（使用默认值）
	cfg, err := LoadConfig("nonexistent.yaml")
	require.NoError(t, err)

	// 验证Nacos默认配置
	assert.False(t, cfg.Nacos.Enabled)
	assert.Equal(t, []string{"127.0.0.1:8848"}, cfg.Nacos.Endpoints)
	assert.Equal(t, "payment-service", cfg.Nacos.Namespace)
	assert.Equal(t, "nacos", cfg.Nacos.Username)
	assert.Equal(t, "nacos", cfg.Nacos.Password)
	assert.False(t, cfg.Nacos.Config.Enabled)
	assert.Equal(t, "payment-backend.yaml", cfg.Nacos.Config.DataID)
	assert.Equal(t, "DEFAULT_GROUP", cfg.Nacos.Config.Group)
	assert.Equal(t, 30, cfg.Nacos.Config.Timeout)
}

func TestStartConfigWatch_Disabled(t *testing.T) {
	config := NacosConfig{
		Enabled: false,
	}

	client, err := StartConfigWatch(config, nil)
	require.NoError(t, err)
	assert.Nil(t, client)
}

// 辅助函数：创建测试用的viper实例
func createTestViper() *viper.Viper {
	v := viper.New()
	v.SetEnvPrefix("PAYMENT")
	v.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
	v.AutomaticEnv()
	setDefaults(v)
	return v
}
