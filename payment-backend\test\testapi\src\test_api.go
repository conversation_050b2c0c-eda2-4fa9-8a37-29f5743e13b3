package main

import (
	"fmt"
	"io"
	"log"
	"os"
	"strings"
	"time"
)

var (
	// 详细日志记录器，只输出到文件
	detailLogger *log.Logger
	// 汇总日志记录器，同时输出到控制台和文件
	summaryLogger *log.Logger
)

const (
	// dev1 服务器 内网 gin HTTP 地址 (注意，在 k8s 上需映射)
	dev1_local_gin_url_base = "http://192.168.1.200:15445"
	// dev2 服务器 内网 gin HTTP 地址 (注意，在 k8s 上需映射)
	dev2_local_gin_url_base = "http://192.168.1.200:25906"
	// sit 服务器 内网 gin HTTP 地址
	sit_local_gin_url_base = "http://192.168.1.200:30000" // 端口号待定

	// dev1 服务器 内网 RPC 地址 (注意，在 k8s 上需映射)
	dev1_local_rpc_url_base = "http://192.168.1.200:15446"
	// dev2 服务器 内网 RPC 地址 (注意，在 k8s 上需映射)
	dev2_local_rpc_url_base = "http://192.168.1.200:25907"
	// sit 服务器 内网 RPC 地址(注意，在 k8s 上需映射)
	sit_local_rpc_url_base = "http://192.168.1.200:30001" // 端口号待定

	// dev1 服务器 外网地址
	dev1_external_url_base = "http://ny10wt9045294.vicp.fun:25639"
	// dev2 服务器 外网地址
	dev2_external_url_base = "http://ny10wt9045294.vicp.fun"
	// sit 服务器 外网地址
	sit_external_url_base = "http://ny10wt9045294.vicp.fun:29397"
)

func main() {
	// 创建日志文件
	logFile, err := os.OpenFile("test.log", os.O_CREATE|os.O_RDWR|os.O_TRUNC, 0666)
	if err != nil {
		log.Fatalf("Failed to create log file: %v", err)
	}
	defer logFile.Close()

	// 设置详细日志输出到文件
	detailLogger = log.New(logFile, "", log.LstdFlags)

	// 设置汇总日志同时输出到控制台和文件
	summaryLogger = log.New(io.MultiWriter(os.Stdout, logFile), "", 0)

	run_env := "all"
	if len(os.Args) > 1 {
		run_env = os.Args[1]
	}
	if run_env == "all" {
		run_env = "dev1,dev2,sit"
	}

	run_envs := make([]string, 0)
	for _, env := range strings.Split(run_env, ",") {
		run_envs = append(run_envs, strings.TrimSpace(env))
	}

	// 记录测试开始时间到日志文件
	detailLogger.Printf("=== Test Session Started at %v ===", time.Now().Format("2006-01-02 15:04:05"))

	testResults := make([][]TestResult, 0)
	testedRunEnvs := make([]string, 0)
	for _, run_env := range run_envs {
		detailLogger.Printf("+++++++++++++++ Starting test for %v +++++++++++++++", run_env)
		fmt.Printf("+++++++++++++++ Starting test for %v +++++++++++++++\n", run_env)
		testResult := StartTest(run_env)
		if testResult != nil {
			testResults = append(testResults, testResult)
			testedRunEnvs = append(testedRunEnvs, run_env)
		}
		detailLogger.Printf("--------------- Finished test for %v ---------------", run_env)
		fmt.Printf("--------------- Finished test for %v ---------------\n\n", run_env)
	}

	// 记录汇总开始到日志文件
	detailLogger.Printf("=== Starting Test Summary ===")

	summaryLogger.Printf("\n\n##################### EACH TEST SUMMARY #####################")
	for i, testResult := range testResults {
		printTestSummary(testedRunEnvs[i], testResult)
		summaryLogger.Println()
	}

	// 记录测试结束时间到日志文件
	detailLogger.Printf("=== Test Session Ended at %v ===", time.Now().Format("2006-01-02 15:04:05"))
}

func StartTest(run_env string) []TestResult {
	config := getEnvConfig(run_env)
	if config == nil {
		msg := fmt.Sprintf("Unknown environment: %s", run_env)
		fmt.Printf("%s\n", msg)
		detailLogger.Printf("%s", msg)
		return nil
	}

	// 详细信息记录到日志文件
	detailLogger.Printf("Testing environment: %s", config.Name)
	detailLogger.Printf("External URL: %s", config.ExternalURL)
	detailLogger.Printf("Local Gin URL: %s", config.LocalGinURL)
	detailLogger.Printf("Local RPC URL: %s", config.LocalRpcURL)
	detailLogger.Printf("")

	// 简要信息输出到控制台
	fmt.Printf("Testing environment: %s\n", config.Name)
	fmt.Printf("External URL: %s\n", config.ExternalURL)
	fmt.Printf("Local Gin URL: %s\n", config.LocalGinURL)
	fmt.Printf("Local RPC URL: %s\n", config.LocalRpcURL)
	fmt.Println()

	var results []TestResult

	// 获取外网接口的token
	detailLogger.Printf("=== Getting External API Token ===")
	fmt.Println("=== Getting External API Token ===")
	token, err := getExternalToken(config)
	if err != nil {
		msg := fmt.Sprintf("Failed to get external token: %v", err)
		fmt.Printf("%s\n", msg)
		detailLogger.Printf("%s", msg)
		results = append(results, TestResult{
			TestName: "Get External Token",
			Success:  false,
			Message:  fmt.Sprintf("Failed to get token: %v", err),
			Duration: 0,
		})
	} else {
		msg := fmt.Sprintf("Token obtained successfully: %s...", token[:50])
		fmt.Printf("%s\n", msg)
		detailLogger.Printf("Token obtained successfully: %s", token)
		results = append(results, TestResult{
			TestName: "Get External Token",
			Success:  true,
			Message:  "Token obtained successfully",
			Duration: 0,
		})
	}

	// 测试外网接口 (只测试 gin HTTP)
	detailLogger.Printf("=== Testing External APIs (Gin HTTP only) ===")
	fmt.Println("\n=== Testing External APIs (Gin HTTP only) ===")
	results = append(results, testCreateOrderExternal(config, token))
	results = append(results, testGetUserOrdersExternal(config, token))
	results = append(results, testListAllPackagesExternal(config, token))
	results = append(results, testListAllPackagesExternalWithFilters(config, token))

	// 测试内网接口 (测试 gin HTTP 和 dubbo RPC)
	detailLogger.Printf("=== Testing Internal APIs (Gin HTTP + Dubbo RPC) ===")
	fmt.Println("\n=== Testing Internal APIs (Gin HTTP + Dubbo RPC) ===")

	// 订单相关接口测试
	results = append(results, testListAllOrdersGin(config))
	results = append(results, testListAllOrdersRPC(config))
	results = append(results, testForceRefundGin(config))

	// 流量包管理接口测试 (Gin HTTP)
	results = append(results, testAdminAddPackagesGin(config))
	results = append(results, testAdminListAllPackagesGin(config))
	results = append(results, testAdminUpdatePackagesGin(config))
	results = append(results, testAdminDeletePackagesGin(config))

	// 流量包管理接口测试 (Dubbo RPC)
	results = append(results, testAdminAddPackagesRPC(config))
	results = append(results, testAdminListAllPackagesRPC(config))
	results = append(results, testAdminUpdatePackagesRPC(config))
	results = append(results, testAdminDeletePackagesRPC(config))

	// 不在这里打印测试结果汇总，因为会在main函数中统一打印
	// printTestSummary(run_env, results)

	return results
}
