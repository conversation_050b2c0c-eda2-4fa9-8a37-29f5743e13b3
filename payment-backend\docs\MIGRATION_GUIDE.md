# 数据库重构迁移指南

## 概述

本次重构将原有的多表设计（payments、checkout_sessions、checkout_session_items）简化为单表设计（orders），统一管理订单和支付信息。

## 重构内容

### 1. 数据库表结构变更

#### 旧表结构
- `payments` - 支付记录表
- `checkout_sessions` - 结账会话表  
- `checkout_session_items` - 会话项目表

#### 新表结构
- `orders` - 订单表（包含所有订单和支付信息）

### 2. 字段映射关系

| 旧字段 (payments) | 新字段 (orders) | 说明 |
|------------------|----------------|------|
| id (UUID) | id (BIGINT) | 主键类型变更 |
| session_id | order_id | 订单唯一标识 |
| customer_id | user_id | 用户ID |
| amount | amount | 金额（改为DECIMAL类型） |
| currency | currency | 货币（改为CHAR(3)） |
| status | pay_status | 支付状态（枚举改为字符串） |
| provider | payed_method | 支付方式 |
| provider_payment_id | psp_payment_id | PSP支付ID |
| description | product_desc | 产品描述 |
| created_at | created_at | 创建时间 |
| updated_at | updated_at | 更新时间 |
| completed_at | payed_at | 支付完成时间 |

### 3. 新增字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| product_id | VARCHAR(64) | 产品ID |
| price_id | VARCHAR(64) | 价格ID |
| quantity | INT UNSIGNED | 购买数量 |
| net_amount | DECIMAL(18,2) | 净额 |
| card_number | VARCHAR(32) | 支付卡尾号 |
| refund_status | VARCHAR(20) | 退款状态 |
| refunded_at | DATETIME | 退款时间 |
| psp_product_id | VARCHAR(64) | PSP产品ID |
| psp_product_desc | TEXT | PSP产品描述 |
| psp_price_id | VARCHAR(64) | PSP价格ID |
| psp_customer_id | VARCHAR(128) | PSP客户ID |
| psp_customer_email | VARCHAR(128) | PSP客户邮箱 |
| psp_subscription_id | VARCHAR(128) | PSP订阅ID |
| deleted | TINYINT(1) | 软删除标记 |
| deleted_at | DATETIME | 软删除时间 |

## 代码结构变更

### 1. Domain层
- 删除：`Payment`、`CheckoutSession`、`CheckoutSessionItem`
- 新增：`Order` 实体，包含完整的订单和支付信息

### 2. Repository层
- 删除：`PaymentRepository`、`CheckoutSessionRepository`
- 新增：`OrderRepository` 统一管理订单数据

### 3. Service层
- 删除：`PaymentService`
- 新增：`OrderService` 处理订单业务逻辑

### 4. Handler层
- 删除：`PaymentHandler`
- 新增：`OrderHandler` 处理订单API请求

### 5. API接口变更

#### 旧接口
```
POST /api/v1/pay-service/checkout/session
GET  /api/v1/pay-service/payments/:id
GET  /api/v1/pay-service/sessions/:session_id/payment
POST /api/v1/pay-service/webhooks/:provider
```

#### 新接口
```
POST /api/v1/order-service/orders
GET  /api/v1/order-service/orders/:order_id
GET  /api/v1/order-service/orders/id/:id
GET  /api/v1/order-service/orders
PUT  /api/v1/order-service/orders/:order_id
POST /api/v1/order-service/orders/:order_id/cancel
POST /api/v1/order-service/orders/:order_id/refund
POST /api/v1/order-service/webhooks/:provider
```

## 订单ID生成规则

新的订单ID生成规则：`订单创建时间 + 金额 + 支付方式 + 雪花算法ID`

示例：`20250710153045999stripe1234567890123456789`

## 数据库索引

为了优化查询性能，创建了以下索引：

```sql
-- 基础索引
CREATE UNIQUE INDEX idx_orders_order_id ON orders(order_id);
CREATE INDEX idx_orders_user_id ON orders(user_id);
CREATE INDEX idx_orders_pay_status ON orders(pay_status);
CREATE INDEX idx_orders_psp_payment_id ON orders(psp_payment_id);
CREATE INDEX idx_orders_psp_customer_id ON orders(psp_customer_id);
CREATE INDEX idx_orders_created_at ON orders(created_at DESC);

-- 复合索引
CREATE INDEX idx_orders_user_pay_status ON orders(user_id, pay_status);
CREATE INDEX idx_orders_user_created ON orders(user_id, created_at DESC);
CREATE INDEX idx_orders_status_created ON orders(pay_status, created_at DESC);

-- 其他索引
CREATE INDEX idx_orders_deleted ON orders(deleted);
CREATE INDEX idx_orders_product_id ON orders(product_id);
CREATE INDEX idx_orders_price_id ON orders(price_id);
```

## 迁移步骤

### 1. 备份现有数据
```bash
mysqldump -u username -p database_name > backup_$(date +%Y%m%d_%H%M%S).sql
```

### 2. 运行数据库迁移
```bash
go run scripts/migrate.go -action=migrate
```

### 3. 验证迁移结果
```bash
go run scripts/migrate.go -action=info
```

### 4. 数据迁移（如果有现有数据）
如果有现有的payments和checkout_sessions数据，需要编写数据迁移脚本将数据转换到新的orders表中。

## 配置变更

### 数据库配置
确保配置文件中的数据库配置正确：

```yaml
database:
  driver: "mysql"
  host: "localhost"
  port: 3306
  username: "payment_user"
  password: "payment_password"
  database: "payment_db"
  ssl_mode: "disable"
```

### 环境变量
支持通过环境变量配置：
```bash
export PAYMENT_DATABASE_HOST=localhost
export PAYMENT_DATABASE_PORT=3306
export PAYMENT_DATABASE_USERNAME=payment_user
export PAYMENT_DATABASE_PASSWORD=payment_password
export PAYMENT_DATABASE_DATABASE=payment_db
```

## 测试

### 1. 单元测试
```bash
go test ./internal/repository/mysql/...
go test ./internal/service/...
go test ./internal/handler/...
```

### 2. 集成测试
```bash
go test ./internal/handler/...
```

### 3. API测试
使用Postman或curl测试新的API接口。

## 注意事项

1. **数据类型变更**：金额字段从int64（分）改为decimal(18,2)（元），需要注意数据转换
2. **主键类型**：从UUID改为自增BIGINT，可能影响现有的关联关系
3. **枚举类型**：从Go枚举改为字符串类型，提高了灵活性但需要注意数据一致性
4. **软删除**：新增了软删除功能，删除操作不会物理删除数据
5. **索引优化**：根据查询模式优化了索引设计，提高查询性能

## 回滚计划

如果迁移出现问题，可以：

1. 恢复数据库备份
2. 回滚到旧版本代码
3. 重新部署旧版本服务

## 性能优化建议

1. 定期清理软删除的数据
2. 监控数据库查询性能
3. 根据实际使用情况调整索引
4. 考虑数据分区策略（当数据量很大时）
