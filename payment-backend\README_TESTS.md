# Payment Backend HTTP 接口单元测试

## 概述

本项目为 payment-backend 的 HTTP 接口提供了全面的单元测试和集成测试套件。测试覆盖率达到 **94.8%**，确保了代码质量和系统稳定性。

## 快速开始

### 使用测试脚本（推荐）

**Windows:**
```bash
# 运行所有测试
scripts\run_tests.bat

# 运行单元测试
scripts\run_tests.bat unit

# 运行集成测试
scripts\run_tests.bat integration

# 生成覆盖率报告
scripts\run_tests.bat coverage

# 查看帮助
scripts\run_tests.bat help
```

**Linux/macOS:**
```bash
# 运行所有测试
scripts/run_tests.sh

# 运行单元测试
scripts/run_tests.sh unit

# 生成覆盖率报告
scripts/run_tests.sh coverage
```

### 手动运行测试

```bash
# 运行所有测试
go test ./internal/handler/... -v

# 运行单元测试
go test ./internal/handler -v -run "^TestPaymentHandler_"

# 运行集成测试
go test ./internal/handler -v -run "TestIntegrationSuite"

# 生成覆盖率报告
go test ./internal/handler -cover
go test ./internal/handler -coverprofile=coverage.out
go tool cover -html coverage.out -o coverage.html
```

## 测试架构

### 文件结构
```
internal/handler/
├── payment_handler_test.go          # 单元测试
├── integration_test.go              # 集成测试
└── testutil/
    ├── test_helpers.go              # 测试工具函数
    └── test_data.go                 # 测试数据构造器
```

### 测试工具
- **测试框架**: Go 标准库 `testing` + `testify`
- **HTTP 测试**: `net/http/httptest` + `gin` 测试模式
- **模拟对象**: `testify/mock`
- **断言库**: `testify/assert`

## 测试覆盖的接口

### 1. CreateCheckoutSession
- **路径**: `POST /api/v1/pay-service/checkout/session`
- **测试场景**: 成功创建、无效请求体、缺少字段、服务错误

### 2. GetPayment
- **路径**: `GET /api/v1/pay-service/payments/{id}`
- **测试场景**: 成功获取、无效ID格式、支付不存在

### 3. GetPaymentBySessionID
- **路径**: `GET /api/v1/pay-service/sessions/{session_id}/payment`
- **测试场景**: 成功获取、空会话ID、支付不存在

### 4. ProcessWebhook
- **路径**: `POST /api/v1/pay-service/webhooks/{provider}`
- **测试场景**: 成功处理、缺少签名、处理失败

### 5. HealthCheck
- **路径**: `GET /health`
- **测试场景**: 健康检查正常响应

## 测试类型

### 单元测试
- 使用模拟对象隔离依赖
- 测试各种边界条件和错误情况
- 验证HTTP状态码和响应格式
- 确保错误处理的正确性

### 集成测试
- 使用真实的内存仓储
- 测试完整的请求-响应流程
- 验证数据持久化
- 测试端到端的业务逻辑

## 测试覆盖率

**当前覆盖率: 94.8%**

### 覆盖率详情
```
payment_handler.go:20:    NewPaymentHandler       100.0%
payment_handler.go:38:    CreateCheckoutSession   100.0%
payment_handler.go:85:    GetPayment              100.0%
payment_handler.go:123:   GetPaymentBySessionID   100.0%
payment_handler.go:160:   ProcessWebhook          83.3%
payment_handler.go:208:   HealthCheck             100.0%
```

### 未覆盖的代码
主要是 ProcessWebhook 中的一些边缘情况，如读取请求体失败等难以在测试中模拟的情况。

## 测试工具和辅助函数

### MockPaymentService
模拟支付服务，支持所有接口方法的模拟。

### MockLogger
模拟日志记录器，支持所有日志级别的记录和验证。

### HTTPTestHelper
HTTP 测试辅助工具，提供：
- JSON 请求创建
- 带头部的请求创建
- 请求执行
- 响应断言

### TestDataBuilder
测试数据构造器，提供流式API构建测试数据：
- `CreateCheckoutSessionRequestBuilder`
- `PaymentBuilder`
- `CheckoutSessionResponseBuilder`

## 示例测试用例

### 单元测试示例
```go
func TestPaymentHandler_CreateCheckoutSession(t *testing.T) {
    testData := testutil.GetTestData()
    helper := testutil.NewHTTPTestHelper(t)
    
    // 设置模拟对象
    mockService := &testutil.MockPaymentService{}
    mockLogger := &testutil.MockLogger{}
    
    // 配置期望行为
    response := testData.CreateValidCheckoutSessionResponse()
    mockService.On("CreateCheckoutSession", mock.AnythingOfType("*domain.CreateCheckoutSessionRequest")).Return(response, nil)
    
    // 创建处理器和路由
    handler := NewPaymentHandler(mockService, mockLogger)
    router := gin.New()
    router.POST("/checkout/session", handler.CreateCheckoutSession)
    
    // 执行测试
    req := helper.CreateJSONRequest("POST", "/checkout/session", testData.CreateValidCheckoutSessionRequest())
    w := helper.PerformRequest(router, req)
    
    // 验证结果
    assert.Equal(t, http.StatusOK, w.Code)
    mockService.AssertExpectations(t)
}
```

### 集成测试示例
```go
func (suite *IntegrationTestSuite) TestCreateCheckoutSessionIntegration() {
    req := suite.testData.NewCreateCheckoutSessionRequestBuilder().
        WithCustomerID("customer_123").
        WithAmount(10000).
        Build()
    
    httpReq := suite.helper.CreateJSONRequest("POST", "/api/v1/pay-service/checkout/session", req)
    w := suite.helper.PerformRequest(suite.router, httpReq)
    
    assert.Equal(suite.T(), http.StatusOK, w.Code)
    
    var response domain.CreateCheckoutSessionResponse
    err := json.Unmarshal(w.Body.Bytes(), &response)
    assert.NoError(suite.T(), err)
    assert.NotEmpty(suite.T(), response.SessionID)
}
```

## 持续集成

### GitHub Actions 示例
```yaml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - uses: actions/setup-go@v2
      with:
        go-version: 1.21
    - name: Run tests
      run: |
        cd payment-backend
        go test ./internal/handler/... -v -cover
```

## 最佳实践

### 1. 测试命名
- 使用描述性的测试名称
- 清楚表达测试场景和预期结果

### 2. 测试结构
- 使用表驱动测试模式
- 分离测试数据和测试逻辑
- 每个测试用例独立且可重复

### 3. 模拟对象
- 使用精确的模拟验证
- 验证调用次数和参数
- 清理模拟对象状态

### 4. 断言
- 使用具体的断言
- 验证完整的响应结构
- 提供清晰的错误消息

## 故障排除

### 常见问题

**1. 测试失败: "模拟对象期望不匹配"**
- 检查模拟对象的设置是否正确
- 确保调用参数类型匹配
- 验证调用次数

**2. 覆盖率报告生成失败**
- 确保有写入权限
- 检查Go工具链是否完整

**3. 集成测试失败**
- 检查依赖服务是否正确初始化
- 验证测试数据的一致性

### 调试技巧
- 使用 `-v` 标志查看详细输出
- 使用 `t.Log()` 添加调试信息
- 检查模拟对象的调用历史

## 贡献指南

1. 新增接口时必须添加对应的单元测试
2. 修改现有接口时必须更新相关测试
3. 保持测试覆盖率在90%以上
4. 遵循现有的测试模式和命名约定
5. 添加必要的文档和注释

## 总结

本测试套件为 payment-backend 的 HTTP 接口提供了全面的测试覆盖，通过单元测试和集成测试的结合，确保了：

- 快速发现回归问题
- 接口行为的正确性
- 代码重构的信心
- 新功能开发的安全保障

测试代码本身也遵循了良好的设计原则，具有良好的可维护性和可扩展性。
