//*
//后台管理平台基础服务定义

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.31.1
// source: protos/area.proto

package admin

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Empty struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Empty) Reset() {
	*x = Empty{}
	mi := &file_protos_area_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Empty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Empty) ProtoMessage() {}

func (x *Empty) ProtoReflect() protoreflect.Message {
	mi := &file_protos_area_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Empty.ProtoReflect.Descriptor instead.
func (*Empty) Descriptor() ([]byte, []int) {
	return file_protos_area_proto_rawDescGZIP(), []int{0}
}

type Country struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Code          string                 `protobuf:"bytes,2,opt,name=code,proto3" json:"code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Country) Reset() {
	*x = Country{}
	mi := &file_protos_area_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Country) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Country) ProtoMessage() {}

func (x *Country) ProtoReflect() protoreflect.Message {
	mi := &file_protos_area_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Country.ProtoReflect.Descriptor instead.
func (*Country) Descriptor() ([]byte, []int) {
	return file_protos_area_proto_rawDescGZIP(), []int{1}
}

func (x *Country) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Country) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

type Language struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Code          string                 `protobuf:"bytes,2,opt,name=code,proto3" json:"code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Language) Reset() {
	*x = Language{}
	mi := &file_protos_area_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Language) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Language) ProtoMessage() {}

func (x *Language) ProtoReflect() protoreflect.Message {
	mi := &file_protos_area_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Language.ProtoReflect.Descriptor instead.
func (*Language) Descriptor() ([]byte, []int) {
	return file_protos_area_proto_rawDescGZIP(), []int{2}
}

func (x *Language) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Language) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

type AreaResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Countries     []*Country             `protobuf:"bytes,1,rep,name=countries,proto3" json:"countries,omitempty"`
	Languages     []*Language            `protobuf:"bytes,2,rep,name=languages,proto3" json:"languages,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AreaResponse) Reset() {
	*x = AreaResponse{}
	mi := &file_protos_area_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AreaResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AreaResponse) ProtoMessage() {}

func (x *AreaResponse) ProtoReflect() protoreflect.Message {
	mi := &file_protos_area_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AreaResponse.ProtoReflect.Descriptor instead.
func (*AreaResponse) Descriptor() ([]byte, []int) {
	return file_protos_area_proto_rawDescGZIP(), []int{3}
}

func (x *AreaResponse) GetCountries() []*Country {
	if x != nil {
		return x.Countries
	}
	return nil
}

func (x *AreaResponse) GetLanguages() []*Language {
	if x != nil {
		return x.Languages
	}
	return nil
}

var File_protos_area_proto protoreflect.FileDescriptor

const file_protos_area_proto_rawDesc = "" +
	"\n" +
	"\x11protos/area.proto\x12\x15com.aibook.admin.grpc\x1a\x1cgoogle/api/annotations.proto\"\a\n" +
	"\x05Empty\"1\n" +
	"\aCountry\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x12\n" +
	"\x04code\x18\x02 \x01(\tR\x04code\"2\n" +
	"\bLanguage\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x12\n" +
	"\x04code\x18\x02 \x01(\tR\x04code\"\x8b\x01\n" +
	"\fAreaResponse\x12<\n" +
	"\tcountries\x18\x01 \x03(\v2\x1e.com.aibook.admin.grpc.CountryR\tcountries\x12=\n" +
	"\tlanguages\x18\x02 \x03(\v2\x1f.com.aibook.admin.grpc.LanguageR\tlanguages2x\n" +
	"\vAreaService\x12i\n" +
	"\bgetAreas\x12\x1c.com.aibook.admin.grpc.Empty\x1a#.com.aibook.admin.grpc.AreaResponse\"\x1a\x82\xd3\xe4\x93\x02\x14\x12\x12/api/v1/admin/areaB\x11P\x01Z\r./admin;adminb\x06proto3"

var (
	file_protos_area_proto_rawDescOnce sync.Once
	file_protos_area_proto_rawDescData []byte
)

func file_protos_area_proto_rawDescGZIP() []byte {
	file_protos_area_proto_rawDescOnce.Do(func() {
		file_protos_area_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_protos_area_proto_rawDesc), len(file_protos_area_proto_rawDesc)))
	})
	return file_protos_area_proto_rawDescData
}

var file_protos_area_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_protos_area_proto_goTypes = []any{
	(*Empty)(nil),        // 0: com.aibook.admin.grpc.Empty
	(*Country)(nil),      // 1: com.aibook.admin.grpc.Country
	(*Language)(nil),     // 2: com.aibook.admin.grpc.Language
	(*AreaResponse)(nil), // 3: com.aibook.admin.grpc.AreaResponse
}
var file_protos_area_proto_depIdxs = []int32{
	1, // 0: com.aibook.admin.grpc.AreaResponse.countries:type_name -> com.aibook.admin.grpc.Country
	2, // 1: com.aibook.admin.grpc.AreaResponse.languages:type_name -> com.aibook.admin.grpc.Language
	0, // 2: com.aibook.admin.grpc.AreaService.getAreas:input_type -> com.aibook.admin.grpc.Empty
	3, // 3: com.aibook.admin.grpc.AreaService.getAreas:output_type -> com.aibook.admin.grpc.AreaResponse
	3, // [3:4] is the sub-list for method output_type
	2, // [2:3] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_protos_area_proto_init() }
func file_protos_area_proto_init() {
	if File_protos_area_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_protos_area_proto_rawDesc), len(file_protos_area_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_protos_area_proto_goTypes,
		DependencyIndexes: file_protos_area_proto_depIdxs,
		MessageInfos:      file_protos_area_proto_msgTypes,
	}.Build()
	File_protos_area_proto = out.File
	file_protos_area_proto_goTypes = nil
	file_protos_area_proto_depIdxs = nil
}
