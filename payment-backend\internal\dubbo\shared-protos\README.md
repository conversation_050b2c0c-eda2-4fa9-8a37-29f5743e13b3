# 介绍

此仓库用来定义协议原型, 生成的文档存放到 gen 子目录下，在其它引用模块就不需要再执行生成命令, 直接引用代码即可。

## 依赖工具安装

- 更新 googleapis 仓库： git submodule update --remote --recursive
- 安装 protoc ： https://github.com/protocolbuffers/protobuf/releases
- 插件安装：
  - `go install github.com/dubbogo/protoc-gen-go-triple/v3@v3.0.3`
  - `go install google.golang.org/protobuf/cmd/protoc-gen-go@latest`
  - `go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@latest`
  - `go install github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-grpc-gateway@latest`
  - `go install github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2@latest`

## 运行方法

- 接口文档生成命令：

```cmd
./shell/gen-api-docs.bat
```

- 代码生成
  - 用户管理模块生成命令：
    - 进入此项目根目录
    - 执行：./shell/gen-user-proto.bat
  - xx 模块生成命令：
    - 进入此项目根目录
    - 执行：./shell/gen-xx-proto.bat
  - xx 模块生成命令：
    - 进入此项目根目录
    - 执行：./shell/gen-xx-proto.bat

## 引用该模块使用方法

- 在需要引用的模块将此项目作为子模块添加，在项目目录下，执行：

```cmd
  git <NAME_EMAIL>:aibook/platform/shared-protos.git ./shared-protos 
  git submodule update --init 

  git submodule update --remote // 后续更新到最新版本
```

- 生成代码引用
  - go 代码引用： 位置为 shared-protos/gen/go
  - python 代码引用： 位置为 shared-protos/gen/python

- 生成命令
- protoc --go_out=. --go_opt=paths=source_relative --go-triple_out=. --go-triple_opt=paths=source_relative  ./greet.proto
