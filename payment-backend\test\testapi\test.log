2025/07/24 17:01:29 === Test Session Started at 2025-07-24 17:01:29 ===
2025/07/24 17:01:29 +++++++++++++++ Starting test for dev1 +++++++++++++++
2025/07/24 17:01:29 Testing environment: dev1
2025/07/24 17:01:29 External URL: http://ny10wt9045294.vicp.fun:25639
2025/07/24 17:01:29 Local Gin URL: http://*************:15445
2025/07/24 17:01:29 Local RPC URL: http://*************:15446
2025/07/24 17:01:29 
2025/07/24 17:01:29 === Getting External API Token ===
2025/07/24 17:01:44 Token obtained successfully: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************.2AAMTUaKNJe-3n5oNoy3TQ9wAIkulca9b7VZ9Vcg_5Y
2025/07/24 17:01:44 === Testing External APIs (Gin HTTP only) ===
2025/07/24 17:01:44 --- Create Order (External Gin HTTP) ---
2025/07/24 17:01:44 URL: http://ny10wt9045294.vicp.fun:25639/api/v1/pay-service/order-service/orders
2025/07/24 17:01:44 Headers: map[Authorization:Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************.2AAMTUaKNJe-3n5oNoy3TQ9wAIkulca9b7VZ9Vcg_5Y x-trace-id:test-**********622833800-**********]
2025/07/24 17:01:44 Request Body: {ProductID:prod_stripe_001 ProductDesc:Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq PriceID:price_1Rd4qlC53MAl6WmqQ9ORYbPq Quantity:1 PSPProvider:stripe}
2025/07/24 17:01:45 Response Status: 303
2025/07/24 17:01:45 Response Body: {
      "order_id": "202507241701455124STRIPE1948307604744704000",
      "checkout_url": "https://checkout.stripe.com/c/pay/cs_test_a1xvBB8ilLhpYBKrvCcKlATUan3oaSloWiqdWOyUkMg0gKulH3IVkZMAkv#fidkdWxOYHwnPyd1blpxYHZxWjA0V2dhc0ZGMDZIRGkzUmh0a2loPV1zM3NjUWNjfVJHbGB%2FXDFkbWM0cW90QFFzVjJ%2FbDNfRnI0NmRoVmFpMnJUdXxidUZxPUJJQ0lpY0dEQW18f0tBZFJxNTVLV2hsbFxQSScpJ2N3amhWYHdzYHcnP3F3cGApJ2lkfGpwcVF8dWAnPyd2bGtiaWBabHFgaCcpJ2BrZGdpYFVpZGZgbWppYWB3dic%2FcXdwYHgl",
      "amount": 0,
      "currency": "",
      "expires_at": "2025-07-25T17:01:46.080680333+08:00"
    }
2025/07/24 17:01:45 Order Details - ID: 202507241701455124STRIPE1948307604744704000, Amount: 0.00 , Checkout URL: https://checkout.stripe.com/c/pay/cs_test_a1xvBB8ilLhpYBKrvCcKlATUan3oaSloWiqdWOyUkMg0gKulH3IVkZMAkv#fidkdWxOYHwnPyd1blpxYHZxWjA0V2dhc0ZGMDZIRGkzUmh0a2loPV1zM3NjUWNjfVJHbGB%2FXDFkbWM0cW90QFFzVjJ%2FbDNfRnI0NmRoVmFpMnJUdXxidUZxPUJJQ0lpY0dEQW18f0tBZFJxNTVLV2hsbFxQSScpJ2N3amhWYHdzYHcnP3F3cGApJ2lkfGpwcVF8dWAnPyd2bGtiaWBabHFgaCcpJ2BrZGdpYFVpZGZgbWppYWB3dic%2FcXdwYHgl
2025/07/24 17:01:45 --- Get User Orders (External Gin HTTP) ---
2025/07/24 17:01:45 URL: http://ny10wt9045294.vicp.fun:25639/api/v1/pay-service/order-service/orders?limit=50&offset=0
2025/07/24 17:01:45 Headers: map[Authorization:Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************.2AAMTUaKNJe-3n5oNoy3TQ9wAIkulca9b7VZ9Vcg_5Y x-trace-id:test-**********329735000-**********]
2025/07/24 17:01:45 Response Status: 200
2025/07/24 17:01:45 Response Body: {
      "userOrders": [
        {
          "order_id": "202507241701455124STRIPE1948307604744704000",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T17:01:45.434+08:00",
          "refund_status": "none"
        },
        {
          "order_id": "202507241613252394STRIPE1948295440747204608",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T16:13:25.31+08:00",
          "refund_status": "none"
        },
        {
          "order_id": "202507241609585449STRIPE1948294571330899968",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T16:09:58.026+08:00",
          "refund_status": "none"
        },
        {
          "order_id": "202507241608340313STRIPE1948294221534334976",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T16:08:34.628+08:00",
          "refund_status": "none"
        },
        {
          "order_id": "202507241607541704STRIPE1948294051677605888",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T16:07:54.131+08:00",
          "refund_status": "none"
        },
        {
          "order_id": "202507241607344815STRIPE1948293967799914496",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T16:07:34.133+08:00",
          "refund_status": "none"
        },
        {
          "order_id": "202507241605040651STRIPE1948293338633342976",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T16:05:04.127+08:00",
          "refund_status": "none"
        },
        {
          "order_id": "202507241600074644STRIPE1948292092874723328",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T16:00:07.115+08:00",
          "refund_status": "none"
        },
        {
          "order_id": "202507241559230263STRIPE1948291908019163136",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T15:59:23.043+08:00",
          "refund_status": "none"
        },
        {
          "order_id": "202507241546214560STRIPE1948288629734379520",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T15:46:21.438+08:00",
          "refund_status": "none"
        },
        {
          "order_id": "202507241541521181STRIPE1948287503467286528",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T15:41:52.915+08:00",
          "refund_status": "none"
        },
        {
          "order_id": "202507241539578968STRIPE1948287018647687168",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T15:39:57.325+08:00",
          "refund_status": "none"
        },
        {
          "order_id": "202507241538452834STRIPE1948286715730857984",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T15:38:45.105+08:00",
          "refund_status": "none"
        },
        {
          "order_id": "202507241536186240STRIPE1948286100409683968",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T15:36:18.401+08:00",
          "refund_status": "none"
        },
        {
          "order_id": "202507241535040903STRIPE1948285790224125952",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T15:35:04.446+08:00",
          "refund_status": "none"
        },
        {
          "order_id": "202507241530548175STRIPE1948284742067556352",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T15:30:54.547+08:00",
          "refund_status": "none"
        }
      ],
      "pagination": {
        "total": 16,
        "limit": 50,
        "offset": 0,
        "remaining": 0
      }
    }
2025/07/24 17:01:45 --- List All Packages (External Gin HTTP) ---
2025/07/24 17:01:45 URL: http://ny10wt9045294.vicp.fun:25639/api/v1/pay-service/store-service/packages?limit=50&offset=0
2025/07/24 17:01:45 Headers: map[Authorization:Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************.2AAMTUaKNJe-3n5oNoy3TQ9wAIkulca9b7VZ9Vcg_5Y x-trace-id:test-**********423883500-**********]
2025/07/24 17:01:45 Response Status: 200
2025/07/24 17:01:45 Response Body: {
      "packages": [
        {
          "package_id": "08618ca1-50a9-40ef-9a10-09269d6b3431",
          "package_name": "测试流量包",
          "package_desc": "用于API测试的流量包",
          "entitlement": 100,
          "entitlement_desc": "100GB流量",
          "price": 19.99,
          "original_price": null,
          "discount_start_time": null,
          "discount_end_time": null,
          "discount_desc": "",
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "US"
        }
      ],
      "pagination": {
        "total": 1,
        "limit": 50,
        "offset": 0,
        "remaining": 0
      }
    }
2025/07/24 17:01:45 --- List All Packages with Filters (External Gin HTTP) ---
2025/07/24 17:01:45 URL: http://ny10wt9045294.vicp.fun:25639/api/v1/pay-service/store-service/packages?currency=USD&country=US&limit=20&offset=0
2025/07/24 17:01:45 Headers: map[Authorization:Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************.2AAMTUaKNJe-3n5oNoy3TQ9wAIkulca9b7VZ9Vcg_5Y x-trace-id:test-**********492757500-**********]
2025/07/24 17:01:45 Response Status: 200
2025/07/24 17:01:45 Response Body: {
      "packages": [
        {
          "package_id": "08618ca1-50a9-40ef-9a10-09269d6b3431",
          "package_name": "测试流量包",
          "package_desc": "用于API测试的流量包",
          "entitlement": 100,
          "entitlement_desc": "100GB流量",
          "price": 19.99,
          "original_price": null,
          "discount_start_time": null,
          "discount_end_time": null,
          "discount_desc": "",
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "US"
        }
      ],
      "pagination": {
        "total": 1,
        "limit": 20,
        "offset": 0,
        "remaining": 0
      }
    }
2025/07/24 17:01:45 === Testing Internal APIs (Gin HTTP + Dubbo RPC) ===
2025/07/24 17:01:45 --- List All Orders (Internal Gin HTTP) ---
2025/07/24 17:01:45 URL: http://*************:15445/api/v1/pay-service/admin/order-service/orders?limit=50&offset=0
2025/07/24 17:01:45 Headers: map[x-role:admin x-trace-id:test-**********552864900-********** x-user-id:admin123]
2025/07/24 17:01:45 Response Status: 200
2025/07/24 17:01:45 Response Body: {
      "orders": [
        {
          "id": 29,
          "order_id": "202507241701455124STRIPE1948307604744704000",
          "user_id": "338944749677314048",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T17:01:45.434+08:00",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "cs_test_a1xvBB8ilLhpYBKrvCcKlATUan3oaSloWiqdWOyUkMg0gKulH3IVkZMAkv",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-24T17:01:45.434+08:00",
          "updated_at": "2025-07-24T09:01:46+08:00",
          "deleted": false
        },
        {
          "id": 28,
          "order_id": "202507241613252394STRIPE1948295440747204608",
          "user_id": "338944749677314048",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T16:13:25.31+08:00",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "cs_test_a1xkqPH2KwW3JbNcUaOFa8epwvhMLclBf7UTXtKEnBgeWq4hDbS5lvnWfV",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-24T16:13:25.31+08:00",
          "updated_at": "2025-07-24T08:13:26+08:00",
          "deleted": false
        },
        {
          "id": 27,
          "order_id": "202507241609585449STRIPE1948294571330899968",
          "user_id": "338944749677314048",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T16:09:58.026+08:00",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "cs_test_a1w78JJM1TCo9frUPS0J8S5d2WZwjhN1EByLetKVdw5CD7UVxlD4gphShZ",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-24T16:09:58.026+08:00",
          "updated_at": "2025-07-24T08:10:00+08:00",
          "deleted": false
        },
        {
          "id": 26,
          "order_id": "202507241608340313STRIPE1948294221534334976",
          "user_id": "338944749677314048",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T16:08:34.628+08:00",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "cs_test_a1CjvRctNF71SK5cyzsvUyMsSOlJSazWH7JkMiRHntBnASgDKq9VYG6YGe",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-24T16:08:34.628+08:00",
          "updated_at": "2025-07-24T08:08:35+08:00",
          "deleted": false
        },
        {
          "id": 25,
          "order_id": "202507241607541704STRIPE1948294051677605888",
          "user_id": "338944749677314048",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T16:07:54.131+08:00",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "cs_test_a1iTp8Ptv3ImFjOAXAABxSdWqeWLhESqt7NBIqQC5O9sW9nIvQ00NiM3t6",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-24T16:07:54.131+08:00",
          "updated_at": "2025-07-24T08:07:54+08:00",
          "deleted": false
        },
        {
          "id": 24,
          "order_id": "202507241607344815STRIPE1948293967799914496",
          "user_id": "338944749677314048",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T16:07:34.133+08:00",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "cs_test_a1ROzaLzGGejI56wtc9LZCBQT0SW0byC80cfSP7FxmjkQp9XF9bAZmHb24",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-24T16:07:34.133+08:00",
          "updated_at": "2025-07-24T08:07:34+08:00",
          "deleted": false
        },
        {
          "id": 23,
          "order_id": "202507241605040651STRIPE1948293338633342976",
          "user_id": "338944749677314048",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T16:05:04.127+08:00",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "cs_test_a1UjBXILvFPjsSNc4xnDRZMqJFaNybVjLl3KqJ9149MNplJCBHpYL0q4wv",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-24T16:05:04.127+08:00",
          "updated_at": "2025-07-24T08:05:05+08:00",
          "deleted": false
        },
        {
          "id": 22,
          "order_id": "202507241600074644STRIPE1948292092874723328",
          "user_id": "338944749677314048",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T16:00:07.115+08:00",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "cs_test_a1mtMDDe4nXWUUrCwGsAxJEDzSA31rCyrvs5KeSWqD6dOt1NeXoB6tHrxz",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-24T16:00:07.115+08:00",
          "updated_at": "2025-07-24T08:00:07+08:00",
          "deleted": false
        },
        {
          "id": 21,
          "order_id": "202507241559230263STRIPE1948291908019163136",
          "user_id": "338944749677314048",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T15:59:23.043+08:00",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "cs_test_a1CNte5WB8UOhkQfmFdBGghyGMDBKcMjcVX138PEntWW1VvirMjKaShFzO",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-24T15:59:23.043+08:00",
          "updated_at": "2025-07-24T07:59:24+08:00",
          "deleted": false
        },
        {
          "id": 20,
          "order_id": "202507241546214560STRIPE1948288629734379520",
          "user_id": "338944749677314048",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T15:46:21.438+08:00",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "cs_test_a1qY1T4Bb6FqXOGZCRSGKwL0yccUVIZzp6K4fKMcw1967cCv34ZanffSwg",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-24T15:46:21.438+08:00",
          "updated_at": "2025-07-24T07:46:22+08:00",
          "deleted": false
        },
        {
          "id": 19,
          "order_id": "202507241541521181STRIPE1948287503467286528",
          "user_id": "338944749677314048",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T15:41:52.915+08:00",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "cs_test_a1YhBDbHC3AGFzgL6EXywFZQqRdVlCkKAI6ML9pw9v3Hh51GiUnoTn2Wzp",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-24T15:41:52.915+08:00",
          "updated_at": "2025-07-24T07:41:53+08:00",
          "deleted": false
        },
        {
          "id": 18,
          "order_id": "202507241539578968STRIPE1948287018647687168",
          "user_id": "338944749677314048",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T15:39:57.325+08:00",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "cs_test_a1xsbzlyZ1Vr6ze9gV1EODPl3U4GcF14lpF6Drn8i6gmy4OiCNWldUh94U",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-24T15:39:57.325+08:00",
          "updated_at": "2025-07-24T07:39:57+08:00",
          "deleted": false
        },
        {
          "id": 17,
          "order_id": "202507241538452834STRIPE1948286715730857984",
          "user_id": "338944749677314048",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T15:38:45.105+08:00",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "cs_test_a1fWEpqzDWph0LTwfzEVVI8YaUCJdSFhsLfQfxR9EgZ5dFhvLFSc6fAtwB",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-24T15:38:45.105+08:00",
          "updated_at": "2025-07-24T07:38:45+08:00",
          "deleted": false
        },
        {
          "id": 16,
          "order_id": "202507241536186240STRIPE1948286100409683968",
          "user_id": "338944749677314048",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T15:36:18.401+08:00",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "cs_test_a1LmwLOGovU2w3NHQQPLKkslSJkRLetfnF37KTjDBxGijtjvLxdCePjD8b",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-24T15:36:18.401+08:00",
          "updated_at": "2025-07-24T07:36:19+08:00",
          "deleted": false
        },
        {
          "id": 15,
          "order_id": "202507241535040903STRIPE1948285790224125952",
          "user_id": "338944749677314048",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T15:35:04.446+08:00",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "cs_test_a1BjQQc33z2HKQP6MI0L3FhtkACEF4WY898DkEM15xtwO3Oncx0wHvl0W8",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-24T15:35:04.446+08:00",
          "updated_at": "2025-07-24T07:35:07+08:00",
          "deleted": false
        },
        {
          "id": 14,
          "order_id": "202507241530548175STRIPE1948284742067556352",
          "user_id": "338944749677314048",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T15:30:54.547+08:00",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "cs_test_a1LbcN8yjVybxb1ZSliccvMrUBEnGPk7vLt3fz7qXICaUhvDhryANDqotn",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-24T15:30:54.547+08:00",
          "updated_at": "2025-07-24T07:30:55+08:00",
          "deleted": false
        },
        {
          "id": 13,
          "order_id": "202507241530323835STRIPE1948284651307012096",
          "user_id": "338516746258350080",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T15:30:32.908+08:00",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "cs_test_a1NcP48IAIdGWr2H89M5s4Enycm1hvFd3Z3gF0XPxg8Zt8dcXRQkYgSFSm",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-24T15:30:32.908+08:00",
          "updated_at": "2025-07-24T07:30:33+08:00",
          "deleted": false
        },
        {
          "id": 12,
          "order_id": "202507241529299849STRIPE1948284383802691584",
          "user_id": "338516746258350080",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T15:29:29.129+08:00",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "cs_test_a1E4ettvboyuNTDUfZ6BFyP81Tmw9mDTHiUzDYyftOy9hmlqarX9CUDvbn",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-24T15:29:29.129+08:00",
          "updated_at": "2025-07-24T07:29:29+08:00",
          "deleted": false
        },
        {
          "id": 11,
          "order_id": "202507241518586952STRIPE1948281738727788544",
          "user_id": "338516746258350080",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T15:18:58.494+08:00",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "cs_test_a1XRpw2mtzDcU2LhsczxlvtSHbUr5jbawvulQNtSTMS9KkORe929qCw3cq",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-24T15:18:58.495+08:00",
          "updated_at": "2025-07-24T07:18:59+08:00",
          "deleted": false
        },
        {
          "id": 10,
          "order_id": "202507241517536334STRIPE1948281467943522304",
          "user_id": "338516746258350080",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T15:17:53.934+08:00",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "cs_test_a13ikPF4J2GACTfOyDZLJAGxku5rkACCB1thxksVHvLeMSdTFzfjwlFm5K",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-24T15:17:53.934+08:00",
          "updated_at": "2025-07-24T07:17:54+08:00",
          "deleted": false
        },
        {
          "id": 9,
          "order_id": "202507241516255767STRIPE1948281097494204416",
          "user_id": "338516746258350080",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T15:16:25.613+08:00",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "cs_test_a1SkuAqLo0oOQ5mWPbdLzAHGwmEnmxKw5gDvIYwirqPNkFYRV82nWnPuQm",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-24T15:16:25.613+08:00",
          "updated_at": "2025-07-24T07:16:26+08:00",
          "deleted": false
        },
        {
          "id": 8,
          "order_id": "202507241503416897STRIPE1948277891846377472",
          "user_id": "338516746258350080",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T15:03:41.326+08:00",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "cs_test_a10QgI12U3uZAxDHq9m2DFXhqM5AbLSkCBFAXi7TbriTu15X1BcKZq1ZyB",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-24T15:03:41.326+08:00",
          "updated_at": "2025-07-24T07:03:41+08:00",
          "deleted": false
        },
        {
          "id": 7,
          "order_id": "202507241502540515STRIPE1948277695578116096",
          "user_id": "338516746258350080",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T15:02:54.532+08:00",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "cs_test_a15Nclw1uG2T2EG5rq0RyXVN5hNlJvSJHwLFaOGxqcSd6ycgkWfiUnHVPt",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-24T15:02:54.532+08:00",
          "updated_at": "2025-07-24T07:02:55+08:00",
          "deleted": false
        },
        {
          "id": 6,
          "order_id": "202507241501100834STRIPE1948277258917515264",
          "user_id": "338516746258350080",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T15:01:10.425+08:00",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "cs_test_a1UahySz6iw6InezAcJM4Rxs0vtDGKxMDjRGeCVgjzsFyR96ecLmdl1mn4",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-24T15:01:10.425+08:00",
          "updated_at": "2025-07-24T07:01:11+08:00",
          "deleted": false
        },
        {
          "id": 5,
          "order_id": "202507241500285052STRIPE1948277082718998528",
          "user_id": "338516746258350080",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T15:00:28.416+08:00",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "cs_test_a1qWS0Ng6NGLyE4TiYaSB9ABO3WSWWfTOkh85yYZFjnvfFed0kSDvW9sqc",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-24T15:00:28.416+08:00",
          "updated_at": "2025-07-24T07:00:29+08:00",
          "deleted": false
        },
        {
          "id": 4,
          "order_id": "202507241459570555STRIPE1948276953995808768",
          "user_id": "338516746258350080",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T14:59:57.726+08:00",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "cs_test_a1BKADH0o157vmHQHccpyjziN6N4VHSc4neAM7JPQlqoowR2seyNMSbhn1",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-24T14:59:57.726+08:00",
          "updated_at": "2025-07-24T06:59:58+08:00",
          "deleted": false
        },
        {
          "id": 3,
          "order_id": "202507241458421749STRIPE1948276639917936640",
          "user_id": "338516746258350080",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T14:58:42.843+08:00",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "cs_test_a1r2ZTKMl0Uz0WYKlrpWYLuqer16bWkld4sNhG6QU8p0EffBu9V0JKFx8I",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-24T14:58:42.843+08:00",
          "updated_at": "2025-07-24T06:58:43+08:00",
          "deleted": false
        },
        {
          "id": 2,
          "order_id": "202507240954256308STRIPE1948200064421728256",
          "user_id": "338516746258350080",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T09:54:25.822+08:00",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "cs_test_a1g7dDcIe1D5uQ2a1UJVNaiSTi7yNEBp4j3VY9TlFnxwXIqX3XEICK4Gwp",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-24T09:54:25.823+08:00",
          "updated_at": "2025-07-24T01:54:26+08:00",
          "deleted": false
        }
      ],
      "pagination": {
        "total": 28,
        "limit": 50,
        "offset": 0,
        "remaining": 0
      }
    }
2025/07/24 17:01:45 --- List All Orders (Internal Dubbo RPC) ---
2025/07/24 17:01:45 URL: http://*************:15446/com.aibook.payment.grpc.OrderService/ListAllOrders
2025/07/24 17:01:45 Headers: map[x-trace-id:test-**********768081800-**********]
2025/07/24 17:01:45 Request Body: map[pagination:map[limit:50 offset:0]]
2025/07/24 17:01:45 Response Status: 200
2025/07/24 17:01:45 Response Body: {
      "orders": [
        {
          "id": "29",
          "order_id": "202507241701455124STRIPE1948307604744704000",
          "user_id": "338944749677314048",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "payed_at": "2025-07-24T09:01:45.434Z",
          "refund_status": "none",
          "psp_payment_id": "cs_test_a1xvBB8ilLhpYBKrvCcKlATUan3oaSloWiqdWOyUkMg0gKulH3IVkZMAkv",
          "created_at": "2025-07-24T09:01:45.434Z",
          "updated_at": "2025-07-24T01:01:46Z"
        },
        {
          "id": "28",
          "order_id": "202507241613252394STRIPE1948295440747204608",
          "user_id": "338944749677314048",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "payed_at": "2025-07-24T08:13:25.310Z",
          "refund_status": "none",
          "psp_payment_id": "cs_test_a1xkqPH2KwW3JbNcUaOFa8epwvhMLclBf7UTXtKEnBgeWq4hDbS5lvnWfV",
          "created_at": "2025-07-24T08:13:25.310Z",
          "updated_at": "2025-07-24T00:13:26Z"
        },
        {
          "id": "27",
          "order_id": "202507241609585449STRIPE1948294571330899968",
          "user_id": "338944749677314048",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "payed_at": "2025-07-24T08:09:58.026Z",
          "refund_status": "none",
          "psp_payment_id": "cs_test_a1w78JJM1TCo9frUPS0J8S5d2WZwjhN1EByLetKVdw5CD7UVxlD4gphShZ",
          "created_at": "2025-07-24T08:09:58.026Z",
          "updated_at": "2025-07-24T00:10:00Z"
        },
        {
          "id": "26",
          "order_id": "202507241608340313STRIPE1948294221534334976",
          "user_id": "338944749677314048",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "payed_at": "2025-07-24T08:08:34.628Z",
          "refund_status": "none",
          "psp_payment_id": "cs_test_a1CjvRctNF71SK5cyzsvUyMsSOlJSazWH7JkMiRHntBnASgDKq9VYG6YGe",
          "created_at": "2025-07-24T08:08:34.628Z",
          "updated_at": "2025-07-24T00:08:35Z"
        },
        {
          "id": "25",
          "order_id": "202507241607541704STRIPE1948294051677605888",
          "user_id": "338944749677314048",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "payed_at": "2025-07-24T08:07:54.131Z",
          "refund_status": "none",
          "psp_payment_id": "cs_test_a1iTp8Ptv3ImFjOAXAABxSdWqeWLhESqt7NBIqQC5O9sW9nIvQ00NiM3t6",
          "created_at": "2025-07-24T08:07:54.131Z",
          "updated_at": "2025-07-24T00:07:54Z"
        },
        {
          "id": "24",
          "order_id": "202507241607344815STRIPE1948293967799914496",
          "user_id": "338944749677314048",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "payed_at": "2025-07-24T08:07:34.133Z",
          "refund_status": "none",
          "psp_payment_id": "cs_test_a1ROzaLzGGejI56wtc9LZCBQT0SW0byC80cfSP7FxmjkQp9XF9bAZmHb24",
          "created_at": "2025-07-24T08:07:34.133Z",
          "updated_at": "2025-07-24T00:07:34Z"
        },
        {
          "id": "23",
          "order_id": "202507241605040651STRIPE1948293338633342976",
          "user_id": "338944749677314048",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "payed_at": "2025-07-24T08:05:04.127Z",
          "refund_status": "none",
          "psp_payment_id": "cs_test_a1UjBXILvFPjsSNc4xnDRZMqJFaNybVjLl3KqJ9149MNplJCBHpYL0q4wv",
          "created_at": "2025-07-24T08:05:04.127Z",
          "updated_at": "2025-07-24T00:05:05Z"
        },
        {
          "id": "22",
          "order_id": "202507241600074644STRIPE1948292092874723328",
          "user_id": "338944749677314048",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "payed_at": "2025-07-24T08:00:07.115Z",
          "refund_status": "none",
          "psp_payment_id": "cs_test_a1mtMDDe4nXWUUrCwGsAxJEDzSA31rCyrvs5KeSWqD6dOt1NeXoB6tHrxz",
          "created_at": "2025-07-24T08:00:07.115Z",
          "updated_at": "2025-07-24T00:00:07Z"
        },
        {
          "id": "21",
          "order_id": "202507241559230263STRIPE1948291908019163136",
          "user_id": "338944749677314048",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "payed_at": "2025-07-24T07:59:23.043Z",
          "refund_status": "none",
          "psp_payment_id": "cs_test_a1CNte5WB8UOhkQfmFdBGghyGMDBKcMjcVX138PEntWW1VvirMjKaShFzO",
          "created_at": "2025-07-24T07:59:23.043Z",
          "updated_at": "2025-07-23T23:59:24Z"
        },
        {
          "id": "20",
          "order_id": "202507241546214560STRIPE1948288629734379520",
          "user_id": "338944749677314048",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "payed_at": "2025-07-24T07:46:21.438Z",
          "refund_status": "none",
          "psp_payment_id": "cs_test_a1qY1T4Bb6FqXOGZCRSGKwL0yccUVIZzp6K4fKMcw1967cCv34ZanffSwg",
          "created_at": "2025-07-24T07:46:21.438Z",
          "updated_at": "2025-07-23T23:46:22Z"
        },
        {
          "id": "19",
          "order_id": "202507241541521181STRIPE1948287503467286528",
          "user_id": "338944749677314048",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "payed_at": "2025-07-24T07:41:52.915Z",
          "refund_status": "none",
          "psp_payment_id": "cs_test_a1YhBDbHC3AGFzgL6EXywFZQqRdVlCkKAI6ML9pw9v3Hh51GiUnoTn2Wzp",
          "created_at": "2025-07-24T07:41:52.915Z",
          "updated_at": "2025-07-23T23:41:53Z"
        },
        {
          "id": "18",
          "order_id": "202507241539578968STRIPE1948287018647687168",
          "user_id": "338944749677314048",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "payed_at": "2025-07-24T07:39:57.325Z",
          "refund_status": "none",
          "psp_payment_id": "cs_test_a1xsbzlyZ1Vr6ze9gV1EODPl3U4GcF14lpF6Drn8i6gmy4OiCNWldUh94U",
          "created_at": "2025-07-24T07:39:57.325Z",
          "updated_at": "2025-07-23T23:39:57Z"
        },
        {
          "id": "17",
          "order_id": "202507241538452834STRIPE1948286715730857984",
          "user_id": "338944749677314048",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "payed_at": "2025-07-24T07:38:45.105Z",
          "refund_status": "none",
          "psp_payment_id": "cs_test_a1fWEpqzDWph0LTwfzEVVI8YaUCJdSFhsLfQfxR9EgZ5dFhvLFSc6fAtwB",
          "created_at": "2025-07-24T07:38:45.105Z",
          "updated_at": "2025-07-23T23:38:45Z"
        },
        {
          "id": "16",
          "order_id": "202507241536186240STRIPE1948286100409683968",
          "user_id": "338944749677314048",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "payed_at": "2025-07-24T07:36:18.401Z",
          "refund_status": "none",
          "psp_payment_id": "cs_test_a1LmwLOGovU2w3NHQQPLKkslSJkRLetfnF37KTjDBxGijtjvLxdCePjD8b",
          "created_at": "2025-07-24T07:36:18.401Z",
          "updated_at": "2025-07-23T23:36:19Z"
        },
        {
          "id": "15",
          "order_id": "202507241535040903STRIPE1948285790224125952",
          "user_id": "338944749677314048",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "payed_at": "2025-07-24T07:35:04.446Z",
          "refund_status": "none",
          "psp_payment_id": "cs_test_a1BjQQc33z2HKQP6MI0L3FhtkACEF4WY898DkEM15xtwO3Oncx0wHvl0W8",
          "created_at": "2025-07-24T07:35:04.446Z",
          "updated_at": "2025-07-23T23:35:07Z"
        },
        {
          "id": "14",
          "order_id": "202507241530548175STRIPE1948284742067556352",
          "user_id": "338944749677314048",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "payed_at": "2025-07-24T07:30:54.547Z",
          "refund_status": "none",
          "psp_payment_id": "cs_test_a1LbcN8yjVybxb1ZSliccvMrUBEnGPk7vLt3fz7qXICaUhvDhryANDqotn",
          "created_at": "2025-07-24T07:30:54.547Z",
          "updated_at": "2025-07-23T23:30:55Z"
        },
        {
          "id": "13",
          "order_id": "202507241530323835STRIPE1948284651307012096",
          "user_id": "338516746258350080",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "payed_at": "2025-07-24T07:30:32.908Z",
          "refund_status": "none",
          "psp_payment_id": "cs_test_a1NcP48IAIdGWr2H89M5s4Enycm1hvFd3Z3gF0XPxg8Zt8dcXRQkYgSFSm",
          "created_at": "2025-07-24T07:30:32.908Z",
          "updated_at": "2025-07-23T23:30:33Z"
        },
        {
          "id": "12",
          "order_id": "202507241529299849STRIPE1948284383802691584",
          "user_id": "338516746258350080",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "payed_at": "2025-07-24T07:29:29.129Z",
          "refund_status": "none",
          "psp_payment_id": "cs_test_a1E4ettvboyuNTDUfZ6BFyP81Tmw9mDTHiUzDYyftOy9hmlqarX9CUDvbn",
          "created_at": "2025-07-24T07:29:29.129Z",
          "updated_at": "2025-07-23T23:29:29Z"
        },
        {
          "id": "11",
          "order_id": "202507241518586952STRIPE1948281738727788544",
          "user_id": "338516746258350080",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "payed_at": "2025-07-24T07:18:58.494Z",
          "refund_status": "none",
          "psp_payment_id": "cs_test_a1XRpw2mtzDcU2LhsczxlvtSHbUr5jbawvulQNtSTMS9KkORe929qCw3cq",
          "created_at": "2025-07-24T07:18:58.495Z",
          "updated_at": "2025-07-23T23:18:59Z"
        },
        {
          "id": "10",
          "order_id": "202507241517536334STRIPE1948281467943522304",
          "user_id": "338516746258350080",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "payed_at": "2025-07-24T07:17:53.934Z",
          "refund_status": "none",
          "psp_payment_id": "cs_test_a13ikPF4J2GACTfOyDZLJAGxku5rkACCB1thxksVHvLeMSdTFzfjwlFm5K",
          "created_at": "2025-07-24T07:17:53.934Z",
          "updated_at": "2025-07-23T23:17:54Z"
        },
        {
          "id": "9",
          "order_id": "202507241516255767STRIPE1948281097494204416",
          "user_id": "338516746258350080",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "payed_at": "2025-07-24T07:16:25.613Z",
          "refund_status": "none",
          "psp_payment_id": "cs_test_a1SkuAqLo0oOQ5mWPbdLzAHGwmEnmxKw5gDvIYwirqPNkFYRV82nWnPuQm",
          "created_at": "2025-07-24T07:16:25.613Z",
          "updated_at": "2025-07-23T23:16:26Z"
        },
        {
          "id": "8",
          "order_id": "202507241503416897STRIPE1948277891846377472",
          "user_id": "338516746258350080",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "payed_at": "2025-07-24T07:03:41.326Z",
          "refund_status": "none",
          "psp_payment_id": "cs_test_a10QgI12U3uZAxDHq9m2DFXhqM5AbLSkCBFAXi7TbriTu15X1BcKZq1ZyB",
          "created_at": "2025-07-24T07:03:41.326Z",
          "updated_at": "2025-07-23T23:03:41Z"
        },
        {
          "id": "7",
          "order_id": "202507241502540515STRIPE1948277695578116096",
          "user_id": "338516746258350080",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "payed_at": "2025-07-24T07:02:54.532Z",
          "refund_status": "none",
          "psp_payment_id": "cs_test_a15Nclw1uG2T2EG5rq0RyXVN5hNlJvSJHwLFaOGxqcSd6ycgkWfiUnHVPt",
          "created_at": "2025-07-24T07:02:54.532Z",
          "updated_at": "2025-07-23T23:02:55Z"
        },
        {
          "id": "6",
          "order_id": "202507241501100834STRIPE1948277258917515264",
          "user_id": "338516746258350080",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "payed_at": "2025-07-24T07:01:10.425Z",
          "refund_status": "none",
          "psp_payment_id": "cs_test_a1UahySz6iw6InezAcJM4Rxs0vtDGKxMDjRGeCVgjzsFyR96ecLmdl1mn4",
          "created_at": "2025-07-24T07:01:10.425Z",
          "updated_at": "2025-07-23T23:01:11Z"
        },
        {
          "id": "5",
          "order_id": "202507241500285052STRIPE1948277082718998528",
          "user_id": "338516746258350080",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "payed_at": "2025-07-24T07:00:28.416Z",
          "refund_status": "none",
          "psp_payment_id": "cs_test_a1qWS0Ng6NGLyE4TiYaSB9ABO3WSWWfTOkh85yYZFjnvfFed0kSDvW9sqc",
          "created_at": "2025-07-24T07:00:28.416Z",
          "updated_at": "2025-07-23T23:00:29Z"
        },
        {
          "id": "4",
          "order_id": "202507241459570555STRIPE1948276953995808768",
          "user_id": "338516746258350080",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "payed_at": "2025-07-24T06:59:57.726Z",
          "refund_status": "none",
          "psp_payment_id": "cs_test_a1BKADH0o157vmHQHccpyjziN6N4VHSc4neAM7JPQlqoowR2seyNMSbhn1",
          "created_at": "2025-07-24T06:59:57.726Z",
          "updated_at": "2025-07-23T22:59:58Z"
        },
        {
          "id": "3",
          "order_id": "202507241458421749STRIPE1948276639917936640",
          "user_id": "338516746258350080",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "payed_at": "2025-07-24T06:58:42.843Z",
          "refund_status": "none",
          "psp_payment_id": "cs_test_a1r2ZTKMl0Uz0WYKlrpWYLuqer16bWkld4sNhG6QU8p0EffBu9V0JKFx8I",
          "created_at": "2025-07-24T06:58:42.843Z",
          "updated_at": "2025-07-23T22:58:43Z"
        },
        {
          "id": "2",
          "order_id": "202507240954256308STRIPE1948200064421728256",
          "user_id": "338516746258350080",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "payed_at": "2025-07-24T01:54:25.822Z",
          "refund_status": "none",
          "psp_payment_id": "cs_test_a1g7dDcIe1D5uQ2a1UJVNaiSTi7yNEBp4j3VY9TlFnxwXIqX3XEICK4Gwp",
          "created_at": "2025-07-24T01:54:25.823Z",
          "updated_at": "2025-07-23T17:54:26Z"
        }
      ],
      "pagination": {
        "total": "28",
        "limit": 50
      }
    }
2025/07/24 17:01:45 --- Force Refund Order (Internal Gin HTTP) ---
2025/07/24 17:01:45 URL: http://*************:15445/api/v1/pay-service/admin/order-service/orders/20250710153045999stripe1234567890123456789/force-refund
2025/07/24 17:01:45 Headers: map[x-role:admin x-trace-id:test-**********940281100-********** x-user-id:admin123]
2025/07/24 17:01:45 Request Body: {Amount:<nil>}
2025/07/24 17:01:46 Response Status: 400
2025/07/24 17:01:46 Response Body: {
      "code": 10006,
      "message": "failed to process refund: order with order ID 20250710153045999stripe1234567890123456789 not found"
    }
2025/07/24 17:01:46 Request failed with HTTP 400: {"code":10006,"message":"failed to process refund: order with order ID 20250710153045999stripe1234567890123456789 not found"}
2025/07/24 17:01:46 --- Admin Add Packages (Internal Gin HTTP) ---
2025/07/24 17:01:46 URL: http://*************:15445/api/v1/pay-service/admin/store-service/packages
2025/07/24 17:01:46 Headers: map[x-role:admin x-trace-id:test-**********036561300-********** x-user-id:admin123]
2025/07/24 17:01:46 Request Body: {PackageName:测试流量包 PackageDesc:用于API测试的流量包 Entitlement:100 EntitlementDesc:100GB流量 OriginalPrice:19.99 DiscountPrice:<nil> DiscountStartTime:<nil> DiscountEndTime:<nil> DiscountDesc: SaleStatus:on_sale Currency:USD Country:US Extra1:test Extra2:api Extra3:100 Extra4:30}
2025/07/24 17:01:46 Response Status: 201
2025/07/24 17:01:46 Response Body: {
      "message": "Package created successfully"
    }
2025/07/24 17:01:46 --- Admin List All Packages (Internal Gin HTTP) ---
2025/07/24 17:01:46 URL: http://*************:15445/api/v1/pay-service/admin/store-service/packages?limit=50&offset=0
2025/07/24 17:01:46 Headers: map[x-role:admin x-trace-id:test-**********110599700-********** x-user-id:admin123]
2025/07/24 17:01:46 Response Status: 200
2025/07/24 17:01:46 Response Body: {
      "packages": [
        {
          "id": 2,
          "package_id": "2e0e8b6c-e102-4322-a9b1-2d9c296a61e5",
          "package_name": "测试流量包",
          "package_desc": "用于API测试的流量包",
          "entitlement": 100,
          "entitlement_desc": "100GB流量",
          "original_price": 19.99,
          "discount_price": null,
          "discount_start_time": null,
          "discount_end_time": null,
          "discount_desc": "",
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "US",
          "extra1": "test",
          "extra2": "api",
          "extra3": 100,
          "extra4": 30,
          "created_at": "2025-07-24T17:01:46.846+08:00",
          "updated_at": "2025-07-24T17:01:46.846+08:00"
        },
        {
          "id": 1,
          "package_id": "08618ca1-50a9-40ef-9a10-09269d6b3431",
          "package_name": "测试流量包",
          "package_desc": "用于API测试的流量包",
          "entitlement": 100,
          "entitlement_desc": "100GB流量",
          "original_price": 19.99,
          "discount_price": null,
          "discount_start_time": null,
          "discount_end_time": null,
          "discount_desc": "",
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "US",
          "extra1": "test",
          "extra2": "api",
          "extra3": 100,
          "extra4": 30,
          "created_at": "2025-07-24T17:01:16.667+08:00",
          "updated_at": "2025-07-24T17:01:16.667+08:00"
        }
      ],
      "pagination": {
        "total": 2,
        "limit": 50,
        "offset": 0,
        "remaining": 0
      }
    }
2025/07/24 17:01:46 --- Admin Update Packages (Internal Gin HTTP) ---
2025/07/24 17:01:46 URL: http://*************:15445/api/v1/pay-service/admin/store-service/packages
2025/07/24 17:01:46 Headers: map[x-role:admin x-trace-id:test-**********201023700-********** x-user-id:admin123]
2025/07/24 17:01:46 Request Body: {PackageID:test-package-id-replace-with-real-one PackageName:0xc0003a21b0 PackageDesc:<nil> Entitlement:<nil> EntitlementDesc:<nil> OriginalPrice:0xc00038e0a0 DiscountPrice:<nil> DiscountStartTime:<nil> DiscountEndTime:<nil> DiscountDesc:<nil> SaleStatus:<nil> Currency:<nil> Country:<nil> Extra1:<nil> Extra2:<nil> Extra3:<nil> Extra4:<nil>}
2025/07/24 17:01:46 Response Status: 500
2025/07/24 17:01:46 Response Body: {
      "code": 10006,
      "message": "Failed to update package"
    }
2025/07/24 17:01:46 Request failed with HTTP 500: {"code":10006,"message":"Failed to update package"}
2025/07/24 17:01:46 --- Admin Delete Packages (Internal Gin HTTP) ---
2025/07/24 17:01:46 URL: http://*************:15445/api/v1/pay-service/admin/store-service/packages
2025/07/24 17:01:46 Headers: map[x-role:admin x-trace-id:test-**********297377700-********** x-user-id:admin123]
2025/07/24 17:01:46 Request Body: {PackageID:test-package-id-replace-with-real-one Currency:USD Country:US}
2025/07/24 17:01:46 Response Status: 200
2025/07/24 17:01:46 Response Body: {
      "message": "Package deleted successfully"
    }
2025/07/24 17:01:46 --- Admin Add Packages (Internal Dubbo RPC) ---
2025/07/24 17:01:46 URL: http://*************:15446/com.aibook.payment.grpc.StoreService/AdminAddPackages
2025/07/24 17:01:46 Headers: map[x-trace-id:test-**********354189000-**********]
2025/07/24 17:01:46 Request Body: map[country:US currency:USD discount_desc: entitlement:100 entitlement_desc:100GB流量 extra1:rpc extra2:test extra3:100 extra4:30 original_price:19.99 package_desc:用于RPC API测试的流量包 package_name:RPC测试流量包 sale_status:on_sale]
2025/07/24 17:01:46 Response Status: 404
2025/07/24 17:01:46 Response Body: 404 page not found
2025/07/24 17:01:46 Request failed with HTTP 404: 404 page not found
2025/07/24 17:01:46 --- Admin List All Packages (Internal Dubbo RPC) ---
2025/07/24 17:01:46 URL: http://*************:15446/com.aibook.payment.grpc.StoreService/AdminListAllPackages
2025/07/24 17:01:46 Headers: map[x-trace-id:test-**********406224300-**********]
2025/07/24 17:01:46 Request Body: map[pagination:map[limit:50 offset:0]]
2025/07/24 17:01:46 Response Status: 404
2025/07/24 17:01:46 Response Body: 404 page not found
2025/07/24 17:01:46 Request failed with HTTP 404: 404 page not found
2025/07/24 17:01:46 --- Admin Update Packages (Internal Dubbo RPC) ---
2025/07/24 17:01:46 URL: http://*************:15446/com.aibook.payment.grpc.StoreService/AdminUpdatePackages
2025/07/24 17:01:46 Headers: map[x-trace-id:test-**********488696800-**********]
2025/07/24 17:01:46 Request Body: map[original_price:24.99 package_id:test-package-id-replace-with-real-one package_name:RPC更新后的测试流量包]
2025/07/24 17:01:46 Response Status: 404
2025/07/24 17:01:46 Response Body: 404 page not found
2025/07/24 17:01:46 Request failed with HTTP 404: 404 page not found
2025/07/24 17:01:46 --- Admin Delete Packages (Internal Dubbo RPC) ---
2025/07/24 17:01:46 URL: http://*************:15446/com.aibook.payment.grpc.StoreService/AdminDeletePackages
2025/07/24 17:01:46 Headers: map[x-trace-id:test-**********540705000-**********]
2025/07/24 17:01:46 Request Body: map[country:US currency:USD package_id:test-package-id-replace-with-real-one]
2025/07/24 17:01:46 Response Status: 404
2025/07/24 17:01:46 Response Body: 404 page not found
2025/07/24 17:01:46 Request failed with HTTP 404: 404 page not found
2025/07/24 17:01:46 --------------- Finished test for dev1 ---------------
2025/07/24 17:01:46 +++++++++++++++ Starting test for dev2 +++++++++++++++
2025/07/24 17:01:46 Testing environment: dev2
2025/07/24 17:01:46 External URL: http://ny10wt9045294.vicp.fun
2025/07/24 17:01:46 Local Gin URL: http://*************:25906
2025/07/24 17:01:46 Local RPC URL: http://*************:25907
2025/07/24 17:01:46 
2025/07/24 17:01:46 === Getting External API Token ===
2025/07/24 17:01:46 Token obtained successfully: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************.kE6rVd6KnnU92Y20VkUut5Ftd6HMhjecIyApD5emKNA
2025/07/24 17:01:46 === Testing External APIs (Gin HTTP only) ===
2025/07/24 17:01:46 --- Create Order (External Gin HTTP) ---
2025/07/24 17:01:46 URL: http://ny10wt9045294.vicp.fun/api/v1/pay-service/order-service/orders
2025/07/24 17:01:46 Headers: map[Authorization:Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************.kE6rVd6KnnU92Y20VkUut5Ftd6HMhjecIyApD5emKNA x-trace-id:test-**********836553800-**********]
2025/07/24 17:01:46 Request Body: {ProductID:prod_stripe_001 ProductDesc:Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq PriceID:price_1Rd4qlC53MAl6WmqQ9ORYbPq Quantity:1 PSPProvider:stripe}
2025/07/24 17:01:47 Response Status: 303
2025/07/24 17:01:47 Response Body: {
      "order_id": "202507241701474541STRIPE1948307614014115840",
      "checkout_url": "https://checkout.stripe.com/c/pay/cs_test_a1Jf3AgqXZJaGMlr2BTN8yVaefNTLVNLqKESwBpj7rl1Myp4zVjkQo6Jif#fidkdWxOYHwnPyd1blpxYHZxWjA0V2dhc0ZGMDZIRGkzUmh0a2loPV1zM3NjUWNjfVJHbGB%2FXDFkbWM0cW90QFFzVjJ%2FbDNfRnI0NmRoVmFpMnJUdXxidUZxPUJJQ0lpY0dEQW18f0tBZFJxNTVLV2hsbFxQSScpJ2N3amhWYHdzYHcnP3F3cGApJ2lkfGpwcVF8dWAnPyd2bGtiaWBabHFgaCcpJ2BrZGdpYFVpZGZgbWppYWB3dic%2FcXdwYHgl",
      "amount": 0,
      "currency": "",
      "expires_at": "2025-07-25T17:01:48.518386738+08:00"
    }
2025/07/24 17:01:47 Order Details - ID: 202507241701474541STRIPE1948307614014115840, Amount: 0.00 , Checkout URL: https://checkout.stripe.com/c/pay/cs_test_a1Jf3AgqXZJaGMlr2BTN8yVaefNTLVNLqKESwBpj7rl1Myp4zVjkQo6Jif#fidkdWxOYHwnPyd1blpxYHZxWjA0V2dhc0ZGMDZIRGkzUmh0a2loPV1zM3NjUWNjfVJHbGB%2FXDFkbWM0cW90QFFzVjJ%2FbDNfRnI0NmRoVmFpMnJUdXxidUZxPUJJQ0lpY0dEQW18f0tBZFJxNTVLV2hsbFxQSScpJ2N3amhWYHdzYHcnP3F3cGApJ2lkfGpwcVF8dWAnPyd2bGtiaWBabHFgaCcpJ2BrZGdpYFVpZGZgbWppYWB3dic%2FcXdwYHgl
2025/07/24 17:01:47 --- Get User Orders (External Gin HTTP) ---
2025/07/24 17:01:47 URL: http://ny10wt9045294.vicp.fun/api/v1/pay-service/order-service/orders?limit=50&offset=0
2025/07/24 17:01:47 Headers: map[Authorization:Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************.kE6rVd6KnnU92Y20VkUut5Ftd6HMhjecIyApD5emKNA x-trace-id:test-**********761820300-**********]
2025/07/24 17:01:47 Response Status: 200
2025/07/24 17:01:47 Response Body: {
      "userOrders": [
        {
          "order_id": "202507241701474541STRIPE1948307614014115840",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T17:01:47.644+08:00",
          "refund_status": "none"
        },
        {
          "order_id": "202507241701178629STRIPE1948307487878811648",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T17:01:17.57+08:00",
          "refund_status": "none"
        },
        {
          "order_id": "202507241615139527STRIPE1948295894398930944",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T16:15:13.47+08:00",
          "refund_status": "none"
        },
        {
          "order_id": "202507241610026378STRIPE1948294589513207808",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T16:10:02.36+08:00",
          "refund_status": "none"
        },
        {
          "order_id": "202507241609366107STRIPE1948294482277437440",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T16:09:36.793+08:00",
          "refund_status": "none"
        },
        {
          "order_id": "202507241608028961STRIPE1948294087442436096",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T16:08:02.657+08:00",
          "refund_status": "none"
        },
        {
          "order_id": "202507241607354240STRIPE1948293974062010368",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T16:07:35.625+08:00",
          "refund_status": "none"
        },
        {
          "order_id": "202507241605053575STRIPE1948293345683968000",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T16:05:05.809+08:00",
          "refund_status": "none"
        },
        {
          "order_id": "202507241559247712STRIPE1948291916202250240",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T15:59:24.993+08:00",
          "refund_status": "none"
        },
        {
          "order_id": "202507241546236220STRIPE1948288638290759680",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T15:46:23.478+08:00",
          "refund_status": "none"
        },
        {
          "order_id": "202507241541541236STRIPE1948287509238648832",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T15:41:54.292+08:00",
          "refund_status": "none"
        },
        {
          "order_id": "202507241540598713STRIPE1948287277369135104",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T15:40:59.009+08:00",
          "refund_status": "none"
        },
        {
          "order_id": "202507241539586297STRIPE1948287024184168448",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T15:39:58.646+08:00",
          "refund_status": "none"
        },
        {
          "order_id": "202507241538467282STRIPE1948286722118782976",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T15:38:46.627+08:00",
          "refund_status": "none"
        },
        {
          "order_id": "202507241536198994STRIPE1948286106109743104",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T15:36:19.76+08:00",
          "refund_status": "none"
        },
        {
          "order_id": "202507241535072301STRIPE1948285804484759552",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T15:35:07.847+08:00",
          "refund_status": "none"
        },
        {
          "order_id": "202507241530552257STRIPE1948284747675340800",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T15:30:55.883+08:00",
          "refund_status": "none"
        }
      ],
      "pagination": {
        "total": 17,
        "limit": 50,
        "offset": 0,
        "remaining": 0
      }
    }
2025/07/24 17:01:47 --- List All Packages (External Gin HTTP) ---
2025/07/24 17:01:47 URL: http://ny10wt9045294.vicp.fun/api/v1/pay-service/store-service/packages?limit=50&offset=0
2025/07/24 17:01:47 Headers: map[Authorization:Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************.kE6rVd6KnnU92Y20VkUut5Ftd6HMhjecIyApD5emKNA x-trace-id:test-**********849387900-**********]
2025/07/24 17:01:47 Response Status: 200
2025/07/24 17:01:47 Response Body: {
      "packages": [
        {
          "package_id": "772f0f80-22e1-4a39-ba8c-d56178677d06",
          "package_name": "测试流量包",
          "package_desc": "用于API测试的流量包",
          "entitlement": 100,
          "entitlement_desc": "100GB流量",
          "price": 19.99,
          "original_price": null,
          "discount_start_time": null,
          "discount_end_time": null,
          "discount_desc": "",
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "US"
        }
      ],
      "pagination": {
        "total": 1,
        "limit": 50,
        "offset": 0,
        "remaining": 0
      }
    }
2025/07/24 17:01:47 --- List All Packages with Filters (External Gin HTTP) ---
2025/07/24 17:01:47 URL: http://ny10wt9045294.vicp.fun/api/v1/pay-service/store-service/packages?currency=USD&country=US&limit=20&offset=0
2025/07/24 17:01:47 Headers: map[Authorization:Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************.kE6rVd6KnnU92Y20VkUut5Ftd6HMhjecIyApD5emKNA x-trace-id:test-**********902820000-**********]
2025/07/24 17:01:47 Response Status: 200
2025/07/24 17:01:47 Response Body: {
      "packages": [
        {
          "package_id": "772f0f80-22e1-4a39-ba8c-d56178677d06",
          "package_name": "测试流量包",
          "package_desc": "用于API测试的流量包",
          "entitlement": 100,
          "entitlement_desc": "100GB流量",
          "price": 19.99,
          "original_price": null,
          "discount_start_time": null,
          "discount_end_time": null,
          "discount_desc": "",
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "US"
        }
      ],
      "pagination": {
        "total": 1,
        "limit": 20,
        "offset": 0,
        "remaining": 0
      }
    }
2025/07/24 17:01:47 === Testing Internal APIs (Gin HTTP + Dubbo RPC) ===
2025/07/24 17:01:47 --- List All Orders (Internal Gin HTTP) ---
2025/07/24 17:01:47 URL: http://*************:25906/api/v1/pay-service/admin/order-service/orders?limit=50&offset=0
2025/07/24 17:01:47 Headers: map[x-role:admin x-trace-id:test-**********955947700-********** x-user-id:admin123]
2025/07/24 17:01:48 Response Status: 200
2025/07/24 17:01:48 Response Body: {
      "orders": [
        {
          "id": 1017,
          "order_id": "202507241701474541STRIPE1948307614014115840",
          "user_id": "338945122932625408",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T17:01:47.644+08:00",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "cs_test_a1Jf3AgqXZJaGMlr2BTN8yVaefNTLVNLqKESwBpj7rl1Myp4zVjkQo6Jif",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-24T17:01:47.644+08:00",
          "updated_at": "2025-07-24T09:01:48+08:00",
          "deleted": false
        },
        {
          "id": 1016,
          "order_id": "202507241701178629STRIPE1948307487878811648",
          "user_id": "338945122932625408",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T17:01:17.57+08:00",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "cs_test_a1cwGG2iDmQ2rhFDBJNujwzQuEWCbSfqDBSbKcR5wdfjLp79I4O4pc52Z5",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-24T17:01:17.57+08:00",
          "updated_at": "2025-07-24T09:01:18+08:00",
          "deleted": false
        },
        {
          "id": 1015,
          "order_id": "202507241615139527STRIPE1948295894398930944",
          "user_id": "338945122932625408",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T16:15:13.47+08:00",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "cs_test_a1SmVXn2YIHOt3NJAIMoaznKkbkuzJgqLADhLZoTX7JHch8MERKqvSzcML",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-24T16:15:13.47+08:00",
          "updated_at": "2025-07-24T08:15:18+08:00",
          "deleted": false
        },
        {
          "id": 1014,
          "order_id": "202507241610026378STRIPE1948294589513207808",
          "user_id": "338945122932625408",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T16:10:02.36+08:00",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "cs_test_a1xsLqxAkEdoKSdCU1Ip4ejVxd46vR3J841kwFwJidj9htHa41aTRq99E5",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-24T16:10:02.36+08:00",
          "updated_at": "2025-07-24T08:10:02+08:00",
          "deleted": false
        },
        {
          "id": 1013,
          "order_id": "202507241609366107STRIPE1948294482277437440",
          "user_id": "338945122932625408",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T16:09:36.793+08:00",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "cs_test_a1pLMbkp6DOU7yUeE0ql7mZ2esW2BZ8456qaVFDwBVMiIVg65j0LYQWJKH",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-24T16:09:36.794+08:00",
          "updated_at": "2025-07-24T08:09:37+08:00",
          "deleted": false
        },
        {
          "id": 1012,
          "order_id": "202507241608028961STRIPE1948294087442436096",
          "user_id": "338945122932625408",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T16:08:02.657+08:00",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "cs_test_a1jniiwP8QCpJSOHNvXvv5MottooqEBpZmpNKjHZQ35xVXrvZbAVeI6Qmy",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-24T16:08:02.657+08:00",
          "updated_at": "2025-07-24T08:08:03+08:00",
          "deleted": false
        },
        {
          "id": 1011,
          "order_id": "202507241607354240STRIPE1948293974062010368",
          "user_id": "338945122932625408",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T16:07:35.625+08:00",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "cs_test_a1xc4POnWWOAPregD7uDgNTqh0HdEJiJbw00a3bsvzTIaIoGWCt3XKIF0C",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-24T16:07:35.625+08:00",
          "updated_at": "2025-07-24T08:07:36+08:00",
          "deleted": false
        },
        {
          "id": 1010,
          "order_id": "202507241605053575STRIPE1948293345683968000",
          "user_id": "338945122932625408",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T16:05:05.809+08:00",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "cs_test_a1blY4uJ4usD4Q9z43hmfz7fJ2sPcwGqQcGLB8YtKVlVlaAbH95m39EEdu",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-24T16:05:05.809+08:00",
          "updated_at": "2025-07-24T08:05:07+08:00",
          "deleted": false
        },
        {
          "id": 1009,
          "order_id": "202507241559247712STRIPE1948291916202250240",
          "user_id": "338945122932625408",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T15:59:24.993+08:00",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "cs_test_a1HHBmNkb4JcLoTwdnhzG4on2hq8kVnkBCF8AkQwudYkeV4k8dZE9gf6eC",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-24T15:59:24.993+08:00",
          "updated_at": "2025-07-24T07:59:25+08:00",
          "deleted": false
        },
        {
          "id": 1008,
          "order_id": "202507241546236220STRIPE1948288638290759680",
          "user_id": "338945122932625408",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T15:46:23.478+08:00",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "cs_test_a15AxVpnErNjq1f4Hs2MyZRke9xUIOScI6jYVEdX0DC4H8dMb9oC8la9y2",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-24T15:46:23.478+08:00",
          "updated_at": "2025-07-24T07:46:24+08:00",
          "deleted": false
        },
        {
          "id": 1007,
          "order_id": "202507241541541236STRIPE1948287509238648832",
          "user_id": "338945122932625408",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T15:41:54.292+08:00",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "cs_test_a1V3PqLF6isxFcTpsD5YqqfOIpN0Tgqwx75KvfO1QyKrwGgtgmba1lotlO",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-24T15:41:54.292+08:00",
          "updated_at": "2025-07-24T07:41:54+08:00",
          "deleted": false
        },
        {
          "id": 1006,
          "order_id": "202507241540598713STRIPE1948287277369135104",
          "user_id": "338945122932625408",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T15:40:59.009+08:00",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "cs_test_a1HDucBJT6PbdcZypiF63h2oMTR3AOVOiVxG8b9DcnbNtd6UUvru57UuCw",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-24T15:40:59.009+08:00",
          "updated_at": "2025-07-24T07:40:59+08:00",
          "deleted": false
        },
        {
          "id": 1005,
          "order_id": "202507241539586297STRIPE1948287024184168448",
          "user_id": "338945122932625408",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T15:39:58.646+08:00",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "cs_test_a1eaumAR0pdHxgTZYtCuGFZlNNbghBpytAdBZDKLLpUZtXSoguYGp0IZuj",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-24T15:39:58.646+08:00",
          "updated_at": "2025-07-24T07:39:59+08:00",
          "deleted": false
        },
        {
          "id": 1004,
          "order_id": "202507241538467282STRIPE1948286722118782976",
          "user_id": "338945122932625408",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T15:38:46.627+08:00",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "cs_test_a1thqkjQfYZlkFEiLiExGqFirqSaoBWTyraUHySygkYi1LPnlo2jqOA6qN",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-24T15:38:46.627+08:00",
          "updated_at": "2025-07-24T07:38:47+08:00",
          "deleted": false
        },
        {
          "id": 1003,
          "order_id": "202507241536198994STRIPE1948286106109743104",
          "user_id": "338945122932625408",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T15:36:19.76+08:00",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "cs_test_a1geqTwXgqqqE5BZLnbslNN1JRgQ1o5A8y29uFuKwsrnFC7cs029s02spH",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-24T15:36:19.76+08:00",
          "updated_at": "2025-07-24T07:36:20+08:00",
          "deleted": false
        },
        {
          "id": 1002,
          "order_id": "202507241535072301STRIPE1948285804484759552",
          "user_id": "338945122932625408",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T15:35:07.847+08:00",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "cs_test_a1wn8apCHD4OGRg36keNa569kIC0nAqV0dxsCTyCBkg86MJ9plHd6dqbwC",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-24T15:35:07.847+08:00",
          "updated_at": "2025-07-24T07:35:08+08:00",
          "deleted": false
        },
        {
          "id": 1001,
          "order_id": "202507241530552257STRIPE1948284747675340800",
          "user_id": "338945122932625408",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "payed_at": "2025-07-24T15:30:55.883+08:00",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "cs_test_a1OHfL4zdxgPH8Fv0hODPHVNE5iaMDXh0SSMO6xGDftif7p11KCNbmbSDo",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-24T15:30:55.883+08:00",
          "updated_at": "2025-07-24T07:30:56+08:00",
          "deleted": false
        },
        {
          "id": 1000,
          "order_id": "202507191022365063STRIPE1946395217431105536",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-19T10:22:36.767+08:00",
          "updated_at": "2025-07-19T10:22:36.767+08:00",
          "deleted": false
        },
        {
          "id": 999,
          "order_id": "202507191022350789STRIPE1946395213131943936",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-19T10:22:35.742+08:00",
          "updated_at": "2025-07-19T10:22:35.742+08:00",
          "deleted": false
        },
        {
          "id": 998,
          "order_id": "202507191022340211STRIPE1946395208769867776",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-19T10:22:34.703+08:00",
          "updated_at": "2025-07-19T10:22:34.703+08:00",
          "deleted": false
        },
        {
          "id": 997,
          "order_id": "202507191022338351STRIPE1946395204588146688",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-19T10:22:33.706+08:00",
          "updated_at": "2025-07-19T10:22:33.706+08:00",
          "deleted": false
        },
        {
          "id": 996,
          "order_id": "202507191022324483STRIPE1946395200091852800",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-19T10:22:32.633+08:00",
          "updated_at": "2025-07-19T10:22:32.633+08:00",
          "deleted": false
        },
        {
          "id": 995,
          "order_id": "202507191022318162STRIPE1946395195801079808",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-19T10:22:31.61+08:00",
          "updated_at": "2025-07-19T10:22:31.61+08:00",
          "deleted": false
        },
        {
          "id": 994,
          "order_id": "202507191022308635STRIPE1946395190986018816",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-19T10:22:30.462+08:00",
          "updated_at": "2025-07-19T10:22:30.462+08:00",
          "deleted": false
        },
        {
          "id": 993,
          "order_id": "202507191022299573STRIPE1946395186653302784",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-19T10:22:29.43+08:00",
          "updated_at": "2025-07-19T10:22:29.43+08:00",
          "deleted": false
        },
        {
          "id": 992,
          "order_id": "202507191022287841STRIPE1946395182358335488",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-19T10:22:28.406+08:00",
          "updated_at": "2025-07-19T10:22:28.406+08:00",
          "deleted": false
        },
        {
          "id": 991,
          "order_id": "202507191022274873STRIPE1946395177799127040",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-19T10:22:27.318+08:00",
          "updated_at": "2025-07-19T10:22:27.318+08:00",
          "deleted": false
        },
        {
          "id": 990,
          "order_id": "202507191022261611STRIPE1946395173638377472",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-19T10:22:26.327+08:00",
          "updated_at": "2025-07-19T10:22:26.327+08:00",
          "deleted": false
        },
        {
          "id": 989,
          "order_id": "202507191022254266STRIPE1946395169448267776",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-19T10:22:25.327+08:00",
          "updated_at": "2025-07-19T10:22:25.327+08:00",
          "deleted": false
        },
        {
          "id": 988,
          "order_id": "202507191022247517STRIPE1946395165161689088",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-19T10:22:24.306+08:00",
          "updated_at": "2025-07-19T10:22:24.306+08:00",
          "deleted": false
        },
        {
          "id": 987,
          "order_id": "202507191022238390STRIPE1946395160795418624",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-19T10:22:23.264+08:00",
          "updated_at": "2025-07-19T10:22:23.264+08:00",
          "deleted": false
        },
        {
          "id": 986,
          "order_id": "202507191022227133STRIPE1946395156634669056",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-19T10:22:22.273+08:00",
          "updated_at": "2025-07-19T10:22:22.273+08:00",
          "deleted": false
        },
        {
          "id": 985,
          "order_id": "202507191022215039STRIPE1946395152520056832",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-19T10:22:21.292+08:00",
          "updated_at": "2025-07-19T10:22:21.292+08:00",
          "deleted": false
        },
        {
          "id": 984,
          "order_id": "202507191022208362STRIPE1946395148510302208",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-19T10:22:20.335+08:00",
          "updated_at": "2025-07-19T10:22:20.335+08:00",
          "deleted": false
        },
        {
          "id": 983,
          "order_id": "202507191022196212STRIPE1946395144135643136",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-19T10:22:19.293+08:00",
          "updated_at": "2025-07-19T10:22:19.293+08:00",
          "deleted": false
        },
        {
          "id": 982,
          "order_id": "202507191022185044STRIPE1946395139664515072",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-19T10:22:18.227+08:00",
          "updated_at": "2025-07-19T10:22:18.227+08:00",
          "deleted": false
        },
        {
          "id": 981,
          "order_id": "202507191022170600STRIPE1946395135432462336",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-19T10:22:17.218+08:00",
          "updated_at": "2025-07-19T10:22:17.218+08:00",
          "deleted": false
        },
        {
          "id": 980,
          "order_id": "202507191022168377STRIPE1946395130554486784",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-19T10:22:16.054+08:00",
          "updated_at": "2025-07-19T10:22:16.054+08:00",
          "deleted": false
        },
        {
          "id": 979,
          "order_id": "202507191022141319STRIPE1946395125957529600",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-19T10:22:14.959+08:00",
          "updated_at": "2025-07-19T10:22:14.959+08:00",
          "deleted": false
        },
        {
          "id": 978,
          "order_id": "202507191022135108STRIPE1946395121595453440",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-19T10:22:13.918+08:00",
          "updated_at": "2025-07-19T10:22:13.918+08:00",
          "deleted": false
        },
        {
          "id": 977,
          "order_id": "202507191022120093STRIPE1946395117128519680",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-19T10:22:12.854+08:00",
          "updated_at": "2025-07-19T10:22:12.854+08:00",
          "deleted": false
        },
        {
          "id": 976,
          "order_id": "202507191022110775STRIPE1946395113135542272",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-19T10:22:11.901+08:00",
          "updated_at": "2025-07-19T10:22:11.901+08:00",
          "deleted": false
        },
        {
          "id": 975,
          "order_id": "202507191022103867STRIPE1946395108538585088",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-19T10:22:10.806+08:00",
          "updated_at": "2025-07-19T10:22:10.806+08:00",
          "deleted": false
        },
        {
          "id": 974,
          "order_id": "202507191022096316STRIPE1946395104210063360",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-19T10:22:09.774+08:00",
          "updated_at": "2025-07-19T10:22:09.774+08:00",
          "deleted": false
        },
        {
          "id": 973,
          "order_id": "202507191022082521STRIPE1946395100120616960",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-19T10:22:08.799+08:00",
          "updated_at": "2025-07-19T10:22:08.799+08:00",
          "deleted": false
        },
        {
          "id": 972,
          "order_id": "202507191022077049STRIPE1946395095704014848",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-19T10:22:07.746+08:00",
          "updated_at": "2025-07-19T10:22:07.746+08:00",
          "deleted": false
        },
        {
          "id": 971,
          "order_id": "202507191022061628STRIPE1946395091581014016",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-19T10:22:06.762+08:00",
          "updated_at": "2025-07-19T10:22:06.762+08:00",
          "deleted": false
        },
        {
          "id": 970,
          "order_id": "202507191022059727STRIPE1946395087130857472",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-19T10:22:05.702+08:00",
          "updated_at": "2025-07-19T10:22:05.702+08:00",
          "deleted": false
        },
        {
          "id": 969,
          "order_id": "202507191022040892STRIPE1946395082231910400",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-19T10:22:04.534+08:00",
          "updated_at": "2025-07-19T10:22:04.534+08:00",
          "deleted": false
        },
        {
          "id": 968,
          "order_id": "202507191022038120STRIPE1946395077907582976",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "amount": 0,
          "net_amount": 0,
          "currency": "",
          "pay_status": "created",
          "pay_ret": "",
          "payed_method": "",
          "psp_provider": "stripe",
          "card_number": "",
          "refund_status": "none",
          "psp_product_id": "",
          "psp_product_desc": "",
          "psp_price_id": "",
          "psp_payment_id": "",
          "psp_payment_intent_id": "",
          "psp_payment_refund_id": "",
          "psp_payment_refund_ret": "",
          "psp_customer_id": "",
          "psp_customer_email": "",
          "psp_subscription_id": "",
          "created_at": "2025-07-19T10:22:03.502+08:00",
          "updated_at": "2025-07-19T10:22:03.502+08:00",
          "deleted": false
        }
      ],
      "pagination": {
        "total": 1017,
        "limit": 50,
        "offset": 0,
        "remaining": 967
      }
    }
2025/07/24 17:01:48 --- List All Orders (Internal Dubbo RPC) ---
2025/07/24 17:01:48 URL: http://*************:25907/com.aibook.payment.grpc.OrderService/ListAllOrders
2025/07/24 17:01:48 Headers: map[x-trace-id:test-**********170094300-**********]
2025/07/24 17:01:48 Request Body: map[pagination:map[limit:50 offset:0]]
2025/07/24 17:01:48 Response Status: 200
2025/07/24 17:01:48 Response Body: {
      "orders": [
        {
          "id": "1017",
          "order_id": "202507241701474541STRIPE1948307614014115840",
          "user_id": "338945122932625408",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "payed_at": "2025-07-24T09:01:47.644Z",
          "refund_status": "none",
          "psp_payment_id": "cs_test_a1Jf3AgqXZJaGMlr2BTN8yVaefNTLVNLqKESwBpj7rl1Myp4zVjkQo6Jif",
          "created_at": "2025-07-24T09:01:47.644Z",
          "updated_at": "2025-07-24T01:01:48Z"
        },
        {
          "id": "1016",
          "order_id": "202507241701178629STRIPE1948307487878811648",
          "user_id": "338945122932625408",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "payed_at": "2025-07-24T09:01:17.570Z",
          "refund_status": "none",
          "psp_payment_id": "cs_test_a1cwGG2iDmQ2rhFDBJNujwzQuEWCbSfqDBSbKcR5wdfjLp79I4O4pc52Z5",
          "created_at": "2025-07-24T09:01:17.570Z",
          "updated_at": "2025-07-24T01:01:18Z"
        },
        {
          "id": "1015",
          "order_id": "202507241615139527STRIPE1948295894398930944",
          "user_id": "338945122932625408",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "payed_at": "2025-07-24T08:15:13.470Z",
          "refund_status": "none",
          "psp_payment_id": "cs_test_a1SmVXn2YIHOt3NJAIMoaznKkbkuzJgqLADhLZoTX7JHch8MERKqvSzcML",
          "created_at": "2025-07-24T08:15:13.470Z",
          "updated_at": "2025-07-24T00:15:18Z"
        },
        {
          "id": "1014",
          "order_id": "202507241610026378STRIPE1948294589513207808",
          "user_id": "338945122932625408",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "payed_at": "2025-07-24T08:10:02.360Z",
          "refund_status": "none",
          "psp_payment_id": "cs_test_a1xsLqxAkEdoKSdCU1Ip4ejVxd46vR3J841kwFwJidj9htHa41aTRq99E5",
          "created_at": "2025-07-24T08:10:02.360Z",
          "updated_at": "2025-07-24T00:10:02Z"
        },
        {
          "id": "1013",
          "order_id": "202507241609366107STRIPE1948294482277437440",
          "user_id": "338945122932625408",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "payed_at": "2025-07-24T08:09:36.793Z",
          "refund_status": "none",
          "psp_payment_id": "cs_test_a1pLMbkp6DOU7yUeE0ql7mZ2esW2BZ8456qaVFDwBVMiIVg65j0LYQWJKH",
          "created_at": "2025-07-24T08:09:36.794Z",
          "updated_at": "2025-07-24T00:09:37Z"
        },
        {
          "id": "1012",
          "order_id": "202507241608028961STRIPE1948294087442436096",
          "user_id": "338945122932625408",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "payed_at": "2025-07-24T08:08:02.657Z",
          "refund_status": "none",
          "psp_payment_id": "cs_test_a1jniiwP8QCpJSOHNvXvv5MottooqEBpZmpNKjHZQ35xVXrvZbAVeI6Qmy",
          "created_at": "2025-07-24T08:08:02.657Z",
          "updated_at": "2025-07-24T00:08:03Z"
        },
        {
          "id": "1011",
          "order_id": "202507241607354240STRIPE1948293974062010368",
          "user_id": "338945122932625408",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "payed_at": "2025-07-24T08:07:35.625Z",
          "refund_status": "none",
          "psp_payment_id": "cs_test_a1xc4POnWWOAPregD7uDgNTqh0HdEJiJbw00a3bsvzTIaIoGWCt3XKIF0C",
          "created_at": "2025-07-24T08:07:35.625Z",
          "updated_at": "2025-07-24T00:07:36Z"
        },
        {
          "id": "1010",
          "order_id": "202507241605053575STRIPE1948293345683968000",
          "user_id": "338945122932625408",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "payed_at": "2025-07-24T08:05:05.809Z",
          "refund_status": "none",
          "psp_payment_id": "cs_test_a1blY4uJ4usD4Q9z43hmfz7fJ2sPcwGqQcGLB8YtKVlVlaAbH95m39EEdu",
          "created_at": "2025-07-24T08:05:05.809Z",
          "updated_at": "2025-07-24T00:05:07Z"
        },
        {
          "id": "1009",
          "order_id": "202507241559247712STRIPE1948291916202250240",
          "user_id": "338945122932625408",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "payed_at": "2025-07-24T07:59:24.993Z",
          "refund_status": "none",
          "psp_payment_id": "cs_test_a1HHBmNkb4JcLoTwdnhzG4on2hq8kVnkBCF8AkQwudYkeV4k8dZE9gf6eC",
          "created_at": "2025-07-24T07:59:24.993Z",
          "updated_at": "2025-07-23T23:59:25Z"
        },
        {
          "id": "1008",
          "order_id": "202507241546236220STRIPE1948288638290759680",
          "user_id": "338945122932625408",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "payed_at": "2025-07-24T07:46:23.478Z",
          "refund_status": "none",
          "psp_payment_id": "cs_test_a15AxVpnErNjq1f4Hs2MyZRke9xUIOScI6jYVEdX0DC4H8dMb9oC8la9y2",
          "created_at": "2025-07-24T07:46:23.478Z",
          "updated_at": "2025-07-23T23:46:24Z"
        },
        {
          "id": "1007",
          "order_id": "202507241541541236STRIPE1948287509238648832",
          "user_id": "338945122932625408",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "payed_at": "2025-07-24T07:41:54.292Z",
          "refund_status": "none",
          "psp_payment_id": "cs_test_a1V3PqLF6isxFcTpsD5YqqfOIpN0Tgqwx75KvfO1QyKrwGgtgmba1lotlO",
          "created_at": "2025-07-24T07:41:54.292Z",
          "updated_at": "2025-07-23T23:41:54Z"
        },
        {
          "id": "1006",
          "order_id": "202507241540598713STRIPE1948287277369135104",
          "user_id": "338945122932625408",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "payed_at": "2025-07-24T07:40:59.009Z",
          "refund_status": "none",
          "psp_payment_id": "cs_test_a1HDucBJT6PbdcZypiF63h2oMTR3AOVOiVxG8b9DcnbNtd6UUvru57UuCw",
          "created_at": "2025-07-24T07:40:59.009Z",
          "updated_at": "2025-07-23T23:40:59Z"
        },
        {
          "id": "1005",
          "order_id": "202507241539586297STRIPE1948287024184168448",
          "user_id": "338945122932625408",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "payed_at": "2025-07-24T07:39:58.646Z",
          "refund_status": "none",
          "psp_payment_id": "cs_test_a1eaumAR0pdHxgTZYtCuGFZlNNbghBpytAdBZDKLLpUZtXSoguYGp0IZuj",
          "created_at": "2025-07-24T07:39:58.646Z",
          "updated_at": "2025-07-23T23:39:59Z"
        },
        {
          "id": "1004",
          "order_id": "202507241538467282STRIPE1948286722118782976",
          "user_id": "338945122932625408",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "payed_at": "2025-07-24T07:38:46.627Z",
          "refund_status": "none",
          "psp_payment_id": "cs_test_a1thqkjQfYZlkFEiLiExGqFirqSaoBWTyraUHySygkYi1LPnlo2jqOA6qN",
          "created_at": "2025-07-24T07:38:46.627Z",
          "updated_at": "2025-07-23T23:38:47Z"
        },
        {
          "id": "1003",
          "order_id": "202507241536198994STRIPE1948286106109743104",
          "user_id": "338945122932625408",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "payed_at": "2025-07-24T07:36:19.760Z",
          "refund_status": "none",
          "psp_payment_id": "cs_test_a1geqTwXgqqqE5BZLnbslNN1JRgQ1o5A8y29uFuKwsrnFC7cs029s02spH",
          "created_at": "2025-07-24T07:36:19.760Z",
          "updated_at": "2025-07-23T23:36:20Z"
        },
        {
          "id": "1002",
          "order_id": "202507241535072301STRIPE1948285804484759552",
          "user_id": "338945122932625408",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "payed_at": "2025-07-24T07:35:07.847Z",
          "refund_status": "none",
          "psp_payment_id": "cs_test_a1wn8apCHD4OGRg36keNa569kIC0nAqV0dxsCTyCBkg86MJ9plHd6dqbwC",
          "created_at": "2025-07-24T07:35:07.847Z",
          "updated_at": "2025-07-23T23:35:08Z"
        },
        {
          "id": "1001",
          "order_id": "202507241530552257STRIPE1948284747675340800",
          "user_id": "338945122932625408",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "payed_at": "2025-07-24T07:30:55.883Z",
          "refund_status": "none",
          "psp_payment_id": "cs_test_a1OHfL4zdxgPH8Fv0hODPHVNE5iaMDXh0SSMO6xGDftif7p11KCNbmbSDo",
          "created_at": "2025-07-24T07:30:55.883Z",
          "updated_at": "2025-07-23T23:30:56Z"
        },
        {
          "id": "1000",
          "order_id": "202507191022365063STRIPE1946395217431105536",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "refund_status": "none",
          "created_at": "2025-07-19T02:22:36.767Z",
          "updated_at": "2025-07-19T02:22:36.767Z"
        },
        {
          "id": "999",
          "order_id": "202507191022350789STRIPE1946395213131943936",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "refund_status": "none",
          "created_at": "2025-07-19T02:22:35.742Z",
          "updated_at": "2025-07-19T02:22:35.742Z"
        },
        {
          "id": "998",
          "order_id": "202507191022340211STRIPE1946395208769867776",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "refund_status": "none",
          "created_at": "2025-07-19T02:22:34.703Z",
          "updated_at": "2025-07-19T02:22:34.703Z"
        },
        {
          "id": "997",
          "order_id": "202507191022338351STRIPE1946395204588146688",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "refund_status": "none",
          "created_at": "2025-07-19T02:22:33.706Z",
          "updated_at": "2025-07-19T02:22:33.706Z"
        },
        {
          "id": "996",
          "order_id": "202507191022324483STRIPE1946395200091852800",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "refund_status": "none",
          "created_at": "2025-07-19T02:22:32.633Z",
          "updated_at": "2025-07-19T02:22:32.633Z"
        },
        {
          "id": "995",
          "order_id": "202507191022318162STRIPE1946395195801079808",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "refund_status": "none",
          "created_at": "2025-07-19T02:22:31.610Z",
          "updated_at": "2025-07-19T02:22:31.610Z"
        },
        {
          "id": "994",
          "order_id": "202507191022308635STRIPE1946395190986018816",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "refund_status": "none",
          "created_at": "2025-07-19T02:22:30.462Z",
          "updated_at": "2025-07-19T02:22:30.462Z"
        },
        {
          "id": "993",
          "order_id": "202507191022299573STRIPE1946395186653302784",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "refund_status": "none",
          "created_at": "2025-07-19T02:22:29.430Z",
          "updated_at": "2025-07-19T02:22:29.430Z"
        },
        {
          "id": "992",
          "order_id": "202507191022287841STRIPE1946395182358335488",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "refund_status": "none",
          "created_at": "2025-07-19T02:22:28.406Z",
          "updated_at": "2025-07-19T02:22:28.406Z"
        },
        {
          "id": "991",
          "order_id": "202507191022274873STRIPE1946395177799127040",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "refund_status": "none",
          "created_at": "2025-07-19T02:22:27.318Z",
          "updated_at": "2025-07-19T02:22:27.318Z"
        },
        {
          "id": "990",
          "order_id": "202507191022261611STRIPE1946395173638377472",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "refund_status": "none",
          "created_at": "2025-07-19T02:22:26.327Z",
          "updated_at": "2025-07-19T02:22:26.327Z"
        },
        {
          "id": "989",
          "order_id": "202507191022254266STRIPE1946395169448267776",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "refund_status": "none",
          "created_at": "2025-07-19T02:22:25.327Z",
          "updated_at": "2025-07-19T02:22:25.327Z"
        },
        {
          "id": "988",
          "order_id": "202507191022247517STRIPE1946395165161689088",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "refund_status": "none",
          "created_at": "2025-07-19T02:22:24.306Z",
          "updated_at": "2025-07-19T02:22:24.306Z"
        },
        {
          "id": "987",
          "order_id": "202507191022238390STRIPE1946395160795418624",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "refund_status": "none",
          "created_at": "2025-07-19T02:22:23.264Z",
          "updated_at": "2025-07-19T02:22:23.264Z"
        },
        {
          "id": "986",
          "order_id": "202507191022227133STRIPE1946395156634669056",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "refund_status": "none",
          "created_at": "2025-07-19T02:22:22.273Z",
          "updated_at": "2025-07-19T02:22:22.273Z"
        },
        {
          "id": "985",
          "order_id": "202507191022215039STRIPE1946395152520056832",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "refund_status": "none",
          "created_at": "2025-07-19T02:22:21.292Z",
          "updated_at": "2025-07-19T02:22:21.292Z"
        },
        {
          "id": "984",
          "order_id": "202507191022208362STRIPE1946395148510302208",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "refund_status": "none",
          "created_at": "2025-07-19T02:22:20.335Z",
          "updated_at": "2025-07-19T02:22:20.335Z"
        },
        {
          "id": "983",
          "order_id": "202507191022196212STRIPE1946395144135643136",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "refund_status": "none",
          "created_at": "2025-07-19T02:22:19.293Z",
          "updated_at": "2025-07-19T02:22:19.293Z"
        },
        {
          "id": "982",
          "order_id": "202507191022185044STRIPE1946395139664515072",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "refund_status": "none",
          "created_at": "2025-07-19T02:22:18.227Z",
          "updated_at": "2025-07-19T02:22:18.227Z"
        },
        {
          "id": "981",
          "order_id": "202507191022170600STRIPE1946395135432462336",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "refund_status": "none",
          "created_at": "2025-07-19T02:22:17.218Z",
          "updated_at": "2025-07-19T02:22:17.218Z"
        },
        {
          "id": "980",
          "order_id": "202507191022168377STRIPE1946395130554486784",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "refund_status": "none",
          "created_at": "2025-07-19T02:22:16.054Z",
          "updated_at": "2025-07-19T02:22:16.054Z"
        },
        {
          "id": "979",
          "order_id": "202507191022141319STRIPE1946395125957529600",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "refund_status": "none",
          "created_at": "2025-07-19T02:22:14.959Z",
          "updated_at": "2025-07-19T02:22:14.959Z"
        },
        {
          "id": "978",
          "order_id": "202507191022135108STRIPE1946395121595453440",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "refund_status": "none",
          "created_at": "2025-07-19T02:22:13.918Z",
          "updated_at": "2025-07-19T02:22:13.918Z"
        },
        {
          "id": "977",
          "order_id": "202507191022120093STRIPE1946395117128519680",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "refund_status": "none",
          "created_at": "2025-07-19T02:22:12.854Z",
          "updated_at": "2025-07-19T02:22:12.854Z"
        },
        {
          "id": "976",
          "order_id": "202507191022110775STRIPE1946395113135542272",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "refund_status": "none",
          "created_at": "2025-07-19T02:22:11.901Z",
          "updated_at": "2025-07-19T02:22:11.901Z"
        },
        {
          "id": "975",
          "order_id": "202507191022103867STRIPE1946395108538585088",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "refund_status": "none",
          "created_at": "2025-07-19T02:22:10.806Z",
          "updated_at": "2025-07-19T02:22:10.806Z"
        },
        {
          "id": "974",
          "order_id": "202507191022096316STRIPE1946395104210063360",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "refund_status": "none",
          "created_at": "2025-07-19T02:22:09.774Z",
          "updated_at": "2025-07-19T02:22:09.774Z"
        },
        {
          "id": "973",
          "order_id": "202507191022082521STRIPE1946395100120616960",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "refund_status": "none",
          "created_at": "2025-07-19T02:22:08.799Z",
          "updated_at": "2025-07-19T02:22:08.799Z"
        },
        {
          "id": "972",
          "order_id": "202507191022077049STRIPE1946395095704014848",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "refund_status": "none",
          "created_at": "2025-07-19T02:22:07.746Z",
          "updated_at": "2025-07-19T02:22:07.746Z"
        },
        {
          "id": "971",
          "order_id": "202507191022061628STRIPE1946395091581014016",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "refund_status": "none",
          "created_at": "2025-07-19T02:22:06.762Z",
          "updated_at": "2025-07-19T02:22:06.762Z"
        },
        {
          "id": "970",
          "order_id": "202507191022059727STRIPE1946395087130857472",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "refund_status": "none",
          "created_at": "2025-07-19T02:22:05.702Z",
          "updated_at": "2025-07-19T02:22:05.702Z"
        },
        {
          "id": "969",
          "order_id": "202507191022040892STRIPE1946395082231910400",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "refund_status": "none",
          "created_at": "2025-07-19T02:22:04.534Z",
          "updated_at": "2025-07-19T02:22:04.534Z"
        },
        {
          "id": "968",
          "order_id": "202507191022038120STRIPE1946395077907582976",
          "user_id": "user123",
          "product_id": "prod_stripe_001",
          "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
          "quantity": 1,
          "pay_status": "created",
          "psp_provider": "stripe",
          "refund_status": "none",
          "created_at": "2025-07-19T02:22:03.502Z",
          "updated_at": "2025-07-19T02:22:03.502Z"
        }
      ],
      "pagination": {
        "total": "1017",
        "limit": 50,
        "remaining": "967"
      }
    }
2025/07/24 17:01:48 --- Force Refund Order (Internal Gin HTTP) ---
2025/07/24 17:01:48 URL: http://*************:25906/api/v1/pay-service/admin/order-service/orders/20250710153045999stripe1234567890123456789/force-refund
2025/07/24 17:01:48 Headers: map[x-role:admin x-trace-id:test-**********375373700-********** x-user-id:admin123]
2025/07/24 17:01:48 Request Body: {Amount:<nil>}
2025/07/24 17:01:48 Response Status: 400
2025/07/24 17:01:48 Response Body: {
      "code": 10006,
      "message": "failed to process refund: order with order ID 20250710153045999stripe1234567890123456789 not found"
    }
2025/07/24 17:01:48 Request failed with HTTP 400: {"code":10006,"message":"failed to process refund: order with order ID 20250710153045999stripe1234567890123456789 not found"}
2025/07/24 17:01:48 --- Admin Add Packages (Internal Gin HTTP) ---
2025/07/24 17:01:48 URL: http://*************:25906/api/v1/pay-service/admin/store-service/packages
2025/07/24 17:01:48 Headers: map[x-role:admin x-trace-id:test-**********544303700-********** x-user-id:admin123]
2025/07/24 17:01:48 Request Body: {PackageName:测试流量包 PackageDesc:用于API测试的流量包 Entitlement:100 EntitlementDesc:100GB流量 OriginalPrice:19.99 DiscountPrice:<nil> DiscountStartTime:<nil> DiscountEndTime:<nil> DiscountDesc: SaleStatus:on_sale Currency:USD Country:US Extra1:test Extra2:api Extra3:100 Extra4:30}
2025/07/24 17:01:48 Response Status: 201
2025/07/24 17:01:48 Response Body: {
      "message": "Package created successfully"
    }
2025/07/24 17:01:48 --- Admin List All Packages (Internal Gin HTTP) ---
2025/07/24 17:01:48 URL: http://*************:25906/api/v1/pay-service/admin/store-service/packages?limit=50&offset=0
2025/07/24 17:01:48 Headers: map[x-role:admin x-trace-id:test-**********604854300-********** x-user-id:admin123]
2025/07/24 17:01:48 Response Status: 200
2025/07/24 17:01:48 Response Body: {
      "packages": [
        {
          "id": 2,
          "package_id": "b7a32001-cbe3-45b7-ab94-8dd88edc366b",
          "package_name": "测试流量包",
          "package_desc": "用于API测试的流量包",
          "entitlement": 100,
          "entitlement_desc": "100GB流量",
          "original_price": 19.99,
          "discount_price": null,
          "discount_start_time": null,
          "discount_end_time": null,
          "discount_desc": "",
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "US",
          "extra1": "test",
          "extra2": "api",
          "extra3": 100,
          "extra4": 30,
          "created_at": "2025-07-24T17:01:49.354+08:00",
          "updated_at": "2025-07-24T17:01:49.354+08:00"
        },
        {
          "id": 1,
          "package_id": "772f0f80-22e1-4a39-ba8c-d56178677d06",
          "package_name": "测试流量包",
          "package_desc": "用于API测试的流量包",
          "entitlement": 100,
          "entitlement_desc": "100GB流量",
          "original_price": 19.99,
          "discount_price": null,
          "discount_start_time": null,
          "discount_end_time": null,
          "discount_desc": "",
          "sale_status": "on_sale",
          "currency": "USD",
          "country": "US",
          "extra1": "test",
          "extra2": "api",
          "extra3": 100,
          "extra4": 30,
          "created_at": "2025-07-24T17:01:19.187+08:00",
          "updated_at": "2025-07-24T17:01:19.187+08:00"
        }
      ],
      "pagination": {
        "total": 2,
        "limit": 50,
        "offset": 0,
        "remaining": 0
      }
    }
2025/07/24 17:01:48 --- Admin Update Packages (Internal Gin HTTP) ---
2025/07/24 17:01:48 URL: http://*************:25906/api/v1/pay-service/admin/store-service/packages
2025/07/24 17:01:48 Headers: map[x-role:admin x-trace-id:test-**********695011100-********** x-user-id:admin123]
2025/07/24 17:01:48 Request Body: {PackageID:test-package-id-replace-with-real-one PackageName:0xc00019e390 PackageDesc:<nil> Entitlement:<nil> EntitlementDesc:<nil> OriginalPrice:0xc00008c310 DiscountPrice:<nil> DiscountStartTime:<nil> DiscountEndTime:<nil> DiscountDesc:<nil> SaleStatus:<nil> Currency:<nil> Country:<nil> Extra1:<nil> Extra2:<nil> Extra3:<nil> Extra4:<nil>}
2025/07/24 17:01:48 Response Status: 500
2025/07/24 17:01:48 Response Body: {
      "code": 10006,
      "message": "Failed to update package"
    }
2025/07/24 17:01:48 Request failed with HTTP 500: {"code":10006,"message":"Failed to update package"}
2025/07/24 17:01:48 --- Admin Delete Packages (Internal Gin HTTP) ---
2025/07/24 17:01:48 URL: http://*************:25906/api/v1/pay-service/admin/store-service/packages
2025/07/24 17:01:48 Headers: map[x-role:admin x-trace-id:test-**********747963900-********** x-user-id:admin123]
2025/07/24 17:01:48 Request Body: {PackageID:test-package-id-replace-with-real-one Currency:USD Country:US}
2025/07/24 17:01:48 Response Status: 200
2025/07/24 17:01:48 Response Body: {
      "message": "Package deleted successfully"
    }
2025/07/24 17:01:48 --- Admin Add Packages (Internal Dubbo RPC) ---
2025/07/24 17:01:48 URL: http://*************:25907/com.aibook.payment.grpc.StoreService/AdminAddPackages
2025/07/24 17:01:48 Headers: map[x-trace-id:test-**********802977900-**********]
2025/07/24 17:01:48 Request Body: map[country:US currency:USD discount_desc: entitlement:100 entitlement_desc:100GB流量 extra1:rpc extra2:test extra3:100 extra4:30 original_price:19.99 package_desc:用于RPC API测试的流量包 package_name:RPC测试流量包 sale_status:on_sale]
2025/07/24 17:01:48 Response Status: 404
2025/07/24 17:01:48 Response Body: 404 page not found
2025/07/24 17:01:48 Request failed with HTTP 404: 404 page not found
2025/07/24 17:01:48 --- Admin List All Packages (Internal Dubbo RPC) ---
2025/07/24 17:01:48 URL: http://*************:25907/com.aibook.payment.grpc.StoreService/AdminListAllPackages
2025/07/24 17:01:48 Headers: map[x-trace-id:test-**********853926400-**********]
2025/07/24 17:01:48 Request Body: map[pagination:map[limit:50 offset:0]]
2025/07/24 17:01:48 Response Status: 404
2025/07/24 17:01:48 Response Body: 404 page not found
2025/07/24 17:01:48 Request failed with HTTP 404: 404 page not found
2025/07/24 17:01:48 --- Admin Update Packages (Internal Dubbo RPC) ---
2025/07/24 17:01:48 URL: http://*************:25907/com.aibook.payment.grpc.StoreService/AdminUpdatePackages
2025/07/24 17:01:48 Headers: map[x-trace-id:test-**********936692900-**********]
2025/07/24 17:01:48 Request Body: map[original_price:24.99 package_id:test-package-id-replace-with-real-one package_name:RPC更新后的测试流量包]
2025/07/24 17:01:49 Response Status: 404
2025/07/24 17:01:49 Response Body: 404 page not found
2025/07/24 17:01:49 Request failed with HTTP 404: 404 page not found
2025/07/24 17:01:49 --- Admin Delete Packages (Internal Dubbo RPC) ---
2025/07/24 17:01:49 URL: http://*************:25907/com.aibook.payment.grpc.StoreService/AdminDeletePackages
2025/07/24 17:01:49 Headers: map[x-trace-id:test-1753347709065067700-1753347709]
2025/07/24 17:01:49 Request Body: map[country:US currency:USD package_id:test-package-id-replace-with-real-one]
2025/07/24 17:01:49 Response Status: 404
2025/07/24 17:01:49 Response Body: 404 page not found
2025/07/24 17:01:49 Request failed with HTTP 404: 404 page not found
2025/07/24 17:01:49 --------------- Finished test for dev2 ---------------
2025/07/24 17:01:49 === Starting Test Summary ===


##################### EACH TEST SUMMARY #####################

=== Test Summary for dev1 ===
✅ PASS Get External Token (0.00s) - Token obtained successfully
✅ PASS Create Order (External Gin HTTP) (0.71s) - Order created successfully
✅ PASS Get User Orders (External Gin HTTP) (0.06s) - User orders retrieved successfully
✅ PASS List All Packages (External Gin HTTP) (0.07s) - Packages retrieved successfully via External Gin HTTP
✅ PASS List All Packages with Filters (External Gin HTTP) (0.06s) - Filtered packages retrieved successfully via External Gin HTTP
✅ PASS List All Orders (Internal Gin HTTP) (0.16s) - All orders retrieved successfully via Gin HTTP
✅ PASS List All Orders (Internal Dubbo RPC) (0.17s) - All orders retrieved successfully via Dubbo RPC
❌ FAIL Force Refund Order (Internal Gin HTTP) (0.10s) - HTTP 400: {"code":10006,"message":"failed to process refund: order with order ID 20250710153045999stripe1234567890123456789 not found"}
✅ PASS Admin Add Packages (Internal Gin HTTP) (0.07s) - Package created successfully via Gin HTTP
✅ PASS Admin List All Packages (Internal Gin HTTP) (0.09s) - Admin packages retrieved successfully via Gin HTTP
❌ FAIL Admin Update Packages (Internal Gin HTTP) (0.10s) - HTTP 500: {"code":10006,"message":"Failed to update package"}
✅ PASS Admin Delete Packages (Internal Gin HTTP) (0.06s) - Package deleted successfully via Gin HTTP
❌ FAIL Admin Add Packages (Internal Dubbo RPC) (0.05s) - HTTP 404: 404 page not found
❌ FAIL Admin List All Packages (Internal Dubbo RPC) (0.08s) - HTTP 404: 404 page not found
❌ FAIL Admin Update Packages (Internal Dubbo RPC) (0.05s) - HTTP 404: 404 page not found
❌ FAIL Admin Delete Packages (Internal Dubbo RPC) (0.05s) - HTTP 404: 404 page not found

Results: 10/16 tests passed
⚠️  6 tests failed


=== Test Summary for dev2 ===
✅ PASS Get External Token (0.00s) - Token obtained successfully
✅ PASS Create Order (External Gin HTTP) (0.93s) - Order created successfully
✅ PASS Get User Orders (External Gin HTTP) (0.06s) - User orders retrieved successfully
✅ PASS List All Packages (External Gin HTTP) (0.05s) - Packages retrieved successfully via External Gin HTTP
✅ PASS List All Packages with Filters (External Gin HTTP) (0.05s) - Filtered packages retrieved successfully via External Gin HTTP
✅ PASS List All Orders (Internal Gin HTTP) (0.13s) - All orders retrieved successfully via Gin HTTP
✅ PASS List All Orders (Internal Dubbo RPC) (0.20s) - All orders retrieved successfully via Dubbo RPC
❌ FAIL Force Refund Order (Internal Gin HTTP) (0.17s) - HTTP 400: {"code":10006,"message":"failed to process refund: order with order ID 20250710153045999stripe1234567890123456789 not found"}
✅ PASS Admin Add Packages (Internal Gin HTTP) (0.06s) - Package created successfully via Gin HTTP
✅ PASS Admin List All Packages (Internal Gin HTTP) (0.09s) - Admin packages retrieved successfully via Gin HTTP
❌ FAIL Admin Update Packages (Internal Gin HTTP) (0.05s) - HTTP 500: {"code":10006,"message":"Failed to update package"}
✅ PASS Admin Delete Packages (Internal Gin HTTP) (0.06s) - Package deleted successfully via Gin HTTP
❌ FAIL Admin Add Packages (Internal Dubbo RPC) (0.05s) - HTTP 404: 404 page not found
❌ FAIL Admin List All Packages (Internal Dubbo RPC) (0.08s) - HTTP 404: 404 page not found
❌ FAIL Admin Update Packages (Internal Dubbo RPC) (0.13s) - HTTP 404: 404 page not found
❌ FAIL Admin Delete Packages (Internal Dubbo RPC) (0.08s) - HTTP 404: 404 page not found

Results: 10/16 tests passed
⚠️  6 tests failed

2025/07/24 17:01:49 === Test Session Ended at 2025-07-24 17:01:49 ===
