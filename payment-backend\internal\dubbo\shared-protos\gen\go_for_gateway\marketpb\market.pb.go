// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v6.31.1
// source: protos/market.proto

package marketpb

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SearchBookRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SearchStr string `protobuf:"bytes,1,opt,name=search_str,json=searchStr,proto3" json:"search_str,omitempty"`
	Page      uint32 `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	PageSize  uint32 `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
}

func (x *SearchBookRequest) Reset() {
	*x = SearchBookRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_market_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchBookRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchBookRequest) ProtoMessage() {}

func (x *SearchBookRequest) ProtoReflect() protoreflect.Message {
	mi := &file_protos_market_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchBookRequest.ProtoReflect.Descriptor instead.
func (*SearchBookRequest) Descriptor() ([]byte, []int) {
	return file_protos_market_proto_rawDescGZIP(), []int{0}
}

func (x *SearchBookRequest) GetSearchStr() string {
	if x != nil {
		return x.SearchStr
	}
	return ""
}

func (x *SearchBookRequest) GetPage() uint32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *SearchBookRequest) GetPageSize() uint32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type SearchBookResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Books    []*BookInfo `protobuf:"bytes,1,rep,name=books,proto3" json:"books,omitempty"`
	Page     uint32      `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	PageSize uint32      `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	Total    uint32      `protobuf:"varint,4,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *SearchBookResponse) Reset() {
	*x = SearchBookResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_market_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchBookResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchBookResponse) ProtoMessage() {}

func (x *SearchBookResponse) ProtoReflect() protoreflect.Message {
	mi := &file_protos_market_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchBookResponse.ProtoReflect.Descriptor instead.
func (*SearchBookResponse) Descriptor() ([]byte, []int) {
	return file_protos_market_proto_rawDescGZIP(), []int{1}
}

func (x *SearchBookResponse) GetBooks() []*BookInfo {
	if x != nil {
		return x.Books
	}
	return nil
}

func (x *SearchBookResponse) GetPage() uint32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *SearchBookResponse) GetPageSize() uint32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *SearchBookResponse) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type BookModifyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BookId      uint64  `protobuf:"varint,1,opt,name=book_id,json=bookId,proto3" json:"book_id,omitempty"` // 绘本ID
	Title       string  `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`                  // 绘本标题
	Description string  `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`      // 绘本描述
	Cover       string  `protobuf:"bytes,4,opt,name=cover,proto3" json:"cover,omitempty"`                  // 绘本封面
	Themeids    []int32 `protobuf:"varint,5,rep,packed,name=themeids,proto3" json:"themeids,omitempty"`    // 绘本主题
}

func (x *BookModifyRequest) Reset() {
	*x = BookModifyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_market_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BookModifyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BookModifyRequest) ProtoMessage() {}

func (x *BookModifyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_protos_market_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BookModifyRequest.ProtoReflect.Descriptor instead.
func (*BookModifyRequest) Descriptor() ([]byte, []int) {
	return file_protos_market_proto_rawDescGZIP(), []int{2}
}

func (x *BookModifyRequest) GetBookId() uint64 {
	if x != nil {
		return x.BookId
	}
	return 0
}

func (x *BookModifyRequest) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *BookModifyRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *BookModifyRequest) GetCover() string {
	if x != nil {
		return x.Cover
	}
	return ""
}

func (x *BookModifyRequest) GetThemeids() []int32 {
	if x != nil {
		return x.Themeids
	}
	return nil
}

type BookDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BookId uint64 `protobuf:"varint,1,opt,name=book_id,json=bookId,proto3" json:"book_id,omitempty"`
}

func (x *BookDetailRequest) Reset() {
	*x = BookDetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_market_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BookDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BookDetailRequest) ProtoMessage() {}

func (x *BookDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_protos_market_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BookDetailRequest.ProtoReflect.Descriptor instead.
func (*BookDetailRequest) Descriptor() ([]byte, []int) {
	return file_protos_market_proto_rawDescGZIP(), []int{3}
}

func (x *BookDetailRequest) GetBookId() uint64 {
	if x != nil {
		return x.BookId
	}
	return 0
}

type BookDetailResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BookInfo *BookInfo `protobuf:"bytes,1,opt,name=book_info,json=bookInfo,proto3" json:"book_info,omitempty"`
}

func (x *BookDetailResponse) Reset() {
	*x = BookDetailResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_market_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BookDetailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BookDetailResponse) ProtoMessage() {}

func (x *BookDetailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_protos_market_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BookDetailResponse.ProtoReflect.Descriptor instead.
func (*BookDetailResponse) Descriptor() ([]byte, []int) {
	return file_protos_market_proto_rawDescGZIP(), []int{4}
}

func (x *BookDetailResponse) GetBookInfo() *BookInfo {
	if x != nil {
		return x.BookInfo
	}
	return nil
}

type BookInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BookId         uint64 `protobuf:"varint,1,opt,name=book_id,json=bookId,proto3" json:"book_id,omitempty"`                         // 绘本ID
	Title          string `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`                                          // 绘本标题
	Description    string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`                              // 绘本描述
	Cover          string `protobuf:"bytes,4,opt,name=cover,proto3" json:"cover,omitempty"`                                          // 绘本封面
	RecommendScore uint32 `protobuf:"varint,5,opt,name=recommend_score,json=recommendScore,proto3" json:"recommend_score,omitempty"` // 绘本推荐分数
	DownloadCount  uint32 `protobuf:"varint,6,opt,name=download_count,json=downloadCount,proto3" json:"download_count,omitempty"`    // 绘本下载次数
	CreateAt       uint32 `protobuf:"varint,7,opt,name=create_at,json=createAt,proto3" json:"create_at,omitempty"`                   // 绘本进入绘本市场时间
	UpdateAt       uint32 `protobuf:"varint,8,opt,name=update_at,json=updateAt,proto3" json:"update_at,omitempty"`                   // 最后更新时间
}

func (x *BookInfo) Reset() {
	*x = BookInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_market_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BookInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BookInfo) ProtoMessage() {}

func (x *BookInfo) ProtoReflect() protoreflect.Message {
	mi := &file_protos_market_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BookInfo.ProtoReflect.Descriptor instead.
func (*BookInfo) Descriptor() ([]byte, []int) {
	return file_protos_market_proto_rawDescGZIP(), []int{5}
}

func (x *BookInfo) GetBookId() uint64 {
	if x != nil {
		return x.BookId
	}
	return 0
}

func (x *BookInfo) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *BookInfo) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *BookInfo) GetCover() string {
	if x != nil {
		return x.Cover
	}
	return ""
}

func (x *BookInfo) GetRecommendScore() uint32 {
	if x != nil {
		return x.RecommendScore
	}
	return 0
}

func (x *BookInfo) GetDownloadCount() uint32 {
	if x != nil {
		return x.DownloadCount
	}
	return 0
}

func (x *BookInfo) GetCreateAt() uint32 {
	if x != nil {
		return x.CreateAt
	}
	return 0
}

func (x *BookInfo) GetUpdateAt() uint32 {
	if x != nil {
		return x.UpdateAt
	}
	return 0
}

type ThemeBookTopkRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ThemeId uint64 `protobuf:"varint,1,opt,name=theme_id,json=themeId,proto3" json:"theme_id,omitempty"`
}

func (x *ThemeBookTopkRequest) Reset() {
	*x = ThemeBookTopkRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_market_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ThemeBookTopkRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThemeBookTopkRequest) ProtoMessage() {}

func (x *ThemeBookTopkRequest) ProtoReflect() protoreflect.Message {
	mi := &file_protos_market_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThemeBookTopkRequest.ProtoReflect.Descriptor instead.
func (*ThemeBookTopkRequest) Descriptor() ([]byte, []int) {
	return file_protos_market_proto_rawDescGZIP(), []int{6}
}

func (x *ThemeBookTopkRequest) GetThemeId() uint64 {
	if x != nil {
		return x.ThemeId
	}
	return 0
}

type ThemeBookTopkResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Books []*BookInfo `protobuf:"bytes,1,rep,name=books,proto3" json:"books,omitempty"` // 绘本数组
}

func (x *ThemeBookTopkResponse) Reset() {
	*x = ThemeBookTopkResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_market_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ThemeBookTopkResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThemeBookTopkResponse) ProtoMessage() {}

func (x *ThemeBookTopkResponse) ProtoReflect() protoreflect.Message {
	mi := &file_protos_market_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThemeBookTopkResponse.ProtoReflect.Descriptor instead.
func (*ThemeBookTopkResponse) Descriptor() ([]byte, []int) {
	return file_protos_market_proto_rawDescGZIP(), []int{7}
}

func (x *ThemeBookTopkResponse) GetBooks() []*BookInfo {
	if x != nil {
		return x.Books
	}
	return nil
}

type ThemeTopkResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Themes []*Theme `protobuf:"bytes,1,rep,name=themes,proto3" json:"themes,omitempty"` // 绘本主题数组
}

func (x *ThemeTopkResponse) Reset() {
	*x = ThemeTopkResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_market_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ThemeTopkResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThemeTopkResponse) ProtoMessage() {}

func (x *ThemeTopkResponse) ProtoReflect() protoreflect.Message {
	mi := &file_protos_market_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThemeTopkResponse.ProtoReflect.Descriptor instead.
func (*ThemeTopkResponse) Descriptor() ([]byte, []int) {
	return file_protos_market_proto_rawDescGZIP(), []int{8}
}

func (x *ThemeTopkResponse) GetThemes() []*Theme {
	if x != nil {
		return x.Themes
	}
	return nil
}

type Theme struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ThemeId        uint64 `protobuf:"varint,1,opt,name=theme_id,json=themeId,proto3" json:"theme_id,omitempty"`                      // 绘本主题ID
	Name           string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                                            // 绘本主题名称
	Description    string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`                              // 绘本主题描述
	Cover          string `protobuf:"bytes,4,opt,name=cover,proto3" json:"cover,omitempty"`                                          // 绘本主题封面
	RecommendScore uint32 `protobuf:"varint,5,opt,name=recommend_score,json=recommendScore,proto3" json:"recommend_score,omitempty"` // 绘本主题推荐分数
	BookCount      uint32 `protobuf:"varint,6,opt,name=book_count,json=bookCount,proto3" json:"book_count,omitempty"`                // 绘本数量
}

func (x *Theme) Reset() {
	*x = Theme{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_market_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Theme) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Theme) ProtoMessage() {}

func (x *Theme) ProtoReflect() protoreflect.Message {
	mi := &file_protos_market_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Theme.ProtoReflect.Descriptor instead.
func (*Theme) Descriptor() ([]byte, []int) {
	return file_protos_market_proto_rawDescGZIP(), []int{9}
}

func (x *Theme) GetThemeId() uint64 {
	if x != nil {
		return x.ThemeId
	}
	return 0
}

func (x *Theme) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Theme) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Theme) GetCover() string {
	if x != nil {
		return x.Cover
	}
	return ""
}

func (x *Theme) GetRecommendScore() uint32 {
	if x != nil {
		return x.RecommendScore
	}
	return 0
}

func (x *Theme) GetBookCount() uint32 {
	if x != nil {
		return x.BookCount
	}
	return 0
}

type Empty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Empty) Reset() {
	*x = Empty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_market_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Empty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Empty) ProtoMessage() {}

func (x *Empty) ProtoReflect() protoreflect.Message {
	mi := &file_protos_market_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Empty.ProtoReflect.Descriptor instead.
func (*Empty) Descriptor() ([]byte, []int) {
	return file_protos_market_proto_rawDescGZIP(), []int{10}
}

type ShareBookRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BookId uint64 `protobuf:"varint,1,opt,name=book_id,json=bookId,proto3" json:"book_id,omitempty"` // 绘本ID
}

func (x *ShareBookRequest) Reset() {
	*x = ShareBookRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_market_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ShareBookRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShareBookRequest) ProtoMessage() {}

func (x *ShareBookRequest) ProtoReflect() protoreflect.Message {
	mi := &file_protos_market_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShareBookRequest.ProtoReflect.Descriptor instead.
func (*ShareBookRequest) Descriptor() ([]byte, []int) {
	return file_protos_market_proto_rawDescGZIP(), []int{11}
}

func (x *ShareBookRequest) GetBookId() uint64 {
	if x != nil {
		return x.BookId
	}
	return 0
}

var File_protos_market_proto protoreflect.FileDescriptor

var file_protos_market_proto_rawDesc = []byte{
	0x0a, 0x13, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x75, 0x73, 0x65, 0x72, 0x1a, 0x1c, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x63, 0x0a, 0x11, 0x53, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d,
	0x0a, 0x0a, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x73, 0x74, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x53, 0x74, 0x72, 0x12, 0x12, 0x0a,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0x81,
	0x01, 0x0a, 0x12, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x24, 0x0a, 0x05, 0x62, 0x6f, 0x6f, 0x6b, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x42, 0x6f, 0x6f, 0x6b,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x62, 0x6f, 0x6f, 0x6b, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12,
	0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x22, 0x96, 0x01, 0x0a, 0x11, 0x42, 0x6f, 0x6f, 0x6b, 0x4d, 0x6f, 0x64, 0x69, 0x66,
	0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x62, 0x6f, 0x6f, 0x6b,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x62, 0x6f, 0x6f, 0x6b, 0x49,
	0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x76,
	0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x12,
	0x1a, 0x0a, 0x08, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x69, 0x64, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x05, 0x52, 0x08, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x69, 0x64, 0x73, 0x22, 0x2c, 0x0a, 0x11, 0x42,
	0x6f, 0x6f, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x17, 0x0a, 0x07, 0x62, 0x6f, 0x6f, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x06, 0x62, 0x6f, 0x6f, 0x6b, 0x49, 0x64, 0x22, 0x41, 0x0a, 0x12, 0x42, 0x6f, 0x6f,
	0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x2b, 0x0a, 0x09, 0x62, 0x6f, 0x6f, 0x6b, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x08, 0x62, 0x6f, 0x6f, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xfb, 0x01, 0x0a,
	0x08, 0x42, 0x6f, 0x6f, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17, 0x0a, 0x07, 0x62, 0x6f, 0x6f,
	0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x62, 0x6f, 0x6f, 0x6b,
	0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f,
	0x76, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x6f, 0x76, 0x65, 0x72,
	0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x5f, 0x73, 0x63,
	0x6f, 0x72, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x72, 0x65, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x64, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x64, 0x6f, 0x77,
	0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0d, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x1b, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x74, 0x12, 0x1b, 0x0a,
	0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x08, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x74, 0x22, 0x31, 0x0a, 0x14, 0x54, 0x68,
	0x65, 0x6d, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x54, 0x6f, 0x70, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x49, 0x64, 0x22, 0x3d, 0x0a,
	0x15, 0x54, 0x68, 0x65, 0x6d, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x54, 0x6f, 0x70, 0x6b, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x24, 0x0a, 0x05, 0x62, 0x6f, 0x6f, 0x6b, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x42, 0x6f, 0x6f,
	0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x62, 0x6f, 0x6f, 0x6b, 0x73, 0x22, 0x38, 0x0a, 0x11,
	0x54, 0x68, 0x65, 0x6d, 0x65, 0x54, 0x6f, 0x70, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x54, 0x68, 0x65, 0x6d, 0x65, 0x52, 0x06,
	0x74, 0x68, 0x65, 0x6d, 0x65, 0x73, 0x22, 0xb6, 0x01, 0x0a, 0x05, 0x54, 0x68, 0x65, 0x6d, 0x65,
	0x12, 0x19, 0x0a, 0x08, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x07, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x64, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x0e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x53, 0x63, 0x6f, 0x72, 0x65,
	0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x6f, 0x6f, 0x6b, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x62, 0x6f, 0x6f, 0x6b, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22,
	0x07, 0x0a, 0x05, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x2b, 0x0a, 0x10, 0x53, 0x68, 0x61, 0x72,
	0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07,
	0x62, 0x6f, 0x6f, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x62,
	0x6f, 0x6f, 0x6b, 0x49, 0x64, 0x32, 0xce, 0x04, 0x0a, 0x0d, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x51, 0x0a, 0x09, 0x53, 0x68, 0x61, 0x72, 0x65,
	0x42, 0x6f, 0x6f, 0x6b, 0x12, 0x16, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x53, 0x68, 0x61, 0x72,
	0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x0b, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x1f, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x19, 0x3a, 0x01, 0x2a, 0x22, 0x14, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x61,
	0x72, 0x6b, 0x65, 0x74, 0x2f, 0x73, 0x68, 0x61, 0x72, 0x65, 0x12, 0x66, 0x0a, 0x0a, 0x42, 0x6f,
	0x6f, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x17, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e,
	0x42, 0x6f, 0x6f, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x18, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x25, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x1f, 0x12, 0x1d, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x61, 0x72,
	0x6b, 0x65, 0x74, 0x2f, 0x62, 0x6f, 0x6f, 0x6b, 0x2f, 0x7b, 0x62, 0x6f, 0x6f, 0x6b, 0x5f, 0x69,
	0x64, 0x7d, 0x12, 0x5c, 0x0a, 0x0a, 0x42, 0x6f, 0x6f, 0x6b, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79,
	0x12, 0x17, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x42, 0x6f, 0x6f, 0x6b, 0x4d, 0x6f, 0x64, 0x69,
	0x66, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x0b, 0x2e, 0x75, 0x73, 0x65, 0x72,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x28, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x22, 0x3a, 0x01,
	0x2a, 0x22, 0x1d, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x61, 0x72, 0x6b, 0x65,
	0x74, 0x2f, 0x62, 0x6f, 0x6f, 0x6b, 0x2f, 0x7b, 0x62, 0x6f, 0x6f, 0x6b, 0x5f, 0x69, 0x64, 0x7d,
	0x12, 0x5c, 0x0a, 0x0a, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x42, 0x6f, 0x6f, 0x6b, 0x12, 0x17,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x42, 0x6f, 0x6f, 0x6b,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x1b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x15, 0x12, 0x13, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x31, 0x2f, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x2f, 0x62, 0x6f, 0x6f, 0x6b, 0x12, 0x54,
	0x0a, 0x09, 0x54, 0x68, 0x65, 0x6d, 0x65, 0x54, 0x6f, 0x70, 0x6b, 0x12, 0x0b, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x17, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e,
	0x54, 0x68, 0x65, 0x6d, 0x65, 0x54, 0x6f, 0x70, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x21, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1b, 0x12, 0x19, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x31, 0x2f, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x2f, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x2f,
	0x74, 0x6f, 0x70, 0x6b, 0x12, 0x70, 0x0a, 0x0d, 0x54, 0x68, 0x65, 0x6d, 0x65, 0x42, 0x6f, 0x6f,
	0x6b, 0x54, 0x6f, 0x70, 0x6b, 0x12, 0x1a, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x54, 0x68, 0x65,
	0x6d, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x54, 0x6f, 0x70, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x1b, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x54, 0x68, 0x65, 0x6d, 0x65, 0x42, 0x6f,
	0x6f, 0x6b, 0x54, 0x6f, 0x70, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x26,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x20, 0x12, 0x1e, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f,
	0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x2f, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x2f, 0x62, 0x6f, 0x6f,
	0x6b, 0x2f, 0x74, 0x6f, 0x70, 0x6b, 0x42, 0x15, 0x5a, 0x13, 0x2e, 0x2f, 0x6d, 0x61, 0x72, 0x6b,
	0x65, 0x74, 0x70, 0x62, 0x3b, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x70, 0x62, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_protos_market_proto_rawDescOnce sync.Once
	file_protos_market_proto_rawDescData = file_protos_market_proto_rawDesc
)

func file_protos_market_proto_rawDescGZIP() []byte {
	file_protos_market_proto_rawDescOnce.Do(func() {
		file_protos_market_proto_rawDescData = protoimpl.X.CompressGZIP(file_protos_market_proto_rawDescData)
	})
	return file_protos_market_proto_rawDescData
}

var file_protos_market_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_protos_market_proto_goTypes = []interface{}{
	(*SearchBookRequest)(nil),     // 0: user.SearchBookRequest
	(*SearchBookResponse)(nil),    // 1: user.SearchBookResponse
	(*BookModifyRequest)(nil),     // 2: user.BookModifyRequest
	(*BookDetailRequest)(nil),     // 3: user.BookDetailRequest
	(*BookDetailResponse)(nil),    // 4: user.BookDetailResponse
	(*BookInfo)(nil),              // 5: user.BookInfo
	(*ThemeBookTopkRequest)(nil),  // 6: user.ThemeBookTopkRequest
	(*ThemeBookTopkResponse)(nil), // 7: user.ThemeBookTopkResponse
	(*ThemeTopkResponse)(nil),     // 8: user.ThemeTopkResponse
	(*Theme)(nil),                 // 9: user.Theme
	(*Empty)(nil),                 // 10: user.Empty
	(*ShareBookRequest)(nil),      // 11: user.ShareBookRequest
}
var file_protos_market_proto_depIdxs = []int32{
	5,  // 0: user.SearchBookResponse.books:type_name -> user.BookInfo
	5,  // 1: user.BookDetailResponse.book_info:type_name -> user.BookInfo
	5,  // 2: user.ThemeBookTopkResponse.books:type_name -> user.BookInfo
	9,  // 3: user.ThemeTopkResponse.themes:type_name -> user.Theme
	11, // 4: user.MarketService.ShareBook:input_type -> user.ShareBookRequest
	3,  // 5: user.MarketService.BookDetail:input_type -> user.BookDetailRequest
	2,  // 6: user.MarketService.BookModify:input_type -> user.BookModifyRequest
	0,  // 7: user.MarketService.SearchBook:input_type -> user.SearchBookRequest
	10, // 8: user.MarketService.ThemeTopk:input_type -> user.Empty
	6,  // 9: user.MarketService.ThemeBookTopk:input_type -> user.ThemeBookTopkRequest
	10, // 10: user.MarketService.ShareBook:output_type -> user.Empty
	4,  // 11: user.MarketService.BookDetail:output_type -> user.BookDetailResponse
	10, // 12: user.MarketService.BookModify:output_type -> user.Empty
	1,  // 13: user.MarketService.SearchBook:output_type -> user.SearchBookResponse
	8,  // 14: user.MarketService.ThemeTopk:output_type -> user.ThemeTopkResponse
	7,  // 15: user.MarketService.ThemeBookTopk:output_type -> user.ThemeBookTopkResponse
	10, // [10:16] is the sub-list for method output_type
	4,  // [4:10] is the sub-list for method input_type
	4,  // [4:4] is the sub-list for extension type_name
	4,  // [4:4] is the sub-list for extension extendee
	0,  // [0:4] is the sub-list for field type_name
}

func init() { file_protos_market_proto_init() }
func file_protos_market_proto_init() {
	if File_protos_market_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_protos_market_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchBookRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_market_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchBookResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_market_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BookModifyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_market_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BookDetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_market_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BookDetailResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_market_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BookInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_market_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ThemeBookTopkRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_market_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ThemeBookTopkResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_market_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ThemeTopkResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_market_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Theme); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_market_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Empty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_market_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ShareBookRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_protos_market_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_protos_market_proto_goTypes,
		DependencyIndexes: file_protos_market_proto_depIdxs,
		MessageInfos:      file_protos_market_proto_msgTypes,
	}.Build()
	File_protos_market_proto = out.File
	file_protos_market_proto_rawDesc = nil
	file_protos_market_proto_goTypes = nil
	file_protos_market_proto_depIdxs = nil
}
