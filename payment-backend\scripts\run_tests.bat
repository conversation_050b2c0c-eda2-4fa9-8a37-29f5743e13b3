@echo off
setlocal

REM Payment Backend Test Runner Script for Windows

REM Check Go environment
where go >nul 2>nul
if %errorlevel% neq 0 (
    echo Error: Go is not installed or not in PATH
    exit /b 1
)

echo Go version:
go version
echo.

set "action=%1"
if "%action%"=="" set "action=all"

if "%action%"=="unit" goto run_unit_tests
if "%action%"=="integration" goto run_integration_tests
if "%action%"=="all" goto run_all_tests
if "%action%"=="coverage" goto run_coverage
if "%action%"=="benchmark" goto run_benchmark
if "%action%"=="cleanup" goto cleanup
if "%action%"=="help" goto show_help
if "%action%"=="-h" goto show_help
if "%action%"=="--help" goto show_help

echo Error: Unknown option '%action%'
echo.
goto show_help

:run_unit_tests
echo.
echo ==================================================
echo Running Unit Tests
echo ==================================================
echo.
echo Running Handler unit tests...
go test ./internal/handler -v -run "^TestPaymentHandler_"
if %errorlevel% equ 0 (
    echo [SUCCESS] Unit tests passed
) else (
    echo [FAILED] Unit tests failed
    exit /b 1
)
goto end

:run_integration_tests
echo.
echo ==================================================
echo Running Integration Tests
echo ==================================================
echo.
echo Running integration test suite...
go test ./internal/handler -v -run "TestIntegrationSuite"
if %errorlevel% equ 0 (
    echo [SUCCESS] Integration tests passed
) else (
    echo [FAILED] Integration tests failed
    exit /b 1
)
goto end

:run_all_tests
echo.
echo ==================================================
echo Running All Tests
echo ==================================================
echo.
echo Running all Handler tests...
go test ./internal/handler/... -v
if %errorlevel% equ 0 (
    echo [SUCCESS] All tests passed
) else (
    echo [FAILED] Tests failed
    exit /b 1
)
goto end

:run_coverage
echo.
echo ==================================================
echo Generating Test Coverage Report
echo ==================================================
echo.
echo Running all tests first...
go test ./internal/handler/... -v
if %errorlevel% neq 0 (
    echo [FAILED] Tests failed
    exit /b 1
)
echo.
echo Generating coverage data...
go test ./internal/handler -coverprofile=coverage.out
if exist coverage.out (
    echo Generating coverage report...
    go tool cover -func coverage.out
    echo.
    echo Generating HTML coverage report...
    go tool cover -html coverage.out -o coverage.html
    echo [SUCCESS] Coverage report generated:
    echo   - Text report: displayed above
    echo   - HTML report: coverage.html
) else (
    echo [FAILED] Coverage file generation failed
    exit /b 1
)
goto end

:run_benchmark
echo.
echo ==================================================
echo Running Performance Tests
echo ==================================================
echo.
echo Running benchmark tests...
go test ./internal/handler -bench=. -benchmem
echo [SUCCESS] Performance tests completed
goto end

:cleanup
echo.
echo ==================================================
echo Cleaning Test Files
echo ==================================================
echo.
echo Cleaning temporary files...
if exist coverage.out del coverage.out
if exist coverage.html del coverage.html
echo [SUCCESS] Cleanup completed
goto end

:show_help
echo Payment Backend Test Runner Script
echo.
echo Usage: %~nx0 [option]
echo.
echo Options:
echo   unit        Run unit tests
echo   integration Run integration tests
echo   all         Run all tests (default)
echo   coverage    Generate test coverage report
echo   benchmark   Run performance tests
echo   cleanup     Clean test files
echo   help        Show this help information
echo.
echo Examples:
echo   %~nx0              # Run all tests
echo   %~nx0 unit         # Run unit tests only
echo   %~nx0 coverage     # Generate coverage report
echo   %~nx0 cleanup      # Clean test files
goto end

:end
echo.
echo ==================================================
echo Test Completed
echo ==================================================
echo [SUCCESS] All operations completed successfully!
