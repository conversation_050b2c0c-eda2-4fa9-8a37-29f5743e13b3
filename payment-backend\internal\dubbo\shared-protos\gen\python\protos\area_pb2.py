# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: protos/area.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'protos/area.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x11protos/area.proto\x12\x15\x63om.aibook.admin.grpc\x1a\x1cgoogle/api/annotations.proto\"\x07\n\x05\x45mpty\"%\n\x07\x43ountry\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\"&\n\x08Language\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\"u\n\x0c\x41reaResponse\x12\x31\n\tcountries\x18\x01 \x03(\x0b\x32\x1e.com.aibook.admin.grpc.Country\x12\x32\n\tlanguages\x18\x02 \x03(\x0b\x32\x1f.com.aibook.admin.grpc.Language2x\n\x0b\x41reaService\x12i\n\x08getAreas\x12\x1c.com.aibook.admin.grpc.Empty\x1a#.com.aibook.admin.grpc.AreaResponse\"\x1a\x82\xd3\xe4\x93\x02\x14\x12\x12/api/v1/admin/areaB\x11P\x01Z\r./admin;adminb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'protos.area_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'P\001Z\r./admin;admin'
  _globals['_AREASERVICE'].methods_by_name['getAreas']._loaded_options = None
  _globals['_AREASERVICE'].methods_by_name['getAreas']._serialized_options = b'\202\323\344\223\002\024\022\022/api/v1/admin/area'
  _globals['_EMPTY']._serialized_start=74
  _globals['_EMPTY']._serialized_end=81
  _globals['_COUNTRY']._serialized_start=83
  _globals['_COUNTRY']._serialized_end=120
  _globals['_LANGUAGE']._serialized_start=122
  _globals['_LANGUAGE']._serialized_end=160
  _globals['_AREARESPONSE']._serialized_start=162
  _globals['_AREARESPONSE']._serialized_end=279
  _globals['_AREASERVICE']._serialized_start=281
  _globals['_AREASERVICE']._serialized_end=401
# @@protoc_insertion_point(module_scope)
