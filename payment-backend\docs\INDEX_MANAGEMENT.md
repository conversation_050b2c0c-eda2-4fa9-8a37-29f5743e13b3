# 索引管理改进方案

## 概述

为了解决硬编码索引创建的问题，我们引入了新的索引管理方案，可以自动从模型定义中提取索引信息，减少维护成本。

## 问题分析

### 原有问题
1. **硬编码索引**：在 `migrate.go` 中硬编码了大量索引创建语句
2. **维护困难**：修改 `OrderModel` 字段时需要同时修改索引创建函数
3. **重复定义**：模型中的 gorm 标签和 migrate.go 中的索引定义重复
4. **不一致风险**：容易出现模型定义和索引创建不一致的情况

### 解决方案
1. **在模型中统一定义索引**：使用 gorm 标签定义所有基础索引
2. **自动提取索引信息**：通过反射从模型中提取索引定义
3. **分离复合索引**：复杂的复合索引仍在 migrate.go 中定义
4. **索引管理器**：提供统一的索引管理接口

## 改进后的架构

### 1. 模型定义（推荐方式）

在 `OrderModel` 中使用 gorm 标签定义索引：

```go
type OrderModel struct {
    ID                uint64     `gorm:"primaryKey;autoIncrement" json:"id"`
    OrderID           string     `gorm:"type:varchar(64);not null;uniqueIndex:idx_orders_order_id" json:"order_id"`
    UserID            string     `gorm:"type:varchar(64);not null;index:idx_orders_user_id" json:"user_id"`
    ProductID         string     `gorm:"type:varchar(64);not null;index:idx_orders_product_id" json:"product_id"`
    PayStatus         string     `gorm:"type:varchar(20);not null;index:idx_orders_pay_status;default:'created'" json:"pay_status"`
    // ... 其他字段
}
```

### 2. 复合索引定义

在 `migrate.go` 中只定义复合索引：

```go
func createIndexes(db *gorm.DB) error {
    indexes := []IndexDefinition{
        {
            Name:    "idx_orders_user_pay_status",
            Table:   "orders",
            Columns: []string{"user_id", "pay_status"},
            Comment: "用户ID和支付状态的复合索引",
        },
        // ... 其他复合索引
    }
    // ...
}
```

### 3. 索引管理器使用

```go
// 创建索引管理器
indexManager := NewIndexManager(db)

// 列出模型中定义的索引
indexManager.ListModelIndexes(&OrderModel{})

// 验证索引是否存在
err := indexManager.ValidateIndexes(&OrderModel{})

// 创建模型中定义的索引（通常由 GORM AutoMigrate 自动处理）
err := indexManager.CreateModelIndexes(&OrderModel{})
```

## 使用指南

### 1. 添加新字段时

当你在 `OrderModel` 中添加新字段时：

```go
type OrderModel struct {
    // ... 现有字段
    NewField string `gorm:"type:varchar(64);index:idx_orders_new_field" json:"new_field"`
}
```

**无需修改其他文件**，GORM 的 AutoMigrate 会自动创建索引。

### 2. 添加复合索引时

如果需要添加复合索引，在 `migrate.go` 的 `indexes` 数组中添加：

```go
{
    Name:    "idx_orders_new_composite",
    Table:   "orders",
    Columns: []string{"field1", "field2"},
    Comment: "新的复合索引",
}
```

### 3. 验证索引

运行以下命令验证所有索引是否正确创建：

```bash
go run scripts/validate_indexes.go
```

## 最佳实践

### 1. 索引命名规范
- 单列索引：`idx_{table}_{column}`
- 复合索引：`idx_{table}_{column1}_{column2}`
- 唯一索引：`idx_{table}_{column}` 或 `uk_{table}_{column}`

### 2. 索引类型选择
- **单列索引**：在模型字段的 gorm 标签中定义
- **复合索引**：在 migrate.go 中定义
- **特殊索引**（如函数索引）：在 migrate.go 中定义

### 3. 性能考虑
- 避免过多索引，影响写入性能
- 根据查询模式设计复合索引
- 定期分析索引使用情况

## 迁移步骤

### 1. 现有项目迁移

如果你有现有的项目需要迁移到新的索引管理方案：

1. **备份数据库**
2. **更新模型定义**：在字段标签中添加索引定义
3. **清理 migrate.go**：移除已在模型中定义的索引
4. **测试验证**：确保所有索引正确创建

### 2. 新项目

对于新项目：
1. 直接使用新的索引管理方案
2. 在模型中定义基础索引
3. 在 migrate.go 中定义复合索引

## 工具支持

### 1. 索引分析工具

```bash
# 分析当前模型的索引定义
go run tools/analyze_indexes.go

# 比较模型定义和数据库实际索引
go run tools/compare_indexes.go
```

### 2. 索引优化建议

```bash
# 分析查询性能，给出索引优化建议
go run tools/index_advisor.go
```

## 总结

通过这种改进方案：

1. **减少维护成本**：只需在一个地方（模型定义）维护基础索引
2. **提高一致性**：避免模型定义和索引创建不一致
3. **简化流程**：新增字段时无需修改多个文件
4. **保持灵活性**：复杂索引仍可在 migrate.go 中定义
5. **工具支持**：提供索引管理和验证工具

这样的设计既解决了硬编码问题，又保持了系统的灵活性和可维护性。
