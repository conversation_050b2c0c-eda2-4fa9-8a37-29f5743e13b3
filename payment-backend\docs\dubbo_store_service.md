# Dubbo Store Service 接口文档

## 概述

本文档描述了流量包管理的 Dubbo gRPC 接口，提供了与 HTTP 接口相同的功能，支持终端用户和管理员操作。

## 服务定义

服务名称: `StoreService`
包名: `com.aibook.payment.grpc`

## 接口列表

### 1. ListAllPackages - 获取所有流量包（终端用户）

**方法签名:**
```protobuf
rpc ListAllPackages(ListAllPackagesRequest) returns (ListAllPackagesResponse);
```

**请求参数:**
- `user_context`: 用户上下文（必需）
  - `user_id`: 用户ID
  - `role`: 用户角色
- `filter`: 过滤条件（可选）
  - `currency`: 货币单位
  - `country`: 国家
- `pagination`: 分页参数（可选）
  - `limit`: 每页数量（默认50）
  - `offset`: 偏移量（默认0）

**响应:**
- `packages`: 流量包列表
- `pagination`: 分页信息

### 2. AdminAddPackages - 添加流量包（管理员）

**方法签名:**
```protobuf
rpc AdminAddPackages(CreatePackageRequest) returns (CreatePackageResponse);
```

**请求参数:**
- `package_name`: 流量包名称（必需）
- `package_desc`: 流量包描述（必需）
- `entitlement`: 购买后获得的权益（必需）
- `original_price`: 流量包原价（必需）
- `discount_price`: 流量包优惠价（可选）
- `discount_start_time`: 优惠开始时间（可选）
- `discount_end_time`: 优惠结束时间（可选）
- `sale_status`: 出售状态（必需）
- `currency`: 货币单位（必需）
- `country`: 国家（必需）
- `extra1-4`: 附加字段（可选）

**响应:**
- `message`: 操作结果消息

### 3. AdminDeletePackages - 删除流量包（管理员）

**方法签名:**
```protobuf
rpc AdminDeletePackages(DeletePackageRequest) returns (DeletePackageResponse);
```

**请求参数:**
- `package_id`: 流量包ID（必需）
- `currency`: 货币单位（默认USD）
- `country`: 国家（默认US）

**响应:**
- `message`: 操作结果消息

### 4. AdminUpdatePackages - 更新流量包（管理员）

**方法签名:**
```protobuf
rpc AdminUpdatePackages(UpdatePackageRequest) returns (UpdatePackageResponse);
```

**请求参数:**
- `package_id`: 流量包ID（必需）
- 其他字段均为可选，只更新提供的字段

**响应:**
- `message`: 操作结果消息

### 5. AdminListAllPackages - 获取所有流量包（管理员）

**方法签名:**
```protobuf
rpc AdminListAllPackages(AdminListAllPackagesRequest) returns (AdminListAllPackagesResponse);
```

**请求参数:**
- `filter`: 过滤条件（可选）
- `pagination`: 分页参数（可选）

**响应:**
- `packages`: 管理员流量包列表（包含完整信息）
- `pagination`: 分页信息

## 数据类型

### PackageResponse（终端用户响应）
```protobuf
message PackageResponse {
  string package_id = 1;
  string package_name = 2;
  string package_desc = 3;
  int32 entitlement = 4;
  double price = 5; // 实际价格（优惠价或原价）
  optional double original_price = 6; // 原价（仅在有优惠时显示）
  optional google.protobuf.Timestamp discount_start_time = 7;
  optional google.protobuf.Timestamp discount_end_time = 8;
  string sale_status = 9;
  string currency = 10;
  string country = 11;
}
```

### AdminPackageResponse（管理员响应）
```protobuf
message AdminPackageResponse {
  uint64 id = 1;
  string package_id = 2;
  string package_name = 3;
  string package_desc = 4;
  int32 entitlement = 5;
  double original_price = 6;
  optional double discount_price = 7;
  optional google.protobuf.Timestamp discount_start_time = 8;
  optional google.protobuf.Timestamp discount_end_time = 9;
  string sale_status = 10;
  string currency = 11;
  string country = 12;
  string extra1 = 13;
  string extra2 = 14;
  int32 extra3 = 15;
  int32 extra4 = 16;
  google.protobuf.Timestamp created_at = 17;
  google.protobuf.Timestamp updated_at = 18;
}
```

## 错误处理

所有接口都使用标准的 gRPC 错误码：
- `INVALID_ARGUMENT`: 请求参数错误
- `INTERNAL`: 服务器内部错误
- `NOT_FOUND`: 资源不存在

## 使用示例

### Go 客户端示例

```go
// 创建客户端连接
conn, err := grpc.Dial("localhost:20000", grpc.WithInsecure())
if err != nil {
    log.Fatal(err)
}
defer conn.Close()

client := paymentpb.NewStoreServiceClient(conn)

// 获取流量包列表
req := &paymentpb.ListAllPackagesRequest{
    UserContext: &paymentpb.UserContext{
        UserId: "user123",
        Role:   "customer",
    },
    Filter: &paymentpb.PackageFilter{
        Currency: stringPtr("USD"),
        Country:  stringPtr("US"),
    },
    Pagination: &paymentpb.PaginationRequest{
        Limit:  10,
        Offset: 0,
    },
}

resp, err := client.ListAllPackages(context.Background(), req)
if err != nil {
    log.Fatal(err)
}

fmt.Printf("Found %d packages\n", len(resp.Packages))
```

## 注意事项

1. 所有时间字段使用 `google.protobuf.Timestamp` 类型
2. 可选字段使用 `optional` 关键字标记
3. 分页参数如果不提供，会使用默认值（limit=50, offset=0）
4. 管理员接口返回完整的流量包信息，包括内部字段
5. 终端用户接口只返回必要的公开信息

## 生成代码

在修改 `.proto` 文件后，需要重新生成代码：

```bash
# 在 payment-backend/internal/dubbo/shared-protos 目录下执行
protoc --go_out=. --go-grpc_out=. protos/store.proto
```

生成的代码将放在 `gen/go/paymentpb/` 目录下。
