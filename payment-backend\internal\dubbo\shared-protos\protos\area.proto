/**
  后台管理平台基础服务定义
 */
syntax = "proto3";

package com.aibook.admin.grpc;
option java_multiple_files = true;
option go_package = "./admin;admin";
import "google/api/annotations.proto";


message Empty {}


message Country {
  string name = 1;
  string code = 2;
}

message Language {
  string name = 1;
  string code = 2;
}

message AreaResponse {
  repeated Country countries = 1;
  repeated Language languages = 2;
}

service AreaService {
  rpc getAreas(Empty) returns (AreaResponse) {
    option (google.api.http) = {
      get : "/api/v1/admin/area"
    };
  }
}
