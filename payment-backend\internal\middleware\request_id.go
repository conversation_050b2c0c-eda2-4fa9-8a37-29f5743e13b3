package middleware

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// RequestIDMiddleware 请求ID中间件
func RequestIDMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		requestID := c.<PERSON>eader("x-trace-id")
		if requestID == "" {
			requestID = generateRequestID()
		}

		c.<PERSON><PERSON>("x-trace-id", requestID)
		c.Set("x-trace-id", requestID)
		c.Next()
	}
}

// RequireRequestIDMiddleware 强制要求客户端传 X-Request-ID
func RequireRequestIDMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		requestID1 := c.<PERSON>Header("x-trace-id")
		requestID2 := c.GetHeader("X-Request-ID")
		if requestID1 == "" && requestID2 == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "unauthorized",
				"message": "x-trace-id header is required",
			})
			c.Abort()
			return
		}
		requestID := requestID1
		if requestID == "" {
			requestID = requestID2
		}

		// 允许后续中间件继续使用它
		c.Set("x-trace-id", requestID)
		c.Next()
	}
}

// generateRequestID 生成请求ID
func generateRequestID() string {
	return uuid.New().String() // 使用 UUID 作为请求ID
}
