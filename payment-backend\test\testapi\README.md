# API 测试程序

这是一个用于测试支付后端服务API接口的自动化测试程序。

## 功能特性

- 支持多环境测试（dev1, dev2, sit）
- 自动获取外网接口的认证token
- 测试外网接口（Gin HTTP）
- 测试内网接口（Gin HTTP + Dubbo RPC）
- **详细日志记录**: 所有测试数据记录到 `test.log` 文件
- **控制台简要输出**: 控制台只显示关键信息和最终汇总
- **双重输出汇总**: 测试结果汇总同时输出到控制台和日志文件

## 测试接口

### 外网接口（只测试 Gin HTTP）
1. **创建订单** - `POST /api/v1/pay-service/order-service/orders`
2. **获取用户订单** - `GET /api/v1/pay-service/order-service/orders`
3. **获取流量包列表** - `GET /api/v1/pay-service/store-service/packages`
4. **获取流量包列表(带过滤)** - `GET /api/v1/pay-service/store-service/packages?currency=USD&country=US`

### 内网接口（测试 Gin HTTP + Dubbo RPC）

#### 订单相关接口
1. **获取批量订单（Gin HTTP）** - `GET /api/v1/pay-service/admin/order-service/orders`
2. **获取批量订单（Dubbo RPC）** - `POST /com.aibook.payment.grpc.OrderService/ListAllOrders`
3. **强制退款（Gin HTTP）** - `POST /api/v1/pay-service/admin/order-service/orders/:order_id/force-refund`

#### 流量包管理接口（Gin HTTP）
4. **创建流量包** - `PUT /api/v1/pay-service/admin/store-service/packages`
5. **获取所有流量包** - `GET /api/v1/pay-service/admin/store-service/packages`
6. **更新流量包** - `POST /api/v1/pay-service/admin/store-service/packages`
7. **删除流量包** - `DELETE /api/v1/pay-service/admin/store-service/packages`

#### 流量包管理接口（Dubbo RPC）
8. **创建流量包** - `POST /com.aibook.payment.grpc.StoreService/AdminAddPackages`
9. **获取所有流量包** - `POST /com.aibook.payment.grpc.StoreService/AdminListAllPackages`
10. **更新流量包** - `POST /com.aibook.payment.grpc.StoreService/AdminUpdatePackages`
11. **删除流量包** - `POST /com.aibook.payment.grpc.StoreService/AdminDeletePackages`

## 环境配置

- **dev1**: 开发环境1
  - 外网: http://ny10wt9045294.vicp.fun:25639
  - 内网Gin: http://192.168.1.200:15445
  - 内网RPC: http://192.168.1.200:15446

- **dev2**: 开发环境2
  - 外网: http://ny10wt9045294.vicp.fun
  - 内网Gin: http://192.168.1.200:25906
  - 内网RPC: http://192.168.1.200:25907

- **sit**: 系统集成测试环境
  - 外网: http://ny10wt9045294.vicp.fun:29397
  - 内网Gin: http://192.168.1.200:30000
  - 内网RPC: http://192.168.1.200:30001

## 使用方法

### 1. 编译程序

```bash
# 在 payment-backend 目录下运行
build.bat
```

或者手动编译：

```bash
cd test\testapi
go build -o test_api.exe test_api.go
```

### 2. 运行测试

```bash
# 使用批处理脚本（推荐）
run_test.bat [环境名]

# 或直接运行可执行文件
cd test\testapi
.\test_api.exe [环境名]

# 或使用 go run（开发时）
cd test\testapi\src
go run . [环境名]
```

### 3. 环境参数

- `dev1` - 测试开发环境1
- `dev2` - 测试开发环境2
- `sit` - 测试SIT环境
- `all` - 测试所有环境（默认）

### 示例

```bash
# 测试dev1环境
run_test.bat dev1

# 测试所有环境
run_test.bat all
# 或
run_test.bat
```

## 认证方式

### 外网接口
- 使用登录接口自动获取token
- 请求头包含：
  - `x-trace-id`: UUID v4格式的追踪ID
  - `Authorization`: Bearer token

### 内网接口
- 无需token认证
- 请求头包含：
  - `x-trace-id`: UUID v4格式的追踪ID
  - `x-user-id`: 用户ID（admin123）
  - `x-role`: 用户角色（admin）

## 测试结果

### 控制台输出
控制台会显示：
- 测试环境信息（环境名称、URL等）
- 测试进度（正在测试哪个接口）
- 简要的测试结果（状态码、订单ID等关键信息）
- 最终的测试结果汇总

### 日志文件输出
`test.log` 文件包含：
- 测试会话的开始和结束时间
- 每个测试的详细信息：
  - 完整的请求URL
  - 所有请求头
  - 完整的请求体
  - 响应状态码
  - 完整的响应体（格式化的JSON）
- 最终的测试结果汇总

### 查看日志
```bash
# 查看完整日志
cat test.log

# 查看最新日志
tail -f test.log

# 在Windows PowerShell中查看最新日志
Get-Content test.log -Tail 20 -Wait
```

## 依赖

- Go 1.24+
- github.com/google/uuid

## 文件结构

```
test/testapi/
├── src/                                    # 源码目录
│   ├── test_api.go                        # 主程序入口
│   ├── common.go                          # 公共函数和数据结构
│   ├── testCreateOrderExternal.go         # 外网创建订单测试
│   ├── testGetUserOrdersExternal.go       # 外网获取用户订单测试
│   ├── testListAllPackagesExternal.go     # 外网获取流量包列表测试
│   ├── testListAllOrdersGin.go            # 内网订单Gin HTTP接口测试
│   ├── testListAllOrdersRPC.go            # 内网订单Dubbo RPC接口测试
│   ├── testForceRefundGin.go              # 内网强制退款Gin HTTP接口测试
│   ├── testAdminPackagesGin.go            # 内网流量包管理Gin HTTP接口测试
│   ├── testAdminPackagesRPC.go            # 内网流量包管理Dubbo RPC接口测试
│   └── test.log                           # 测试日志文件（运行后生成）
├── testapi.exe      # 编译后的可执行文件
├── go.mod           # Go模块文件
├── go.sum           # Go依赖校验文件
├── build.bat        # 编译脚本
├── run_test.bat     # 运行脚本
└── README.md        # 说明文档
```

## 注意事项

1. **日志文件会持续追加**: 每次运行测试都会在 `test.log` 文件末尾追加新的日志，不会覆盖之前的记录
2. 确保网络连接正常，能够访问测试环境
3. 外网接口需要有效的登录凭据
4. 内网接口需要能够访问内网地址
5. 测试会创建真实的订单和流量包数据，请在测试环境中运行
6. **超时设置**: HTTP请求超时时间设置为60秒
7. **强制退款接口**: 目前只有HTTP实现，没有对应的RPC接口
8. **流量包管理**: 更新和删除操作需要先创建流量包并获取真实的package_id
9. **测试数据**: 某些测试使用示例数据（如order_id），实际测试时需要替换为真实数据
