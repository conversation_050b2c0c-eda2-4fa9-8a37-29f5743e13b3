# Nacos 配置中心集成指南

## 概述

Payment Backend 现已集成 Nacos 配置中心，支持动态配置管理。配置优先级如下：

**配置文件 < Nacos配置 < 环境变量**

即：环境变量具有最高优先级，可以覆盖 Nacos 配置；Nacos 配置可以覆盖本地配置文件。

## 功能特性

- ✅ 从 Nacos 动态加载配置
- ✅ 配置变更实时监听
- ✅ 多环境配置支持
- ✅ 配置优先级管理
- ✅ 故障容错（Nacos 不可用时仍可启动）
- ✅ 多 Nacos 服务器支持

## 配置说明

### 1. Nacos 配置项

在 `config.yaml` 中添加 Nacos 配置：

```yaml
# Nacos配置（统一配置中心和注册中心）
nacos:
  enabled: true                    # 是否启用 Nacos
  endpoints:                       # Nacos 服务器地址列表
    - "127.0.0.1:8848"
    - "127.0.0.1:8849"            # 支持多个服务器
  namespace: "payment-service"      # 命名空间
  username: "nacos"                # 用户名
  password: "nacos"                # 密码

  # 配置中心设置
  config:
    enabled: true                  # 是否启用配置中心
    data_id: "payment-backend.yaml" # 配置文件 ID
    group: "DEFAULT_GROUP"         # 配置组
    timeout: 30                    # 连接超时时间(秒)

  # 注册中心设置
  registry:
    enabled: true                  # 是否启用注册中心（用于Dubbo）
```

### 2. 环境变量配置

```bash
# 启用 Nacos
export PAYMENT_NACOS_ENABLED=true

# Nacos 服务器地址（支持逗号分隔的多个地址）
export PAYMENT_NACOS_ENDPOINTS="192.168.1.100:8848,192.168.1.101:8848"

# 命名空间
export PAYMENT_NACOS_NAMESPACE="payment-service"

# 认证信息
export PAYMENT_NACOS_USERNAME="nacos"
export PAYMENT_NACOS_PASSWORD="nacos"

# 配置中心设置
export PAYMENT_NACOS_CONFIG_ENABLED=true
export PAYMENT_NACOS_CONFIG_DATA_ID="payment-backend.yaml"
export PAYMENT_NACOS_CONFIG_GROUP="DEFAULT_GROUP"
export PAYMENT_NACOS_CONFIG_TIMEOUT=30

# 注册中心设置
export PAYMENT_NACOS_REGISTRY_ENABLED=true
```

## 使用方式

### 1. 基本使用

```go
package main

import (
    "log"
    "payment-backend/internal/config"
)

func main() {
    // 加载配置（自动从 Nacos 获取）
    cfg, err := config.LoadConfig("")
    if err != nil {
        log.Fatalf("Failed to load config: %v", err)
    }
    
    // 使用配置
    fmt.Printf("Server: %s\n", cfg.Server.GetAddress())
}
```

### 2. 使用配置管理器（推荐）

```go
package main

import (
    "log"
    "payment-backend/internal/config"
)

func main() {
    // 创建配置管理器（支持动态配置更新）
    configManager, err := config.NewConfigManager("")
    if err != nil {
        log.Fatalf("Failed to create config manager: %v", err)
    }
    
    // 获取当前配置
    cfg := configManager.GetConfig()
    
    // 添加配置变更回调
    configManager.AddCallback(func(newConfig *config.Config) {
        log.Printf("Configuration updated!")
        // 处理配置变更...
    })
    
    // 应用继续运行...
}
```

## Nacos 配置示例

在 Nacos 控制台中创建配置文件 `payment-backend.yaml`：

```yaml
# 服务器配置
server:
  host: "0.0.0.0"
  port: 8080
  mode: "release"

# 数据库配置
database:
  host: "prod-db-host"
  port: 3306
  username: "prod_user"
  password: "prod_password"
  database: "payment_prod_db"

# 支付配置
payment:
  providers:
    stripe:
      enabled: true
      api_key: "sk_live_xxxxx"
      secret_key: "sk_live_secret_xxxxx"
      settings:
        environment: "live"
    
    paypal:
      enabled: true
      api_key: "live_paypal_key"
      secret_key: "live_paypal_secret"
      settings:
        environment: "live"

# 日志配置
log:
  level: "info"
  format: "json"
  output: "file"
  filename: "/var/log/payment-backend.log"
```

## 配置优先级示例

假设有以下配置：

1. **配置文件** (`config.yaml`):
   ```yaml
   server:
     port: 8080
   log:
     level: "info"
   ```

2. **Nacos 配置**:
   ```yaml
   server:
     port: 9090
   log:
     level: "debug"
   ```

3. **环境变量**:
   ```bash
   export PAYMENT_SERVER_PORT=7777
   ```

**最终配置结果**:
- `server.port`: `7777` (环境变量覆盖)
- `log.level`: `"debug"` (Nacos 配置覆盖配置文件)

## 部署建议

### 开发环境

```bash
# 使用本地 Nacos
export PAYMENT_NACOS_ENABLED=true
export PAYMENT_NACOS_ENDPOINTS="127.0.0.1:8848"
export PAYMENT_NACOS_NAMESPACE="payment-dev"
export PAYMENT_NACOS_DATA_ID="payment-backend-dev.yaml"
```

### 生产环境

```bash
# 使用生产 Nacos 集群
export PAYMENT_NACOS_ENABLED=true
export PAYMENT_NACOS_ENDPOINTS="nacos1.prod.com:8848,nacos2.prod.com:8848,nacos3.prod.com:8848"
export PAYMENT_NACOS_NAMESPACE="payment-prod"
export PAYMENT_NACOS_USERNAME="prod_user"
export PAYMENT_NACOS_PASSWORD="prod_password"
export PAYMENT_NACOS_DATA_ID="payment-backend-prod.yaml"
export PAYMENT_NACOS_GROUP="PROD_GROUP"
```

## 故障处理

### 1. Nacos 不可用

如果 Nacos 服务器不可用，应用仍会正常启动，使用本地配置文件和环境变量。

### 2. 配置格式错误

如果 Nacos 中的配置格式错误，会记录警告日志但不会中断启动。

### 3. 网络超时

可以通过 `PAYMENT_NACOS_TIMEOUT` 环境变量调整连接超时时间。

## 监控和日志

应用会记录以下日志：

```
Successfully loaded config from nacos: dataId=payment-backend.yaml, group=DEFAULT_GROUP
Started nacos config watching: dataId=payment-backend.yaml, group=DEFAULT_GROUP
Config changed from nacos: namespace=payment-service, group=DEFAULT_GROUP, dataId=payment-backend.yaml
```

## 最佳实践

1. **敏感信息**: 使用环境变量存储敏感信息（如数据库密码、API密钥）
2. **环境隔离**: 不同环境使用不同的 namespace 和 data_id
3. **配置验证**: 在配置变更回调中验证新配置的有效性
4. **渐进式部署**: 先在开发环境验证配置变更，再推广到生产环境
5. **备份策略**: 定期备份 Nacos 配置，确保配置安全

## 运行示例

```bash
# 运行配置示例
cd payment-backend
go run examples/nacos_config_example.go
```

这将演示各种配置加载方式和优先级规则。
