@echo off
setlocal enabledelayedexpansion

REM 服务器环境[dev1] (注意，在 k8s 上可能需要映射)
set "req_path=http://*************:15445/api/v1/pay-service/order-service/orders"

REM 服务器环境[dev2] (注意，在 k8s 上可能需要映射)
REM set "req_path=http://*************:25906/api/v1/pay-service/order-service/orders"


REM curl.exe 路径，改成你本机实际路径
set "curl_path=C:\file\program\curl-8.1.2_3-win64-mingw\bin\curl.exe"

REM 设置请求的 JSON 数据（全部写一行）
set "json={\"product_id\":\"prod_stripe_001\",\"product_desc\":\"Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq\",\"price_id\":\"price_1Rd4qlC53MAl6WmqQ9ORYbPq\",\"quantity\":1,\"psp_provider\":\"stripe\"}"


REM 循环1000次
for /l %%i in (1,1,20) do (
    set "traceid=TRACEID_%%i"
    echo Running request %%i with x-trace-id=!traceid!
    "%curl_path%" -X POST "%req_path%" ^
    -H "Content-Type: application/json" ^
    -H "x-trace-id: !traceid!" ^
    -H "x-user-id: user123" ^
    -H "x-role: customer" ^
    --data "!json!"
    echo.
)

endlocal
