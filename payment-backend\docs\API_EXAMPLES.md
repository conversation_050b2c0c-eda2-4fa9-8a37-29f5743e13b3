# API 使用示例

## 概述

本文档提供了新的订单服务API的使用示例。

## 基础信息

- **Base URL**: `http://localhost:8080/api/v1/order-service`
- **认证**: 需要在请求头中包含用户认证信息
- **Content-Type**: `application/json`

## API 接口示例

### 1. 创建订单

**请求**
```bash
curl -X POST http://localhost:8080/api/v1/order-service/orders \
  -H "Content-Type: application/json" \
  -H "X-User-ID: user123" \
  -H "X-Role: customer" \
  -d '{
    "product_id": "prod_123",
    "product_desc": "Premium Subscription",
    "price_id": "price_456",
    "quantity": 1,
    "currency": "USD",
    "payed_method": "stripe",
    "psp_provider": "stripe",
    "description": "Monthly premium subscription"
  }'
```

**响应**
```json
{
  "order_id": "20250710153045999stripe1234567890123456789",
  "checkout_url": "https://mock-payment.example.com/stripe/checkout/20250710153045999stripe1234567890123456789?amount=99.99&currency=USD&user_id=user123&success_url=https%3A%2F%2Fexample.com%2Fsuccess&cancel_url=https%3A%2F%2Fexample.com%2Fcancel",
  "amount": 99.99,
  "currency": "USD",
  "expires_at": "2025-07-11T15:30:45Z"
}
```

### 2. 获取订单详情

**请求**
```bash
curl -X GET http://localhost:8080/api/v1/order-service/orders/20250710153045999stripe1234567890123456789 \
  -H "X-User-ID: user123" \
  -H "X-Role: customer"
```

**响应**
```json
{
  "id": 1,
  "order_id": "20250710153045999stripe1234567890123456789",
  "user_id": "user123",
  "product_id": "prod_123",
  "product_desc": "Premium Subscription",
  "price_id": "price_456",
  "quantity": 1,
  "amount": 99.99,
  "net_amount": 99.99,
  "currency": "USD",
  "pay_status": "created",
  "payed_method": "stripe",
  "card_number": "",
  "payed_at": null,
  "refund_status": "none",
  "refunded_at": null,
  "psp_product_id": "",
  "psp_product_desc": "",
  "psp_price_id": "",
  "psp_payment_id": "",
  "psp_customer_id": "",
  "psp_customer_email": "",
  "psp_subscription_id": "",
  "created_at": "2025-07-10T15:30:45Z",
  "updated_at": "2025-07-10T15:30:45Z",
  "deleted": false,
  "deleted_at": null
}
```

### 3. 获取用户订单列表

**请求**
```bash
curl -X GET "http://localhost:8080/api/v1/order-service/orders?limit=10&offset=0" \
  -H "X-User-ID: user123" \
  -H "X-Role: customer"
```

**响应**
```json
{
  "orders": [
    {
      "id": 1,
      "order_id": "20250710153045999stripe1234567890123456789",
      "user_id": "user123",
      "product_id": "prod_123",
      "product_desc": "Premium Subscription",
      "price_id": "price_456",
      "quantity": 1,
      "amount": 99.99,
      "net_amount": 99.99,
      "currency": "USD",
      "pay_status": "succeeded",
      "payed_method": "stripe",
      "card_number": "**** 4242",
      "payed_at": "2025-07-10T15:35:00Z",
      "refund_status": "none",
      "refunded_at": null,
      "created_at": "2025-07-10T15:30:45Z",
      "updated_at": "2025-07-10T15:35:00Z",
      "deleted": false
    }
  ],
  "limit": 10,
  "offset": 0
}
```

### 4. 更新订单

**请求**
```bash
curl -X PUT http://localhost:8080/api/v1/order-service/orders/20250710153045999stripe1234567890123456789 \
  -H "Content-Type: application/json" \
  -H "X-User-ID: user123" \
  -H "X-Role: customer" \
  -d '{
    "pay_status": "succeeded",
    "psp_payment_id": "pi_1234567890",
    "psp_customer_id": "cus_1234567890",
    "psp_customer_email": "<EMAIL>",
    "card_number": "**** 4242",
    "net_amount": 95.99
  }'
```

**响应**
```json
{
  "message": "Order updated successfully"
}
```

### 5. 取消订单

**请求**
```bash
curl -X POST http://localhost:8080/api/v1/order-service/orders/20250710153045999stripe1234567890123456789/cancel \
  -H "X-User-ID: user123" \
  -H "X-Role: customer"
```

**响应**
```json
{
  "message": "Order cancelled successfully"
}
```

### 6. 退款订单

**请求**
```bash
curl -X POST http://localhost:8080/api/v1/order-service/orders/20250710153045999stripe1234567890123456789/refund \
  -H "Content-Type: application/json" \
  -H "X-User-ID: user123" \
  -H "X-Role: customer" \
  -d '{
    "amount": 50.00
  }'
```

**响应**
```json
{
  "message": "Order refunded successfully"
}
```

### 7. 处理Webhook

**请求**
```bash
curl -X POST http://localhost:8080/api/v1/order-service/webhooks/stripe \
  -H "Content-Type: application/json" \
  -H "Stripe-Signature: t=**********,v1=signature_here" \
  -d '{
    "id": "evt_1234567890",
    "object": "event",
    "type": "payment_intent.succeeded",
    "data": {
      "object": {
        "id": "pi_1234567890",
        "status": "succeeded",
        "amount": 9999,
        "currency": "usd"
      }
    }
  }'
```

**响应**
```json
{
  "message": "Webhook processed successfully"
}
```

### 8. 健康检查

**请求**
```bash
curl -X GET http://localhost:8080/health
```

**响应**
```json
{
  "status": "ok",
  "service": "order-service"
}
```

## 错误响应示例

### 400 Bad Request
```json
{
  "error": "Invalid request format"
}
```

### 401 Unauthorized
```json
{
  "error": "User context not found"
}
```

### 404 Not Found
```json
{
  "error": "order with order ID 20250710153045999stripe1234567890123456789 not found"
}
```

### 500 Internal Server Error
```json
{
  "error": "Failed to create order: database connection failed"
}
```

## 支付状态说明

| 状态 | 说明 |
|------|------|
| created | 订单已创建，等待支付 |
| paid | 支付已提交，处理中 |
| succeeded | 支付成功 |
| failed | 支付失败 |
| expired | 订单已过期 |
| cancelled | 订单已取消 |

## 退款状态说明

| 状态 | 说明 |
|------|------|
| none | 无退款 |
| requested | 退款请求已提交 |
| succeeded | 退款成功 |
| failed | 退款失败 |

## 注意事项

1. **认证**: 所有API都需要在请求头中包含 `X-User-ID` 和 `X-Role`
2. **金额**: 金额使用浮点数表示，单位为元（不是分）
3. **货币**: 使用ISO 4217标准的3位货币代码
4. **时间格式**: 使用ISO 8601格式的UTC时间
5. **分页**: 列表接口支持 `limit` 和 `offset` 参数进行分页
6. **订单ID**: 系统自动生成，包含时间戳、金额、支付方式和雪花ID
