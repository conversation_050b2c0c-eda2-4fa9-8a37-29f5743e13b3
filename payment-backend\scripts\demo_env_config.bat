@echo off
setlocal

echo ========================================
echo Payment Backend Environment Variables Demo
echo ========================================
echo.

echo 1. Testing with default configuration...
echo.
go run main.go serve --help
echo.

echo 2. Setting environment variables...
set PAYMENT_SERVER_HOST=localhost
set PAYMENT_SERVER_PORT=9090
set PAYMENT_SERVER_MODE=release
set PAYMENT_DATABASE_HOST=db.example.com
set PAYMENT_DATABASE_USERNAME=demo_user
set PAYMENT_DATABASE_PASSWORD=demo_password
set PAYMENT_PAYMENT_PROVIDERS_STRIPE_ENABLED=true
set PAYMENT_PAYMENT_PROVIDERS_STRIPE_API_KEY=sk_test_demo_key
set PAYMENT_LOG_LEVEL=debug
set PAYMENT_LOG_FORMAT=console

echo Environment variables set:
echo   PAYMENT_SERVER_HOST=%PAYMENT_SERVER_HOST%
echo   PAYMENT_SERVER_PORT=%PAYMENT_SERVER_PORT%
echo   PAYMENT_SERVER_MODE=%PAYMENT_SERVER_MODE%
echo   PAYMENT_DATABASE_HOST=%PAYMENT_DATABASE_HOST%
echo   PAYMENT_DATABASE_USERNAME=%PAYMENT_DATABASE_USERNAME%
echo   PAYMENT_DATABASE_PASSWORD=%PAYMENT_DATABASE_PASSWORD%
echo   PAYMENT_PAYMENT_PROVIDERS_STRIPE_ENABLED=%PAYMENT_PAYMENT_PROVIDERS_STRIPE_ENABLED%
echo   PAYMENT_PAYMENT_PROVIDERS_STRIPE_API_KEY=%PAYMENT_PAYMENT_PROVIDERS_STRIPE_API_KEY%
echo   PAYMENT_LOG_LEVEL=%PAYMENT_LOG_LEVEL%
echo   PAYMENT_LOG_FORMAT=%PAYMENT_LOG_FORMAT%
echo.

echo 3. Testing configuration loading with environment variables...
echo.
echo Creating a simple test program to show configuration values...

echo package main > temp_config_demo.go
echo. >> temp_config_demo.go
echo import ( >> temp_config_demo.go
echo     "fmt" >> temp_config_demo.go
echo     "payment-backend/internal/config" >> temp_config_demo.go
echo ) >> temp_config_demo.go
echo. >> temp_config_demo.go
echo func main() { >> temp_config_demo.go
echo     cfg, err := config.LoadConfig("") >> temp_config_demo.go
echo     if err != nil { >> temp_config_demo.go
echo         fmt.Printf("Error loading config: %%v\n", err) >> temp_config_demo.go
echo         return >> temp_config_demo.go
echo     } >> temp_config_demo.go
echo. >> temp_config_demo.go
echo     fmt.Println("=== Configuration Values ===") >> temp_config_demo.go
echo     fmt.Printf("Server Host: %%s\n", cfg.Server.Host) >> temp_config_demo.go
echo     fmt.Printf("Server Port: %%d\n", cfg.Server.Port) >> temp_config_demo.go
echo     fmt.Printf("Server Mode: %%s\n", cfg.Server.Mode) >> temp_config_demo.go
echo     fmt.Printf("Database Host: %%s\n", cfg.Database.Host) >> temp_config_demo.go
echo     fmt.Printf("Database Username: %%s\n", cfg.Database.Username) >> temp_config_demo.go
echo     fmt.Printf("Database Password: %%s\n", cfg.Database.Password) >> temp_config_demo.go
echo     fmt.Printf("Log Level: %%s\n", cfg.Log.Level) >> temp_config_demo.go
echo     fmt.Printf("Log Format: %%s\n", cfg.Log.Format) >> temp_config_demo.go
echo. >> temp_config_demo.go
echo     if stripeConfig, exists := cfg.Payment.Providers["stripe"]; exists { >> temp_config_demo.go
echo         fmt.Printf("Stripe Enabled: %%t\n", stripeConfig.Enabled) >> temp_config_demo.go
echo         fmt.Printf("Stripe API Key: %%s\n", stripeConfig.APIKey) >> temp_config_demo.go
echo     } >> temp_config_demo.go
echo } >> temp_config_demo.go

echo Running configuration test...
go run temp_config_demo.go

echo.
echo 4. Cleaning up...
del temp_config_demo.go

echo.
echo ========================================
echo Demo completed!
echo ========================================
echo.
echo Key points demonstrated:
echo - Environment variables override default values
echo - Environment variable naming: PAYMENT_SECTION_FIELD
echo - Nested configuration: PAYMENT_PAYMENT_PROVIDERS_STRIPE_API_KEY
echo - Configuration priority: ENV vars ^> Config file ^> Defaults
