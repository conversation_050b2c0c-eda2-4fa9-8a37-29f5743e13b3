package handler

import (
	"context"

	"go.uber.org/zap"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"

	"payment-backend/internal/domain"
	"payment-backend/internal/domain/store"
	"payment-backend/internal/dubbo/shared-protos/gen/go/storepb"
	"payment-backend/internal/logger"
	"payment-backend/internal/middleware"
)

// PackageDubboHandler Dubbo流量包处理器
type PackageDubboHandler struct {
	packageService store.PackageService
	logger         logger.Logger
}

// NewPackageDubboHandler 创建Dubbo流量包处理器
func NewPackageDubboHandler(packageService store.PackageService, logger logger.Logger) *PackageDubboHandler {
	return &PackageDubboHandler{
		packageService: packageService,
		logger:         logger,
	}
}

// ListAllPackages 获取所有流量包列表（终端用户接口）
func (h *PackageDubboHandler) ListAllPackages(ctx context.Context, req *storepb.ListAllPackagesRequest) (*storepb.ListAllPackagesResponse, error) {
	h.logger.Info("Dubbo ListAllPackages called",
		zap.Any("user_context", req.UserContext),
		zap.Any("filter", req.Filter),
		zap.Any("pagination", req.Pagination))

	// 转换用户上下文
	userContext := h.convertUserContextFromProto(req.UserContext)

	// 转换过滤条件
	filter := h.convertPackageFilterFromProto(req.Filter)

	// 转换分页参数
	pagination := h.convertPaginationFromProto(req.Pagination)

	// 调用业务服务层
	response, err := h.packageService.ListAllPackages(userContext, filter, pagination)
	if err != nil {
		h.logger.Error("Failed to list all packages via Dubbo",
			zap.Any("filter", filter),
			zap.Any("pagination", pagination),
			zap.Error(err))

		return nil, status.Errorf(domain.ErrCodeInternalError, "%v", err)
	}

	// 转换响应
	protoResponse := h.convertListPackagesResponseToProto(response)

	h.logger.Info("Dubbo ListAllPackages completed successfully",
		zap.Int("package_count", len(protoResponse.Packages)),
		zap.Int64("total", protoResponse.Pagination.Total))

	return protoResponse, nil
}

// AdminAddPackages 添加流量包（管理员接口）
func (h *PackageDubboHandler) AdminAddPackages(ctx context.Context, req *storepb.CreatePackageRequest) (*storepb.CreatePackageResponse, error) {
	h.logger.Info("Dubbo AdminAddPackages called",
		zap.String("package_name", req.PackageName),
		zap.String("currency", req.Currency),
		zap.String("country", req.Country))

	// 转换请求
	createReq := h.convertCreatePackageRequestFromProto(req)

	// 调用业务服务层
	err := h.packageService.AdminAddPackages(createReq)
	if err != nil {
		h.logger.Error("Failed to create package via Dubbo",
			zap.String("package_name", req.PackageName),
			zap.Error(err))

		return nil, status.Errorf(domain.ErrCodeInternalError, "%v", err)
	}

	h.logger.Info("Dubbo AdminAddPackages completed successfully",
		zap.String("package_name", req.PackageName))

	return &storepb.CreatePackageResponse{
		Message: "Package created successfully",
	}, nil
}

// AdminDeletePackages 删除流量包（管理员接口）
func (h *PackageDubboHandler) AdminDeletePackages(ctx context.Context, req *storepb.DeletePackageRequest) (*storepb.DeletePackageResponse, error) {
	h.logger.Info("Dubbo AdminDeletePackages called",
		zap.String("package_id", req.PackageId))

	// 转换请求
	deleteReq := &store.DeletePackageRequest{
		PackageID: req.PackageId,
	}

	// 调用业务服务层
	err := h.packageService.AdminDeletePackages(deleteReq)
	if err != nil {
		h.logger.Error("Failed to delete package via Dubbo",
			zap.String("package_id", req.PackageId),
			zap.Error(err))

		return nil, status.Errorf(domain.ErrCodeInternalError, "%v", err)
	}

	h.logger.Info("Dubbo AdminDeletePackages completed successfully",
		zap.String("package_id", req.PackageId))

	return &storepb.DeletePackageResponse{
		Message: "Package deleted successfully",
	}, nil
}

// AdminUpdatePackages 更新流量包（管理员接口）
func (h *PackageDubboHandler) AdminUpdatePackages(ctx context.Context, req *storepb.UpdatePackageRequest) (*storepb.UpdatePackageResponse, error) {
	h.logger.Info("Dubbo AdminUpdatePackages called",
		zap.String("package_id", req.PackageId))

	// 转换请求
	updateReq := h.convertUpdatePackageRequestFromProto(req)

	// 调用业务服务层
	err := h.packageService.AdminUpdatePackages(updateReq)
	if err != nil {
		h.logger.Error("Failed to update package via Dubbo",
			zap.String("package_id", req.PackageId),
			zap.Error(err))

		return nil, status.Errorf(domain.ErrCodeInternalError, "%v", err)
	}

	h.logger.Info("Dubbo AdminUpdatePackages completed successfully",
		zap.String("package_id", req.PackageId))

	return &storepb.UpdatePackageResponse{
		Message: "Package updated successfully",
	}, nil
}

// AdminListAllPackages 获取所有流量包列表（管理员接口）
func (h *PackageDubboHandler) AdminListAllPackages(ctx context.Context, req *storepb.AdminListAllPackagesRequest) (*storepb.AdminListAllPackagesResponse, error) {
	h.logger.Info("Dubbo AdminListAllPackages called",
		zap.Any("filter", req.Filter),
		zap.Any("pagination", req.Pagination))

	// 转换过滤条件
	filter := h.convertPackageFilterFromProto(req.Filter)

	// 转换分页参数
	pagination := h.convertPaginationFromProto(req.Pagination)

	// 调用业务服务层
	response, err := h.packageService.AdminListAllPackages(filter, pagination)
	if err != nil {
		h.logger.Error("Failed to list all packages for admin via Dubbo",
			zap.Any("filter", filter),
			zap.Any("pagination", pagination),
			zap.Error(err))

		return nil, status.Errorf(domain.ErrCodeInternalError, "%v", err)
	}

	// 转换响应
	protoResponse := h.convertAdminListPackagesResponseToProto(response)

	h.logger.Info("Dubbo AdminListAllPackages completed successfully",
		zap.Int("package_count", len(protoResponse.Packages)),
		zap.Int64("total", protoResponse.Pagination.Total))

	return protoResponse, nil
}

// convertUserContextFromProto 转换用户上下文从Proto到Domain
func (h *PackageDubboHandler) convertUserContextFromProto(protoUserContext *storepb.UserContext) *middleware.UserContext {
	if protoUserContext == nil {
		return &middleware.UserContext{}
	}

	return &middleware.UserContext{
		UserID: protoUserContext.UserId,
		Role:   protoUserContext.Role,
	}
}

// convertPackageFilterFromProto 转换过滤条件从Proto到Domain
func (h *PackageDubboHandler) convertPackageFilterFromProto(protoFilter *storepb.PackageFilter) *store.PackageFilter {
	if protoFilter == nil {
		return &store.PackageFilter{}
	}

	filter := &store.PackageFilter{}

	if protoFilter.Currency != nil {
		filter.Currency = protoFilter.Currency
	}
	if protoFilter.Country != nil {
		filter.Country = protoFilter.Country
	}

	return filter
}

// convertPaginationFromProto 转换分页参数从Proto到Domain
func (h *PackageDubboHandler) convertPaginationFromProto(protoPagination *storepb.PaginationRequest) *store.PaginationRequest {
	if protoPagination == nil {
		return &store.PaginationRequest{
			Limit:  50, // 默认值
			Offset: 0,
		}
	}

	return &store.PaginationRequest{
		Limit:  int(protoPagination.Limit),
		Offset: int(protoPagination.Offset),
	}
}

// convertCreatePackageRequestFromProto 转换创建流量包请求从Proto到Domain
func (h *PackageDubboHandler) convertCreatePackageRequestFromProto(protoReq *storepb.CreatePackageRequest) *store.CreatePackageRequest {
	req := &store.CreatePackageRequest{
		PackageName: protoReq.PackageName,
		PackageDesc: protoReq.PackageDesc,
		Entitlement: protoReq.Entitlement,
		EntitlementDesc: protoReq.EntitlementDesc, // TODO: 等protobuf生成后启用
		OriginalPrice: protoReq.OriginalPrice,
		DiscountDesc:    protoReq.DiscountDesc, // TODO: 等protobuf生成后启用
		SaleStatus: protoReq.SaleStatus,
		Currency:   protoReq.Currency,
		Country:    protoReq.Country,
		Extra1:     protoReq.Extra1,
		Extra2:     protoReq.Extra2,
		Extra3:     protoReq.Extra3,
		Extra4:     protoReq.Extra4,
	}

	if protoReq.DiscountPrice != nil {
		req.DiscountPrice = protoReq.DiscountPrice
	}
	if protoReq.DiscountStartTime != nil {
		t := protoReq.DiscountStartTime.AsTime()
		req.DiscountStartTime = &t
	}
	if protoReq.DiscountEndTime != nil {
		t := protoReq.DiscountEndTime.AsTime()
		req.DiscountEndTime = &t
	}

	return req
}

// convertUpdatePackageRequestFromProto 转换更新流量包请求从Proto到Domain
func (h *PackageDubboHandler) convertUpdatePackageRequestFromProto(protoReq *storepb.UpdatePackageRequest) *store.UpdatePackageRequest {
	req := &store.UpdatePackageRequest{
		PackageID: protoReq.PackageId,
	}

	if protoReq.PackageName != nil {
		req.PackageName = protoReq.PackageName
	}
	if protoReq.PackageDesc != nil {
		req.PackageDesc = protoReq.PackageDesc
	}
	if protoReq.Entitlement != nil {
		req.Entitlement = protoReq.Entitlement
	}
	if protoReq.OriginalPrice != nil {
		req.OriginalPrice = protoReq.OriginalPrice
	}
	if protoReq.DiscountPrice != nil {
		req.DiscountPrice = protoReq.DiscountPrice
	}
	if protoReq.DiscountStartTime != nil {
		t := protoReq.DiscountStartTime.AsTime()
		req.DiscountStartTime = &t
	}
	if protoReq.DiscountEndTime != nil {
		t := protoReq.DiscountEndTime.AsTime()
		req.DiscountEndTime = &t
	}
	if protoReq.SaleStatus != nil {
		req.SaleStatus = protoReq.SaleStatus
	}
	if protoReq.Currency != nil {
		req.Currency = protoReq.Currency
	}
	if protoReq.Country != nil {
		req.Country = protoReq.Country
	}
	if protoReq.Extra1 != nil {
		req.Extra1 = protoReq.Extra1
	}
	if protoReq.Extra2 != nil {
		req.Extra2 = protoReq.Extra2
	}
	if protoReq.Extra3 != nil {
		req.Extra3 = protoReq.Extra3
	}
	if protoReq.Extra4 != nil {
		req.Extra4 = protoReq.Extra4
	}

	return req
}

// convertListPackagesResponseToProto 转换流量包列表响应从Domain到Proto
func (h *PackageDubboHandler) convertListPackagesResponseToProto(response *store.ListPackagesResponse) *storepb.ListAllPackagesResponse {
	protoPackages := make([]*storepb.PackageResponse, len(response.Packages))
	for i, pkg := range response.Packages {
		protoPackages[i] = h.convertPackageResponseToProto(pkg)
	}

	return &storepb.ListAllPackagesResponse{
		Packages: protoPackages,
		Pagination: &storepb.PaginationResponse{
			Total:     response.Pagination.Total,
			Limit:     int32(response.Pagination.Limit),
			Offset:    int32(response.Pagination.Offset),
			Remaining: response.Pagination.Remaining,
		},
	}
}

// convertAdminListPackagesResponseToProto 转换管理员流量包列表响应从Domain到Proto
func (h *PackageDubboHandler) convertAdminListPackagesResponseToProto(response *store.AdminListPackagesResponse) *storepb.AdminListAllPackagesResponse {
	protoPackages := make([]*storepb.AdminPackageResponse, len(response.Packages))
	for i, pkg := range response.Packages {
		protoPackages[i] = h.convertAdminPackageResponseToProto(pkg)
	}

	return &storepb.AdminListAllPackagesResponse{
		Packages: protoPackages,
		Pagination: &storepb.PaginationResponse{
			Total:     response.Pagination.Total,
			Limit:     int32(response.Pagination.Limit),
			Offset:    int32(response.Pagination.Offset),
			Remaining: response.Pagination.Remaining,
		},
	}
}

// convertPackageResponseToProto 转换流量包响应从Domain到Proto
func (h *PackageDubboHandler) convertPackageResponseToProto(pkg *store.PackageResponse) *storepb.PackageResponse {
	protoPackage := &storepb.PackageResponse{
		PackageId:   pkg.PackageID,
		PackageName: pkg.PackageName,
		PackageDesc: pkg.PackageDesc,
		Entitlement: pkg.Entitlement,
		Price:       pkg.Price,
		SaleStatus:  pkg.SaleStatus,
		Currency:    pkg.Currency,
		Country:     pkg.Country,
	}

	if pkg.OriginalPrice != nil {
		protoPackage.OriginalPrice = pkg.OriginalPrice
	}
	if pkg.DiscountStartTime != nil {
		protoPackage.DiscountStartTime = timestamppb.New(*pkg.DiscountStartTime)
	}
	if pkg.DiscountEndTime != nil {
		protoPackage.DiscountEndTime = timestamppb.New(*pkg.DiscountEndTime)
	}

	return protoPackage
}

// convertAdminPackageResponseToProto 转换管理员流量包响应从Domain到Proto
func (h *PackageDubboHandler) convertAdminPackageResponseToProto(pkg *store.AdminPackageResponse) *storepb.AdminPackageResponse {
	protoPackage := &storepb.AdminPackageResponse{
		Id:            pkg.ID,
		PackageId:     pkg.PackageID,
		PackageName:   pkg.PackageName,
		PackageDesc:   pkg.PackageDesc,
		Entitlement:   pkg.Entitlement,
		OriginalPrice: pkg.OriginalPrice,
		SaleStatus:    pkg.SaleStatus,
		Currency:      pkg.Currency,
		Country:       pkg.Country,
		Extra1:        pkg.Extra1,
		Extra2:        pkg.Extra2,
		Extra3:        pkg.Extra3,
		Extra4:        pkg.Extra4,
		CreatedAt:     timestamppb.New(*pkg.CreatedAt),
		UpdatedAt:     timestamppb.New(*pkg.UpdatedAt),
	}

	if pkg.DiscountPrice != nil {
		protoPackage.DiscountPrice = pkg.DiscountPrice
	}
	if pkg.DiscountStartTime != nil {
		protoPackage.DiscountStartTime = timestamppb.New(*pkg.DiscountStartTime)
	}
	if pkg.DiscountEndTime != nil {
		protoPackage.DiscountEndTime = timestamppb.New(*pkg.DiscountEndTime)
	}

	return protoPackage
}
