package service

import (
	"fmt"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"payment-backend/internal/config"
	"payment-backend/internal/db"
	"payment-backend/internal/domain"
	"payment-backend/internal/logger"
	"payment-backend/internal/middleware"

	"payment-sdk/payment"
)

// orderService 订单服务实现
type orderService struct {
	orderRepo domain.OrderRepository
	gateways  map[string]payment.PaymentGateway
	config    *config.Config
	logger    logger.Logger
}

// NewOrderService 创建订单服务
func NewOrderService(
	orderRepo domain.OrderRepository,
	gateways map[string]payment.PaymentGateway,
	config *config.Config,
	logger logger.Logger,
) domain.OrderService {
	return &orderService{
		orderRepo: orderRepo,
		gateways:  gateways,
		config:    config,
		logger:    logger,
	}
}

// CreateOrder 创建订单
func (s *orderService) CreateOrder(userCtx *middleware.UserContext, req *domain.CreateOrderRequest) (*domain.CreateOrderResponse, error) {
	var rsp *payment.CreateCheckoutRsp
	var order *domain.Order

	err := db.WithTransaction(func(tx *gorm.DB) error {
		// 在事务中创建订单
		order = domain.NewOrder(userCtx.UserID, req, "")
		if err := s.orderRepo.CreateWithTransaction(tx, order); err != nil {
			return err
		}

		// 创建支付会话（如果失败会回滚订单创建）
		gateway, exists := s.gateways[req.PSPProvider]
		if !exists {
			return fmt.Errorf("payment gateway [%s] not supported", req.PSPProvider)
		}

		// 从配置中获取 SuccessURL 和 CancelURL
		providerConfig, exists := s.config.Payment.Providers[req.PSPProvider]
		if !exists {
			return fmt.Errorf("payment provider [%s] unsupported", req.PSPProvider)
		}

		var err error
		// providerConfig.SuccessURL, providerConfig.CancelURL
		req := &payment.CheckoutReq{
			OrderID:    order.OrderID,
			UserID:     order.UserID,
			ProductID:  order.PriceID,
			PriceID:    order.PriceID,
			Quantity:   order.Quantity,
			SuccessURL: providerConfig.SuccessURL,
			CancelURL:  providerConfig.CancelURL,
		}
		rsp, err = gateway.CreateCheckout(req)
		if err != nil {
			return err
		}

		if err := s.orderRepo.UpdatePaymentIDWithTransaction(tx, order.OrderID, rsp.PaymentID); err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return &domain.CreateOrderResponse{
		OrderID:     order.OrderID,
		CheckoutURL: rsp.CheckoutURL,
		Amount:      order.Amount,
		Currency:    order.Currency,
		ExpiresAt:   time.Now().Add(24 * time.Hour), // 24小时后过期
	}, nil
}

// GetOrder 根据订单ID获取订单
func (s *orderService) GetOrder(orderID string) (*domain.Order, error) {
	order, err := s.orderRepo.GetByOrderID(orderID)
	if err != nil {
		s.logger.Error("Failed to get order", zap.String("order_id", orderID), zap.Error(err))
		return nil, err
	}

	return order, nil
}

// GetOrderByID 根据数据库ID获取订单
func (s *orderService) GetOrderByID(id uint64) (*domain.Order, error) {
	order, err := s.orderRepo.GetByID(id)
	if err != nil {
		s.logger.Error("Failed to get order by ID", zap.Uint64("id", id), zap.Error(err))
		return nil, err
	}

	return order, nil
}

// GetUserOrders 获取用户的订单列表
func (s *orderService) GetUserOrders(userCtx *middleware.UserContext, filter *domain.OrderFilter, pagination *domain.PaginationRequest) (*domain.GetUserOrdersResponse, error) {
	s.logger.Info("orderService::GetUserOrders",
		zap.Any("filter", filter),
		zap.Any("pagination", pagination))

	// 调用仓储层获取数据
	orders, total, err := s.orderRepo.GetByUserID(userCtx.UserID, filter, pagination)
	if err != nil {
		s.logger.Error("Failed to list orders with filters",
			zap.Any("filter", filter),
			zap.Any("pagination", pagination),
			zap.Error(err))
		return nil, err
	}

	// 计算剩余记录数
	remaining := total - int64(pagination.Offset+len(orders))
	if remaining < 0 {
		remaining = 0
	}
	s.logger.Debug("s.orderRepo.ListWithFilters",
		zap.Any("total", total),
		zap.Any("len(orders)", len(orders)),
		zap.Any("remaining", remaining),
		zap.Any("pagination.Offset", pagination.Offset),
		zap.Any("orders", orders))

	userOrders := make([]*domain.UserOrderResponse, 0)
	for _, order := range orders {
		uo := &domain.UserOrderResponse{
			OrderID:      order.OrderID,
			ProductDesc:  order.ProductDesc,
			Quantity:     order.Quantity,
			Amount:       order.Amount,
			NetAmount:    order.NetAmount,
			Currency:     order.Currency,
			PayStatus:    order.PayStatus,
			PayedMethod:  order.PayedMethod,
			PSPProvider:  order.PSPProvider,
			CardNumber:   order.CardNumber,
			PayedAt:      order.PayedAt,
			RefundStatus: order.RefundStatus,
			RefundedAt:   order.RefundedAt}
		userOrders = append(userOrders, uo)
	}
	// 构建响应
	response := &domain.GetUserOrdersResponse{
		UserOrders: userOrders,
		Pagination: &domain.PaginationResponse{
			Total:     total,
			Limit:     pagination.Limit,
			Offset:    pagination.Offset,
			Remaining: remaining,
		},
	}

	s.logger.Info("Successfully GetUserOrders",
		zap.Int("count", len(orders)),
		zap.Int64("total", total),
		zap.Int64("remaining", remaining))

	return response, nil
}

// ListAllOrders 获取所有订单列表（管理员接口）
func (s *orderService) ListAllOrders(filter *domain.OrderFilter, pagination *domain.PaginationRequest) (*domain.ListOrdersResponse, error) {
	s.logger.Info("orderService::ListAllOrders",
		zap.Any("filter", filter),
		zap.Any("pagination", pagination))

	// 调用仓储层获取数据
	orders, total, err := s.orderRepo.ListWithFilters(filter, pagination)
	if err != nil {
		s.logger.Error("Failed to list orders with filters",
			zap.Any("filter", filter),
			zap.Any("pagination", pagination),
			zap.Error(err))
		return nil, err
	}

	// 计算剩余记录数
	remaining := total - int64(pagination.Offset+len(orders))
	if remaining < 0 {
		remaining = 0
	}
	s.logger.Debug("s.orderRepo.ListWithFilters",
		zap.Any("total", total),
		zap.Any("len(orders)", len(orders)),
		zap.Any("remaining", remaining),
		zap.Any("pagination.Offset", pagination.Offset),
		zap.Any("orders", orders))

	// 构建响应
	response := &domain.ListOrdersResponse{
		Orders: orders,
		Pagination: &domain.PaginationResponse{
			Total:     total,
			Limit:     pagination.Limit,
			Offset:    pagination.Offset,
			Remaining: remaining,
		},
	}

	s.logger.Info("Successfully listed orders",
		zap.Int("count", len(orders)),
		zap.Int64("total", total),
		zap.Int64("remaining", remaining))

	return response, nil
}

// UpdateOrder 更新订单
func (s *orderService) UpdateOrder(orderID string, req *domain.UpdateOrderRequest) error {
	s.logger.Info("Updating order", zap.String("order_id", orderID))

	err := db.WithTransaction(func(tx *gorm.DB) error {
		// 在事务中获取现有订单（使用行锁防止并发修改）
		order, err := s.orderRepo.GetByOrderIDWithTransaction(tx, orderID)
		if err != nil {
			return err
		}

		// 更新字段
		if req.PayStatus != "" {
			order.PayStatus = req.PayStatus
		}
		if req.PayedMethod != "" {
			order.PayedMethod = req.PayedMethod
		}
		if req.CardNumber != "" {
			order.CardNumber = req.CardNumber
		}
		if req.PayedAt != nil {
			order.PayedAt = req.PayedAt
		}
		if req.RefundStatus != "" {
			order.RefundStatus = req.RefundStatus
		}
		if req.RefundedAt != nil {
			order.RefundedAt = req.RefundedAt
		}
		if req.PSPPaymentID != "" {
			order.PSPPaymentID = req.PSPPaymentID
		}
		if req.PSPCustomerID != "" {
			order.PSPCustomerID = req.PSPCustomerID
		}
		if req.PSPCustomerEmail != "" {
			order.PSPCustomerEmail = req.PSPCustomerEmail
		}
		if req.PSPSubscriptionID != "" {
			order.PSPSubscriptionID = req.PSPSubscriptionID
		}
		if req.NetAmount != nil {
			order.NetAmount = *req.NetAmount
		}

		now := time.Now()
		order.UpdatedAt = &now

		// 在事务中保存更新
		return s.orderRepo.UpdateWithTransaction(tx, order)
	})

	if err != nil {
		s.logger.Error("Failed to update order", zap.String("order_id", orderID), zap.Error(err))
		return fmt.Errorf("failed to update order: %w", err)
	}

	s.logger.Info("Order updated successfully", zap.String("order_id", orderID))
	return nil
}

// ProcessWebhook 处理支付网关的webhook
// 这里不确定 MQ 缓存事件是否必须。简化起见，先并发处理。
func (s *orderService) ProcessWebhook(provider string, header map[string][]string, payload []byte) error {
	s.logger.Info("Processing webhook", zap.String("provider", provider))

	// 获取支付网关
	gateway, exists := s.gateways[provider]
	if !exists {
		return fmt.Errorf("payment gateway [%s] not supported", provider)
	}

	if err := gateway.Webhook(header, payload, s.paymentEventCb); err != nil {
		s.logger.Error("Failed to call webhook", zap.Error(err))
		return err
	}
	s.logger.Info("Webhook processed successfully", zap.String("provider", provider))
	return nil
}

// CancelOrder 取消订单
func (s *orderService) CancelOrder(orderID string) error {
	s.logger.Info("Cancelling order", zap.String("order_id", orderID))

	err := db.WithTransaction(func(tx *gorm.DB) error {
		// 在事务中获取订单（使用行锁防止并发修改）
		order, err := s.orderRepo.GetByOrderIDWithTransaction(tx, orderID)
		if err != nil {
			return err
		}

		if !order.IsCancellable() {
			return fmt.Errorf("order %s cannot be cancelled, current status: %s", orderID, order.PayStatus)
		}

		order.MarkAsCancelled()

		// 在事务中更新订单状态
		return s.orderRepo.UpdateWithTransaction(tx, order)
	})

	if err != nil {
		s.logger.Error("Failed to cancel order", zap.String("order_id", orderID), zap.Error(err))
		return fmt.Errorf("failed to cancel order: %w", err)
	}

	s.logger.Info("Order cancelled successfully", zap.String("order_id", orderID))
	return nil
}

// RefundOrder 订单退款
func (s *orderService) RefundOrder(orderID string, amount *float64) error {
	s.logger.Info("Processing refund for order",
		zap.String("order_id", orderID),
		zap.Float64p("amount", amount))

	var refundAmount float64
	var order *domain.Order

	err := db.WithTransaction(func(tx *gorm.DB) error {
		// 在事务中获取订单（使用行锁防止并发修改）
		var err error
		order, err = s.orderRepo.GetByOrderIDWithTransaction(tx, orderID)
		if err != nil {
			return err
		}

		if !order.IsRefundable() {
			return fmt.Errorf("order %s cannot be refunded, current status: %s, refund status: %s",
				orderID, order.PayStatus, order.RefundStatus)
		}

		// 如果没有指定退款金额，则全额退款
		refundAmount = order.Amount
		if amount != nil {
			refundAmount = *amount
		}

		// 调用支付网关进行退款
		gateway, exists := s.gateways[order.PSPProvider]
		if !exists {
			return fmt.Errorf("payment gateway [%s] not supported", order.PSPProvider)
		}

		refundID, err := gateway.RefundPayment(orderID, order.PSPPaymentID, order.PSPPaymentIntentID, refundAmount)
		if err != nil {
			s.logger.Error("Failed to refund payment",
				zap.String("order_id", orderID),
				zap.String("psp_payment_id", order.PSPPaymentID),
				zap.Error(err))
			return fmt.Errorf("failed to refund payment: %w", err)
		}

		// 更新订单状态
		order.MarkAsRefundedRequested(refundAmount)
		order.PSPPaymentRefundID = refundID

		// 在事务中更新订单状态
		return s.orderRepo.UpdateWithTransaction(tx, order)
	})

	if err != nil {
		s.logger.Error("Failed to process refund", zap.String("order_id", orderID), zap.Error(err))
		return fmt.Errorf("failed to process refund: %w", err)
	}

	s.logger.Info("Order refunded successfully",
		zap.String("order_id", orderID),
		zap.Float64("refund_amount", refundAmount))
	return nil
}

// 订单支付事件处理
func (s *orderService) paymentEventCb(ev string, orderID string, userID string, msg string) error {
	s.logger.Info("paymentEventCb",
		zap.String("event", ev),
		zap.String("orderID", orderID),
		zap.String("userID", userID))

	switch ev {
	case payment.PaymentEventPending:
		db.WithTransaction(func(tx *gorm.DB) error {
			return s.orderRepo.UpdateStatusOtherWithTransaction(tx, orderID, domain.PayStatusPaid, msg)
		},
		)

	case payment.PaymentEventSucceeded:
		db.WithTransaction(func(tx *gorm.DB) error {
			// TODO: 支付成功后先履约

			// 履约之后再更新订单状态
			return s.orderRepo.UpdateStatusSucceededWithTransaction(tx, orderID, domain.PayStatusSucceeded, msg)
		},
		)

	case payment.PaymentEventCanceled:
		db.WithTransaction(func(tx *gorm.DB) error {
			return s.orderRepo.UpdateStatusOtherWithTransaction(tx, orderID, domain.PayStatusCancelled, msg)
		},
		)

	case payment.PaymentEventFailed:
		db.WithTransaction(func(tx *gorm.DB) error {
			return s.orderRepo.UpdateStatusOtherWithTransaction(tx, orderID, domain.PayStatusFailed, msg)
		},
		)

	case payment.PaymentEventExpired:
		db.WithTransaction(func(tx *gorm.DB) error {
			return s.orderRepo.UpdateStatusOtherWithTransaction(tx, orderID, domain.PayStatusExpired, msg)
		},
		)

	case payment.RefundEventSucceeded:
		db.WithTransaction(func(tx *gorm.DB) error {
			return s.orderRepo.UpdateRefundSucceededWithTransaction(tx, orderID, domain.RefundStatusSucceeded, "")
		},
		)

	case payment.RefundEventFailed:
		db.WithTransaction(func(tx *gorm.DB) error {
			return s.orderRepo.UpdateRefundStatusFailedWithTransaction(tx, orderID, domain.RefundStatusFailed, msg)
		},
		)

	}

	return nil
}
