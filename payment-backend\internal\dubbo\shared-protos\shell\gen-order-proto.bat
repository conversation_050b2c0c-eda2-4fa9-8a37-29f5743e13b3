@echo off
setlocal


set PROTO_DIR=../..


set OUT_DIR=gen


if not exist %OUT_DIR% (
    mkdir %OUT_DIR%
)
if not exist %OUT_DIR%\\python (
    mkdir %OUT_DIR%\\python
)
if not exist %OUT_DIR%\\go (
    mkdir %OUT_DIR%\\go
)

if not exist %OUT_DIR%\\go_for_gateway (
    mkdir %OUT_DIR%\\go_for_gateway
)


cd gen/go

protoc -I=%PROTO_DIR% ^
       -I=%PROTO_DIR%/third_party/googleapis ^
       --go_out=.  ^
       --go-grpc_out=.  ^
       --go-triple_out=.  ^
       %PROTO_DIR%\protos\order.proto


cd ../go_for_gateway
protoc -I=%PROTO_DIR% ^
       -I=%PROTO_DIR%/third_party/googleapis ^
       --go_out=.  ^
       --go-grpc_out=.  ^
       --grpc-gateway_out=.  ^
       %PROTO_DIR%\protos\order.proto



if errorlevel 1 (
    echo [ERROR] protoc xxxx  failed
    exit /b 1
) else (
    echo [OK] xxxx %OUT_DIR%
)

endlocal
