@echo off
setlocal


set PROTO_DIR=.


set OUT_DIR=gen


if not exist %OUT_DIR% (
    mkdir %OUT_DIR%
)
if not exist %OUT_DIR%\\python (
    mkdir %OUT_DIR%\\python
)
if not exist %OUT_DIR%\\go (
    mkdir %OUT_DIR%\\go
)
if not exist %OUT_DIR%\\docs (
    mkdir %OUT_DIR%\\docs
)


protoc -I=%PROTO_DIR% ^
       -I=%PROTO_DIR%/third_party/googleapis ^
       --go_out=%OUT_DIR%/go  --go-grpc_out=%OUT_DIR%/go  ^
       --openapiv2_out=%OUT_DIR%/docs ^
       %PROTO_DIR%\user\user.proto

python -m grpc_tools.protoc -I%PROTO_DIR% -I%PROTO_DIR%/third_party/googleapis  --python_out=%OUT_DIR%/python --grpc_python_out=%OUT_DIR%/python %PROTO_DIR%\user\user.proto


if errorlevel 1 (
    echo [ERROR] protoc xxxx  failed
    exit /b 1
) else (
    echo [OK] xxxx %OUT_DIR%
)

endlocal
