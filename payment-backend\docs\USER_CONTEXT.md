# 用户上下文 (User Context) 使用指南

## 概述

用户上下文是一个优雅的解决方案，用于在 HTTP 接口中获取和使用用户信息（如 `x-user-id` 和 `x-role` 头部）。通过中间件的方式，我们可以在多个接口中统一处理用户认证和授权。

## 核心组件

### 1. UserContext 结构

```go
type UserContext struct {
    UserID string `json:"user_id"`
    Role   string `json:"role"`
}
```

### 2. 中间件

- **AuthMiddleware**: 强制要求认证，缺少用户信息时返回 401
- **OptionalAuthMiddleware**: 可选认证，有用户信息时设置上下文，没有时继续执行
- **RequireRole**: 角色验证中间件，检查用户是否有指定角色权限

### 3. 辅助函数

- **GetUserContext**: 安全获取用户上下文，返回 (userCtx, exists)
- **MustGetUserContext**: 强制获取用户上下文，不存在时 panic

## 使用方法

### 1. 在路由中配置中间件

```go
// 设置需要认证的路由
authenticated := router.Group("/api/v1/pay-service")
authenticated.Use(middleware.AuthMiddleware(logger))
{
    authenticated.POST("/checkout/session", handler.CreateCheckoutSession)
    authenticated.GET("/payments/:id", handler.GetPayment)
}

// 设置管理员路由
admin := router.Group("/api/v1/pay-service/admin")
admin.Use(middleware.AuthMiddleware(logger))
admin.Use(middleware.RequireRole("admin", "super_admin"))
{
    admin.GET("/payments", handler.ListAllPayments)
}

// 设置可选认证的路由（如 Webhook）
webhooks := router.Group("/api/v1/pay-service/webhooks")
webhooks.Use(middleware.OptionalAuthMiddleware(logger))
{
    webhooks.POST("/:provider", handler.ProcessWebhook)
}
```

### 2. 在处理器中使用用户上下文

#### 强制获取用户上下文

```go
func (h *PaymentHandler) CreateCheckoutSession(c *gin.Context) {
    // 获取用户上下文（必须存在）
    userCtx := middleware.MustGetUserContext(c)
    
    // 使用用户信息
    h.logger.Info("Creating checkout session",
        logger.String("user_id", userCtx.UserID),
        logger.String("role", userCtx.Role),
    )
    
    // 业务逻辑...
}
```

#### 可选获取用户上下文

```go
func (h *PaymentHandler) ProcessWebhook(c *gin.Context) {
    // 获取用户上下文（可能不存在）
    userCtx, hasUserCtx := middleware.GetUserContext(c)
    
    logFields := []logger.Field{
        logger.String("provider", providerStr),
    }
    if hasUserCtx {
        logFields = append(logFields, 
            logger.String("user_id", userCtx.UserID),
            logger.String("role", userCtx.Role),
        )
    }
    
    h.logger.Info("Processing webhook", logFields...)
    
    // 业务逻辑...
}
```

### 3. 根据用户角色执行不同逻辑

```go
func (h *PaymentHandler) GetPayments(c *gin.Context) {
    userCtx := middleware.MustGetUserContext(c)
    
    switch userCtx.Role {
    case "admin", "super_admin":
        // 管理员可以查看所有支付
        payments, err := h.paymentService.GetAllPayments()
    case "customer":
        // 客户只能查看自己的支付
        payments, err := h.paymentService.GetPaymentsByCustomer(userCtx.UserID)
    default:
        c.JSON(http.StatusForbidden, gin.H{
            "error": "insufficient_permissions",
            "message": "Your role does not have access to this resource",
        })
        return
    }
    
    // 处理结果...
}
```

## HTTP 请求示例

### 1. 成功的认证请求

```bash
curl -X POST http://localhost:8080/api/v1/pay-service/checkout/session \
  -H "Content-Type: application/json" \
  -H "x-user-id: user123" \
  -H "x-role: customer" \
  -d '{
    "customer_id": "customer123",
    "amount": 10000,
    "currency": "USD",
    "provider": "stripe",
    "success_url": "https://example.com/success",
    "cancel_url": "https://example.com/cancel",
    "description": "Test payment"
  }'
```

### 2. 缺少认证头部的请求（失败）

```bash
curl -X POST http://localhost:8080/api/v1/pay-service/checkout/session \
  -H "Content-Type: application/json" \
  -d '{
    "customer_id": "customer123",
    "amount": 10000,
    "currency": "USD",
    "provider": "stripe",
    "success_url": "https://example.com/success",
    "cancel_url": "https://example.com/cancel",
    "description": "Test payment"
  }'

# 响应: 401 Unauthorized
{
  "error": "unauthorized",
  "message": "Missing user ID in request headers"
}
```

### 3. 权限不足的请求（失败）

```bash
curl -X GET http://localhost:8080/api/v1/pay-service/admin/payments \
  -H "x-user-id: user123" \
  -H "x-role: customer"

# 响应: 403 Forbidden
{
  "error": "forbidden",
  "message": "Insufficient permissions"
}
```

## 测试

### 1. 单元测试

```go
func TestCreateCheckoutSession_WithUserContext(t *testing.T) {
    // 设置模拟对象
    mockService := &MockPaymentService{}
    mockLogger := &MockLogger{}
    
    // 创建处理器
    handler := NewPaymentHandler(mockService, mockLogger)
    
    // 设置路由（带认证中间件）
    router := gin.New()
    router.Use(middleware.AuthMiddleware(mockLogger))
    router.POST("/checkout/session", handler.CreateCheckoutSession)
    
    // 创建请求（带用户头部）
    req := httptest.NewRequest("POST", "/checkout/session", strings.NewReader(`{...}`))
    req.Header.Set("Content-Type", "application/json")
    req.Header.Set("x-user-id", "user123")
    req.Header.Set("x-role", "customer")
    
    // 执行请求
    w := httptest.NewRecorder()
    router.ServeHTTP(w, req)
    
    // 验证结果
    assert.Equal(t, http.StatusOK, w.Code)
}
```

### 2. 集成测试

参考 `examples/user_context_example.go` 中的完整示例。

## 最佳实践

### 1. 日志记录

始终在日志中包含用户信息，用于审计和调试：

```go
h.logger.Info("Operation completed",
    logger.String("user_id", userCtx.UserID),
    logger.String("role", userCtx.Role),
    logger.String("operation", "create_payment"),
    logger.String("resource_id", paymentID),
)
```

### 2. 错误处理

提供清晰的错误消息：

```go
if userCtx.Role != "admin" {
    h.logger.Warn("Unauthorized access attempt",
        logger.String("user_id", userCtx.UserID),
        logger.String("role", userCtx.Role),
        logger.String("required_role", "admin"),
    )
    c.JSON(http.StatusForbidden, gin.H{
        "error": "insufficient_permissions",
        "message": "Admin role required for this operation",
    })
    return
}
```

### 3. 数据过滤

根据用户权限过滤数据：

```go
func (s *PaymentService) GetPayments(userCtx *middleware.UserContext) ([]*domain.Payment, error) {
    if userCtx.Role == "admin" {
        return s.repo.GetAllPayments()
    }
    return s.repo.GetPaymentsByCustomer(userCtx.UserID)
}
```

### 4. 中间件顺序

确保中间件的正确顺序：

```go
router.Use(gin.Logger())                    // 1. 日志
router.Use(gin.Recovery())                  // 2. 恢复
router.Use(middleware.AuthMiddleware(log))  // 3. 认证
router.Use(middleware.RequireRole("admin")) // 4. 授权
```

## 安全考虑

### 1. 头部验证

在生产环境中，应该验证 `x-user-id` 和 `x-role` 头部的真实性，通常通过：
- JWT Token 验证
- API Gateway 验证
- 内部服务间的安全通信

### 2. 角色验证

确保角色信息来自可信源，不能仅依赖客户端提供的头部。

### 3. 审计日志

记录所有用户操作，包括：
- 用户ID
- 操作类型
- 资源ID
- 时间戳
- 结果状态

## 扩展

### 1. 添加更多用户信息

```go
type UserContext struct {
    UserID      string   `json:"user_id"`
    Role        string   `json:"role"`
    Permissions []string `json:"permissions"`
    TenantID    string   `json:"tenant_id"`
}
```

### 2. 自定义认证逻辑

```go
func CustomAuthMiddleware(authService AuthService) gin.HandlerFunc {
    return func(c *gin.Context) {
        token := c.GetHeader("Authorization")
        userCtx, err := authService.ValidateToken(token)
        if err != nil {
            c.JSON(http.StatusUnauthorized, gin.H{"error": "invalid_token"})
            c.Abort()
            return
        }
        c.Set(string(middleware.UserContextKey), userCtx)
        c.Next()
    }
}
```

这个用户上下文系统提供了一个优雅、可扩展的方式来处理用户认证和授权，使得在多个接口中使用用户信息变得简单而一致。
