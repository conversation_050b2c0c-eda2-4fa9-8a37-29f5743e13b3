// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v6.31.1
// source: protos/market.proto

package marketpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	MarketService_ShareBook_FullMethodName     = "/user.MarketService/ShareBook"
	MarketService_BookDetail_FullMethodName    = "/user.MarketService/BookDetail"
	MarketService_BookModify_FullMethodName    = "/user.MarketService/BookModify"
	MarketService_SearchBook_FullMethodName    = "/user.MarketService/SearchBook"
	MarketService_ThemeTopk_FullMethodName     = "/user.MarketService/ThemeTopk"
	MarketService_ThemeBookTopk_FullMethodName = "/user.MarketService/ThemeBookTopk"
)

// MarketServiceClient is the client API for MarketService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type MarketServiceClient interface {
	// 共享绘本
	ShareBook(ctx context.Context, in *ShareBookRequest, opts ...grpc.CallOption) (*Empty, error)
	// 绘本详情
	BookDetail(ctx context.Context, in *BookDetailRequest, opts ...grpc.CallOption) (*BookDetailResponse, error)
	// 绘本属性修改(如审核，下架, 删除)
	BookModify(ctx context.Context, in *BookModifyRequest, opts ...grpc.CallOption) (*Empty, error)
	// 搜索绘本（按主题、按标题）， 按热度排序返回
	SearchBook(ctx context.Context, in *SearchBookRequest, opts ...grpc.CallOption) (*SearchBookResponse, error)
	// 绘本主题 topK
	ThemeTopk(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*ThemeTopkResponse, error)
	// 指定主题下的绘本 topK
	ThemeBookTopk(ctx context.Context, in *ThemeBookTopkRequest, opts ...grpc.CallOption) (*ThemeBookTopkResponse, error)
}

type marketServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewMarketServiceClient(cc grpc.ClientConnInterface) MarketServiceClient {
	return &marketServiceClient{cc}
}

func (c *marketServiceClient) ShareBook(ctx context.Context, in *ShareBookRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, MarketService_ShareBook_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) BookDetail(ctx context.Context, in *BookDetailRequest, opts ...grpc.CallOption) (*BookDetailResponse, error) {
	out := new(BookDetailResponse)
	err := c.cc.Invoke(ctx, MarketService_BookDetail_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) BookModify(ctx context.Context, in *BookModifyRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, MarketService_BookModify_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) SearchBook(ctx context.Context, in *SearchBookRequest, opts ...grpc.CallOption) (*SearchBookResponse, error) {
	out := new(SearchBookResponse)
	err := c.cc.Invoke(ctx, MarketService_SearchBook_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) ThemeTopk(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*ThemeTopkResponse, error) {
	out := new(ThemeTopkResponse)
	err := c.cc.Invoke(ctx, MarketService_ThemeTopk_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) ThemeBookTopk(ctx context.Context, in *ThemeBookTopkRequest, opts ...grpc.CallOption) (*ThemeBookTopkResponse, error) {
	out := new(ThemeBookTopkResponse)
	err := c.cc.Invoke(ctx, MarketService_ThemeBookTopk_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MarketServiceServer is the server API for MarketService service.
// All implementations must embed UnimplementedMarketServiceServer
// for forward compatibility
type MarketServiceServer interface {
	// 共享绘本
	ShareBook(context.Context, *ShareBookRequest) (*Empty, error)
	// 绘本详情
	BookDetail(context.Context, *BookDetailRequest) (*BookDetailResponse, error)
	// 绘本属性修改(如审核，下架, 删除)
	BookModify(context.Context, *BookModifyRequest) (*Empty, error)
	// 搜索绘本（按主题、按标题）， 按热度排序返回
	SearchBook(context.Context, *SearchBookRequest) (*SearchBookResponse, error)
	// 绘本主题 topK
	ThemeTopk(context.Context, *Empty) (*ThemeTopkResponse, error)
	// 指定主题下的绘本 topK
	ThemeBookTopk(context.Context, *ThemeBookTopkRequest) (*ThemeBookTopkResponse, error)
	mustEmbedUnimplementedMarketServiceServer()
}

// UnimplementedMarketServiceServer must be embedded to have forward compatible implementations.
type UnimplementedMarketServiceServer struct {
}

func (UnimplementedMarketServiceServer) ShareBook(context.Context, *ShareBookRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ShareBook not implemented")
}
func (UnimplementedMarketServiceServer) BookDetail(context.Context, *BookDetailRequest) (*BookDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BookDetail not implemented")
}
func (UnimplementedMarketServiceServer) BookModify(context.Context, *BookModifyRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BookModify not implemented")
}
func (UnimplementedMarketServiceServer) SearchBook(context.Context, *SearchBookRequest) (*SearchBookResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchBook not implemented")
}
func (UnimplementedMarketServiceServer) ThemeTopk(context.Context, *Empty) (*ThemeTopkResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ThemeTopk not implemented")
}
func (UnimplementedMarketServiceServer) ThemeBookTopk(context.Context, *ThemeBookTopkRequest) (*ThemeBookTopkResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ThemeBookTopk not implemented")
}
func (UnimplementedMarketServiceServer) mustEmbedUnimplementedMarketServiceServer() {}

// UnsafeMarketServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MarketServiceServer will
// result in compilation errors.
type UnsafeMarketServiceServer interface {
	mustEmbedUnimplementedMarketServiceServer()
}

func RegisterMarketServiceServer(s grpc.ServiceRegistrar, srv MarketServiceServer) {
	s.RegisterService(&MarketService_ServiceDesc, srv)
}

func _MarketService_ShareBook_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ShareBookRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).ShareBook(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MarketService_ShareBook_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).ShareBook(ctx, req.(*ShareBookRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_BookDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BookDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).BookDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MarketService_BookDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).BookDetail(ctx, req.(*BookDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_BookModify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BookModifyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).BookModify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MarketService_BookModify_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).BookModify(ctx, req.(*BookModifyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_SearchBook_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchBookRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).SearchBook(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MarketService_SearchBook_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).SearchBook(ctx, req.(*SearchBookRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_ThemeTopk_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).ThemeTopk(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MarketService_ThemeTopk_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).ThemeTopk(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_ThemeBookTopk_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ThemeBookTopkRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).ThemeBookTopk(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MarketService_ThemeBookTopk_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).ThemeBookTopk(ctx, req.(*ThemeBookTopkRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// MarketService_ServiceDesc is the grpc.ServiceDesc for MarketService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MarketService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "user.MarketService",
	HandlerType: (*MarketServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ShareBook",
			Handler:    _MarketService_ShareBook_Handler,
		},
		{
			MethodName: "BookDetail",
			Handler:    _MarketService_BookDetail_Handler,
		},
		{
			MethodName: "BookModify",
			Handler:    _MarketService_BookModify_Handler,
		},
		{
			MethodName: "SearchBook",
			Handler:    _MarketService_SearchBook_Handler,
		},
		{
			MethodName: "ThemeTopk",
			Handler:    _MarketService_ThemeTopk_Handler,
		},
		{
			MethodName: "ThemeBookTopk",
			Handler:    _MarketService_ThemeBookTopk_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "protos/market.proto",
}
