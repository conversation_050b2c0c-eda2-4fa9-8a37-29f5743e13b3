// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v6.31.1
// source: protos/user.proto

package userpb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ChargeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Points float32 `protobuf:"fixed32,1,opt,name=points,proto3" json:"points,omitempty"` // 积分数
	Reason string  `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty"`   // 充值原因
}

func (x *ChargeRequest) Reset() {
	*x = ChargeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_user_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChargeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChargeRequest) ProtoMessage() {}

func (x *ChargeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_protos_user_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChargeRequest.ProtoReflect.Descriptor instead.
func (*ChargeRequest) Descriptor() ([]byte, []int) {
	return file_protos_user_proto_rawDescGZIP(), []int{0}
}

func (x *ChargeRequest) GetPoints() float32 {
	if x != nil {
		return x.Points
	}
	return 0
}

func (x *ChargeRequest) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

type PageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page     int32 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`                         // 页码(从 1 开始)
	PageSize int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"` // 每页数量
}

func (x *PageRequest) Reset() {
	*x = PageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_user_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PageRequest) ProtoMessage() {}

func (x *PageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_protos_user_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PageRequest.ProtoReflect.Descriptor instead.
func (*PageRequest) Descriptor() ([]byte, []int) {
	return file_protos_user_proto_rawDescGZIP(), []int{1}
}

func (x *PageRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *PageRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type PageResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page     int32 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	Total    int32 `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *PageResponse) Reset() {
	*x = PageResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_user_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PageResponse) ProtoMessage() {}

func (x *PageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_protos_user_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PageResponse.ProtoReflect.Descriptor instead.
func (*PageResponse) Descriptor() ([]byte, []int) {
	return file_protos_user_proto_rawDescGZIP(), []int{2}
}

func (x *PageResponse) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *PageResponse) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *PageResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type BenefitRecordResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Records []*BenefitRecord `protobuf:"bytes,1,rep,name=records,proto3" json:"records,omitempty"`
	Page    *PageResponse    `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty"`
}

func (x *BenefitRecordResponse) Reset() {
	*x = BenefitRecordResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_user_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BenefitRecordResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BenefitRecordResponse) ProtoMessage() {}

func (x *BenefitRecordResponse) ProtoReflect() protoreflect.Message {
	mi := &file_protos_user_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BenefitRecordResponse.ProtoReflect.Descriptor instead.
func (*BenefitRecordResponse) Descriptor() ([]byte, []int) {
	return file_protos_user_proto_rawDescGZIP(), []int{3}
}

func (x *BenefitRecordResponse) GetRecords() []*BenefitRecord {
	if x != nil {
		return x.Records
	}
	return nil
}

func (x *BenefitRecordResponse) GetPage() *PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

type BenefitRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id              uint64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	BenefitType     uint32  `protobuf:"varint,2,opt,name=benefit_type,json=benefitType,proto3" json:"benefit_type,omitempty"`              // '权益类型（1-3），0表示扣除积分, 1-3 表示扣除套餐中功能权益数量， 8-表示积分充值',
	BenefitCount    uint32  `protobuf:"varint,3,opt,name=benefit_count,json=benefitCount,proto3" json:"benefit_count,omitempty"`           // '扣减的权益次数',
	PointsUsed      float32 `protobuf:"fixed32,4,opt,name=points_used,json=pointsUsed,proto3" json:"points_used,omitempty"`                // '本次消耗的积分',
	PointsRecharged float32 `protobuf:"fixed32,5,opt,name=points_recharged,json=pointsRecharged,proto3" json:"points_recharged,omitempty"` // '返还或充值的积分',
	Reason          string  `protobuf:"bytes,6,opt,name=reason,proto3" json:"reason,omitempty"`                                            // '使用原因',
	CreatedAt       uint32  `protobuf:"varint,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`                    // '创建时间',
	UpdatedAt       uint32  `protobuf:"varint,8,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`                    // '更新时间',
}

func (x *BenefitRecord) Reset() {
	*x = BenefitRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_user_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BenefitRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BenefitRecord) ProtoMessage() {}

func (x *BenefitRecord) ProtoReflect() protoreflect.Message {
	mi := &file_protos_user_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BenefitRecord.ProtoReflect.Descriptor instead.
func (*BenefitRecord) Descriptor() ([]byte, []int) {
	return file_protos_user_proto_rawDescGZIP(), []int{4}
}

func (x *BenefitRecord) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BenefitRecord) GetBenefitType() uint32 {
	if x != nil {
		return x.BenefitType
	}
	return 0
}

func (x *BenefitRecord) GetBenefitCount() uint32 {
	if x != nil {
		return x.BenefitCount
	}
	return 0
}

func (x *BenefitRecord) GetPointsUsed() float32 {
	if x != nil {
		return x.PointsUsed
	}
	return 0
}

func (x *BenefitRecord) GetPointsRecharged() float32 {
	if x != nil {
		return x.PointsRecharged
	}
	return 0
}

func (x *BenefitRecord) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *BenefitRecord) GetCreatedAt() uint32 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *BenefitRecord) GetUpdatedAt() uint32 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

// 搜索用户
type SearchRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page             int32  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`                         // 页码(从 1 开始)
	PageSize         int32  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"` // 每页数量
	SearchByNickname string `protobuf:"bytes,3,opt,name=searchByNickname,proto3" json:"searchByNickname,omitempty"`  // 根据昵称查询
	SearchByEmail    string `protobuf:"bytes,4,opt,name=searchByEmail,proto3" json:"searchByEmail,omitempty"`        // 根据邮箱查询
	SearchByDeviceId string `protobuf:"bytes,5,opt,name=searchByDeviceId,proto3" json:"searchByDeviceId,omitempty"`  // 根据设备序列号查询
	SearchByUserId   uint64 `protobuf:"varint,6,opt,name=searchByUserId,proto3" json:"searchByUserId,omitempty"`     // 根据用户ID查询
}

func (x *SearchRequest) Reset() {
	*x = SearchRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_user_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchRequest) ProtoMessage() {}

func (x *SearchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_protos_user_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchRequest.ProtoReflect.Descriptor instead.
func (*SearchRequest) Descriptor() ([]byte, []int) {
	return file_protos_user_proto_rawDescGZIP(), []int{5}
}

func (x *SearchRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *SearchRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *SearchRequest) GetSearchByNickname() string {
	if x != nil {
		return x.SearchByNickname
	}
	return ""
}

func (x *SearchRequest) GetSearchByEmail() string {
	if x != nil {
		return x.SearchByEmail
	}
	return ""
}

func (x *SearchRequest) GetSearchByDeviceId() string {
	if x != nil {
		return x.SearchByDeviceId
	}
	return ""
}

func (x *SearchRequest) GetSearchByUserId() uint64 {
	if x != nil {
		return x.SearchByUserId
	}
	return 0
}

type SearchResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Users     []*DetailProfile `protobuf:"bytes,1,rep,name=users,proto3" json:"users,omitempty"`
	Page      int32            `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`                            // 页码
	PageSize  int32            `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`    // 每页数量
	TotalSize int32            `protobuf:"varint,4,opt,name=total_size,json=totalSize,proto3" json:"total_size,omitempty"` // 总数量
}

func (x *SearchResponse) Reset() {
	*x = SearchResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_user_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchResponse) ProtoMessage() {}

func (x *SearchResponse) ProtoReflect() protoreflect.Message {
	mi := &file_protos_user_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchResponse.ProtoReflect.Descriptor instead.
func (*SearchResponse) Descriptor() ([]byte, []int) {
	return file_protos_user_proto_rawDescGZIP(), []int{6}
}

func (x *SearchResponse) GetUsers() []*DetailProfile {
	if x != nil {
		return x.Users
	}
	return nil
}

func (x *SearchResponse) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *SearchResponse) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *SearchResponse) GetTotalSize() int32 {
	if x != nil {
		return x.TotalSize
	}
	return 0
}

type Empty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Empty) Reset() {
	*x = Empty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_user_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Empty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Empty) ProtoMessage() {}

func (x *Empty) ProtoReflect() protoreflect.Message {
	mi := &file_protos_user_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Empty.ProtoReflect.Descriptor instead.
func (*Empty) Descriptor() ([]byte, []int) {
	return file_protos_user_proto_rawDescGZIP(), []int{7}
}

type DeleteSystemPlanRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlanId uint32 `protobuf:"varint,1,opt,name=plan_id,json=planId,proto3" json:"plan_id,omitempty"` // 套餐id
}

func (x *DeleteSystemPlanRequest) Reset() {
	*x = DeleteSystemPlanRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_user_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteSystemPlanRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteSystemPlanRequest) ProtoMessage() {}

func (x *DeleteSystemPlanRequest) ProtoReflect() protoreflect.Message {
	mi := &file_protos_user_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteSystemPlanRequest.ProtoReflect.Descriptor instead.
func (*DeleteSystemPlanRequest) Descriptor() ([]byte, []int) {
	return file_protos_user_proto_rawDescGZIP(), []int{8}
}

func (x *DeleteSystemPlanRequest) GetPlanId() uint32 {
	if x != nil {
		return x.PlanId
	}
	return 0
}

type DeleteSystemFeaturePointsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` // 记录id
}

func (x *DeleteSystemFeaturePointsRequest) Reset() {
	*x = DeleteSystemFeaturePointsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_user_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteSystemFeaturePointsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteSystemFeaturePointsRequest) ProtoMessage() {}

func (x *DeleteSystemFeaturePointsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_protos_user_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteSystemFeaturePointsRequest.ProtoReflect.Descriptor instead.
func (*DeleteSystemFeaturePointsRequest) Descriptor() ([]byte, []int) {
	return file_protos_user_proto_rawDescGZIP(), []int{9}
}

func (x *DeleteSystemFeaturePointsRequest) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 根据国家过滤套餐，如果国家信息为空，则返回全部套餐信息
type SystemPlans struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Plans []*SystemPlan `protobuf:"bytes,1,rep,name=plans,proto3" json:"plans,omitempty"` //
}

func (x *SystemPlans) Reset() {
	*x = SystemPlans{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_user_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SystemPlans) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SystemPlans) ProtoMessage() {}

func (x *SystemPlans) ProtoReflect() protoreflect.Message {
	mi := &file_protos_user_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SystemPlans.ProtoReflect.Descriptor instead.
func (*SystemPlans) Descriptor() ([]byte, []int) {
	return file_protos_user_proto_rawDescGZIP(), []int{10}
}

func (x *SystemPlans) GetPlans() []*SystemPlan {
	if x != nil {
		return x.Plans
	}
	return nil
}

type SystemFeaturePoints struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id             uint32  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                                // 记录ID
	Country        string  `protobuf:"bytes,2,opt,name=country,proto3" json:"country,omitempty"`                                       // 国家, 每个国家一条记录
	Feature1Points float32 `protobuf:"fixed32,3,opt,name=feature1_points,json=feature1Points,proto3" json:"feature1_points,omitempty"` //套餐图文绘本权益用完后，创建一个图文绘本需要扣除的积分数
	Feature2Points float32 `protobuf:"fixed32,4,opt,name=feature2_points,json=feature2Points,proto3" json:"feature2_points,omitempty"` //套餐动态绘本权益用完后，创建一个动态绘本需要扣除的积分数
	Feature3Points float32 `protobuf:"fixed32,5,opt,name=feature3_points,json=feature3Points,proto3" json:"feature3_points,omitempty"`
	CreatedAt      uint32  `protobuf:"varint,6,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"` // 创建时间, 创建或修改记录时，此字段无效
	UpdatedAt      uint32  `protobuf:"varint,7,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"` // 更新时间, 创建或修改记录时，此字段无效
}

func (x *SystemFeaturePoints) Reset() {
	*x = SystemFeaturePoints{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_user_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SystemFeaturePoints) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SystemFeaturePoints) ProtoMessage() {}

func (x *SystemFeaturePoints) ProtoReflect() protoreflect.Message {
	mi := &file_protos_user_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SystemFeaturePoints.ProtoReflect.Descriptor instead.
func (*SystemFeaturePoints) Descriptor() ([]byte, []int) {
	return file_protos_user_proto_rawDescGZIP(), []int{11}
}

func (x *SystemFeaturePoints) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SystemFeaturePoints) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *SystemFeaturePoints) GetFeature1Points() float32 {
	if x != nil {
		return x.Feature1Points
	}
	return 0
}

func (x *SystemFeaturePoints) GetFeature2Points() float32 {
	if x != nil {
		return x.Feature2Points
	}
	return 0
}

func (x *SystemFeaturePoints) GetFeature3Points() float32 {
	if x != nil {
		return x.Feature3Points
	}
	return 0
}

func (x *SystemFeaturePoints) GetCreatedAt() uint32 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *SystemFeaturePoints) GetUpdatedAt() uint32 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

type GetSystemFeaturePointsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Records []*SystemFeaturePoints `protobuf:"bytes,1,rep,name=records,proto3" json:"records,omitempty"`
}

func (x *GetSystemFeaturePointsResponse) Reset() {
	*x = GetSystemFeaturePointsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_user_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSystemFeaturePointsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSystemFeaturePointsResponse) ProtoMessage() {}

func (x *GetSystemFeaturePointsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_protos_user_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSystemFeaturePointsResponse.ProtoReflect.Descriptor instead.
func (*GetSystemFeaturePointsResponse) Descriptor() ([]byte, []int) {
	return file_protos_user_proto_rawDescGZIP(), []int{12}
}

func (x *GetSystemFeaturePointsResponse) GetRecords() []*SystemFeaturePoints {
	if x != nil {
		return x.Records
	}
	return nil
}

type SystemPlan struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlanId        uint32  `protobuf:"varint,1,opt,name=plan_id,json=planId,proto3" json:"plan_id,omitempty"`                      // 套餐ID
	Name          string  `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                                         // 套餐名称
	Description   string  `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`                           // 套餐描述
	Price         float32 `protobuf:"fixed32,4,opt,name=price,proto3" json:"price,omitempty"`                                     // 套餐价格
	PlanType      int32   `protobuf:"varint,5,opt,name=plan_type,json=planType,proto3" json:"plan_type,omitempty"`                // 套餐类型（0-新用户套餐， 1-普通套餐）
	Country       string  `protobuf:"bytes,6,opt,name=country,proto3" json:"country,omitempty"`                                   // 国家
	Currency      string  `protobuf:"bytes,7,opt,name=currency,proto3" json:"currency,omitempty"`                                 // 货币代码
	Feature1Total int32   `protobuf:"varint,8,opt,name=feature1_total,json=feature1Total,proto3" json:"feature1_total,omitempty"` // 权益1 - 图文绘本创建次数
	Feature2Total int32   `protobuf:"varint,9,opt,name=feature2_total,json=feature2Total,proto3" json:"feature2_total,omitempty"` // 权益2 - 动态绘本创建次数
	Feature3Total int32   `protobuf:"varint,10,opt,name=feature3_total,json=feature3Total,proto3" json:"feature3_total,omitempty"`
	CreatedAt     uint32  `protobuf:"varint,11,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"` // 创建时间, 创建或修改记录时，此字段无效
	UpdatedAt     uint32  `protobuf:"varint,12,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"` // 更新时间, 创建或修改记录时，此字段无效
}

func (x *SystemPlan) Reset() {
	*x = SystemPlan{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_user_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SystemPlan) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SystemPlan) ProtoMessage() {}

func (x *SystemPlan) ProtoReflect() protoreflect.Message {
	mi := &file_protos_user_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SystemPlan.ProtoReflect.Descriptor instead.
func (*SystemPlan) Descriptor() ([]byte, []int) {
	return file_protos_user_proto_rawDescGZIP(), []int{13}
}

func (x *SystemPlan) GetPlanId() uint32 {
	if x != nil {
		return x.PlanId
	}
	return 0
}

func (x *SystemPlan) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SystemPlan) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *SystemPlan) GetPrice() float32 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *SystemPlan) GetPlanType() int32 {
	if x != nil {
		return x.PlanType
	}
	return 0
}

func (x *SystemPlan) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *SystemPlan) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *SystemPlan) GetFeature1Total() int32 {
	if x != nil {
		return x.Feature1Total
	}
	return 0
}

func (x *SystemPlan) GetFeature2Total() int32 {
	if x != nil {
		return x.Feature2Total
	}
	return 0
}

func (x *SystemPlan) GetFeature3Total() int32 {
	if x != nil {
		return x.Feature3Total
	}
	return 0
}

func (x *SystemPlan) GetCreatedAt() uint32 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *SystemPlan) GetUpdatedAt() uint32 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

type UserPlan struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlanId         int32  `protobuf:"varint,1,opt,name=plan_id,json=planId,proto3" json:"plan_id,omitempty"`                         // 套餐id
	PlanType       int32  `protobuf:"varint,2,opt,name=plan_type,json=planType,proto3" json:"plan_type,omitempty"`                   // 套餐类型
	ActivatedAt    uint32 `protobuf:"varint,3,opt,name=activated_at,json=activatedAt,proto3" json:"activated_at,omitempty"`          // 秒级时间戳
	ExpiredAt      uint32 `protobuf:"varint,4,opt,name=expired_at,json=expiredAt,proto3" json:"expired_at,omitempty"`                // unix 秒级时间戳
	Feature1Remain int32  `protobuf:"varint,5,opt,name=feature1_remain,json=feature1Remain,proto3" json:"feature1_remain,omitempty"` // 权益1 - 图文绘本创建次数
	Feature2Remain int32  `protobuf:"varint,6,opt,name=feature2_remain,json=feature2Remain,proto3" json:"feature2_remain,omitempty"` // 权益2 - 动态绘本创建次数
	Feature3Remain int32  `protobuf:"varint,7,opt,name=feature3_remain,json=feature3Remain,proto3" json:"feature3_remain,omitempty"`
	Feature1Total  int32  `protobuf:"varint,8,opt,name=feature1_total,json=feature1Total,proto3" json:"feature1_total,omitempty"`
	Feature2Total  int32  `protobuf:"varint,9,opt,name=feature2_total,json=feature2Total,proto3" json:"feature2_total,omitempty"`
	Feature3Total  int32  `protobuf:"varint,10,opt,name=feature3_total,json=feature3Total,proto3" json:"feature3_total,omitempty"`
}

func (x *UserPlan) Reset() {
	*x = UserPlan{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_user_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserPlan) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserPlan) ProtoMessage() {}

func (x *UserPlan) ProtoReflect() protoreflect.Message {
	mi := &file_protos_user_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserPlan.ProtoReflect.Descriptor instead.
func (*UserPlan) Descriptor() ([]byte, []int) {
	return file_protos_user_proto_rawDescGZIP(), []int{14}
}

func (x *UserPlan) GetPlanId() int32 {
	if x != nil {
		return x.PlanId
	}
	return 0
}

func (x *UserPlan) GetPlanType() int32 {
	if x != nil {
		return x.PlanType
	}
	return 0
}

func (x *UserPlan) GetActivatedAt() uint32 {
	if x != nil {
		return x.ActivatedAt
	}
	return 0
}

func (x *UserPlan) GetExpiredAt() uint32 {
	if x != nil {
		return x.ExpiredAt
	}
	return 0
}

func (x *UserPlan) GetFeature1Remain() int32 {
	if x != nil {
		return x.Feature1Remain
	}
	return 0
}

func (x *UserPlan) GetFeature2Remain() int32 {
	if x != nil {
		return x.Feature2Remain
	}
	return 0
}

func (x *UserPlan) GetFeature3Remain() int32 {
	if x != nil {
		return x.Feature3Remain
	}
	return 0
}

func (x *UserPlan) GetFeature1Total() int32 {
	if x != nil {
		return x.Feature1Total
	}
	return 0
}

func (x *UserPlan) GetFeature2Total() int32 {
	if x != nil {
		return x.Feature2Total
	}
	return 0
}

func (x *UserPlan) GetFeature3Total() int32 {
	if x != nil {
		return x.Feature3Total
	}
	return 0
}

type ReduceBenefitRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BenefitType int32 `protobuf:"varint,1,opt,name=benefit_type,json=benefitType,proto3" json:"benefit_type,omitempty"` // 扣减权益类型（0-积分， 1-3 ：对应套餐里的功能权益，
	// 1-图文绘本，2-动态绘本）
	BenefitReduceCount int32   `protobuf:"varint,2,opt,name=benefit_reduce_count,json=benefitReduceCount,proto3" json:"benefit_reduce_count,omitempty"` // 权益扣数量
	PointReduceCount   float32 `protobuf:"fixed32,3,opt,name=point_reduce_count,json=pointReduceCount,proto3" json:"point_reduce_count,omitempty"`      // 积分扣数量, 和权益类型对应
	Reason             string  `protobuf:"bytes,4,opt,name=reason,proto3" json:"reason,omitempty"`                                                      // 扣减原因
}

func (x *ReduceBenefitRequest) Reset() {
	*x = ReduceBenefitRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_user_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReduceBenefitRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReduceBenefitRequest) ProtoMessage() {}

func (x *ReduceBenefitRequest) ProtoReflect() protoreflect.Message {
	mi := &file_protos_user_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReduceBenefitRequest.ProtoReflect.Descriptor instead.
func (*ReduceBenefitRequest) Descriptor() ([]byte, []int) {
	return file_protos_user_proto_rawDescGZIP(), []int{15}
}

func (x *ReduceBenefitRequest) GetBenefitType() int32 {
	if x != nil {
		return x.BenefitType
	}
	return 0
}

func (x *ReduceBenefitRequest) GetBenefitReduceCount() int32 {
	if x != nil {
		return x.BenefitReduceCount
	}
	return 0
}

func (x *ReduceBenefitRequest) GetPointReduceCount() float32 {
	if x != nil {
		return x.PointReduceCount
	}
	return 0
}

func (x *ReduceBenefitRequest) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

type BenefitOrder struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrderId uint64 `protobuf:"varint,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"` // 订单id
}

func (x *BenefitOrder) Reset() {
	*x = BenefitOrder{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_user_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BenefitOrder) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BenefitOrder) ProtoMessage() {}

func (x *BenefitOrder) ProtoReflect() protoreflect.Message {
	mi := &file_protos_user_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BenefitOrder.ProtoReflect.Descriptor instead.
func (*BenefitOrder) Descriptor() ([]byte, []int) {
	return file_protos_user_proto_rawDescGZIP(), []int{16}
}

func (x *BenefitOrder) GetOrderId() uint64 {
	if x != nil {
		return x.OrderId
	}
	return 0
}

type DeviceInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DeviceId   string `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	DeviceName string `protobuf:"bytes,2,opt,name=device_name,json=deviceName,proto3" json:"device_name,omitempty"`
	DeviceType string `protobuf:"bytes,3,opt,name=device_type,json=deviceType,proto3" json:"device_type,omitempty"`
	OsName     string `protobuf:"bytes,4,opt,name=os_name,json=osName,proto3" json:"os_name,omitempty"`
	OsVersion  string `protobuf:"bytes,5,opt,name=os_version,json=osVersion,proto3" json:"os_version,omitempty"`
	AppVersion string `protobuf:"bytes,6,opt,name=app_version,json=appVersion,proto3" json:"app_version,omitempty"`
}

func (x *DeviceInfo) Reset() {
	*x = DeviceInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_user_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeviceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceInfo) ProtoMessage() {}

func (x *DeviceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_protos_user_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceInfo.ProtoReflect.Descriptor instead.
func (*DeviceInfo) Descriptor() ([]byte, []int) {
	return file_protos_user_proto_rawDescGZIP(), []int{17}
}

func (x *DeviceInfo) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *DeviceInfo) GetDeviceName() string {
	if x != nil {
		return x.DeviceName
	}
	return ""
}

func (x *DeviceInfo) GetDeviceType() string {
	if x != nil {
		return x.DeviceType
	}
	return ""
}

func (x *DeviceInfo) GetOsName() string {
	if x != nil {
		return x.OsName
	}
	return ""
}

func (x *DeviceInfo) GetOsVersion() string {
	if x != nil {
		return x.OsVersion
	}
	return ""
}

func (x *DeviceInfo) GetAppVersion() string {
	if x != nil {
		return x.AppVersion
	}
	return ""
}

type CreateUserRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Email                string      `protobuf:"bytes,1,opt,name=email,proto3" json:"email,omitempty"`                                                               // 邮箱
	Password             string      `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`                                                         // 密码，经过 sha1（passwd + salt（aibook）） 加密后
	Country              string      `protobuf:"bytes,3,opt,name=country,proto3" json:"country,omitempty"`                                                           // 国家代码，3位
	Language             string      `protobuf:"bytes,4,opt,name=language,proto3" json:"language,omitempty"`                                                         // 语言代码
	DeviceInfo           *DeviceInfo `protobuf:"bytes,5,opt,name=device_info,json=deviceInfo,proto3" json:"device_info,omitempty"`                                   // 设备信息
	EmailVerifyCode      string      `protobuf:"bytes,6,opt,name=email_verify_code,json=emailVerifyCode,proto3" json:"email_verify_code,omitempty"`                  //  邮箱验证码
	EmailVerifyCodeToken string      `protobuf:"bytes,7,opt,name=email_verify_code_token,json=emailVerifyCodeToken,proto3" json:"email_verify_code_token,omitempty"` // 验证码 token
}

func (x *CreateUserRequest) Reset() {
	*x = CreateUserRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_user_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateUserRequest) ProtoMessage() {}

func (x *CreateUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_protos_user_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateUserRequest.ProtoReflect.Descriptor instead.
func (*CreateUserRequest) Descriptor() ([]byte, []int) {
	return file_protos_user_proto_rawDescGZIP(), []int{18}
}

func (x *CreateUserRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *CreateUserRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *CreateUserRequest) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *CreateUserRequest) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *CreateUserRequest) GetDeviceInfo() *DeviceInfo {
	if x != nil {
		return x.DeviceInfo
	}
	return nil
}

func (x *CreateUserRequest) GetEmailVerifyCode() string {
	if x != nil {
		return x.EmailVerifyCode
	}
	return ""
}

func (x *CreateUserRequest) GetEmailVerifyCodeToken() string {
	if x != nil {
		return x.EmailVerifyCodeToken
	}
	return ""
}

type LoginRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Email    string `protobuf:"bytes,1,opt,name=email,proto3" json:"email,omitempty"`       // 用户名
	Password string `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"` // 密码
}

func (x *LoginRequest) Reset() {
	*x = LoginRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_user_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoginRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginRequest) ProtoMessage() {}

func (x *LoginRequest) ProtoReflect() protoreflect.Message {
	mi := &file_protos_user_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginRequest.ProtoReflect.Descriptor instead.
func (*LoginRequest) Descriptor() ([]byte, []int) {
	return file_protos_user_proto_rawDescGZIP(), []int{19}
}

func (x *LoginRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *LoginRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

type LoginResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Seq      uint32 `protobuf:"varint,1,opt,name=seq,proto3" json:"seq,omitempty"`                           // 用户序列号
	Token    string `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`                        // 登录令牌, 2个月过期
	ExpireAt uint32 `protobuf:"varint,3,opt,name=expire_at,json=expireAt,proto3" json:"expire_at,omitempty"` // 过期 unix 秒级时间戳
	UserId   uint64 `protobuf:"varint,4,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`       // 用户ID, 仅研发联调时返回； 生产不返回
}

func (x *LoginResponse) Reset() {
	*x = LoginResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_user_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoginResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginResponse) ProtoMessage() {}

func (x *LoginResponse) ProtoReflect() protoreflect.Message {
	mi := &file_protos_user_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginResponse.ProtoReflect.Descriptor instead.
func (*LoginResponse) Descriptor() ([]byte, []int) {
	return file_protos_user_proto_rawDescGZIP(), []int{20}
}

func (x *LoginResponse) GetSeq() uint32 {
	if x != nil {
		return x.Seq
	}
	return 0
}

func (x *LoginResponse) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *LoginResponse) GetExpireAt() uint32 {
	if x != nil {
		return x.ExpireAt
	}
	return 0
}

func (x *LoginResponse) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type LoginWithCodeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Email                string `protobuf:"bytes,1,opt,name=email,proto3" json:"email,omitempty"`
	EmailVerifyCode      string `protobuf:"bytes,2,opt,name=email_verify_code,json=emailVerifyCode,proto3" json:"email_verify_code,omitempty"`
	EmailVerifyCodeToken string `protobuf:"bytes,3,opt,name=email_verify_code_token,json=emailVerifyCodeToken,proto3" json:"email_verify_code_token,omitempty"`
}

func (x *LoginWithCodeRequest) Reset() {
	*x = LoginWithCodeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_user_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoginWithCodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginWithCodeRequest) ProtoMessage() {}

func (x *LoginWithCodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_protos_user_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginWithCodeRequest.ProtoReflect.Descriptor instead.
func (*LoginWithCodeRequest) Descriptor() ([]byte, []int) {
	return file_protos_user_proto_rawDescGZIP(), []int{21}
}

func (x *LoginWithCodeRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *LoginWithCodeRequest) GetEmailVerifyCode() string {
	if x != nil {
		return x.EmailVerifyCode
	}
	return ""
}

func (x *LoginWithCodeRequest) GetEmailVerifyCodeToken() string {
	if x != nil {
		return x.EmailVerifyCodeToken
	}
	return ""
}

type UpdatePasswordRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OldPassword string `protobuf:"bytes,1,opt,name=old_password,json=oldPassword,proto3" json:"old_password,omitempty"`
	NewPassword string `protobuf:"bytes,2,opt,name=new_password,json=newPassword,proto3" json:"new_password,omitempty"`
}

func (x *UpdatePasswordRequest) Reset() {
	*x = UpdatePasswordRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_user_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePasswordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePasswordRequest) ProtoMessage() {}

func (x *UpdatePasswordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_protos_user_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePasswordRequest.ProtoReflect.Descriptor instead.
func (*UpdatePasswordRequest) Descriptor() ([]byte, []int) {
	return file_protos_user_proto_rawDescGZIP(), []int{22}
}

func (x *UpdatePasswordRequest) GetOldPassword() string {
	if x != nil {
		return x.OldPassword
	}
	return ""
}

func (x *UpdatePasswordRequest) GetNewPassword() string {
	if x != nil {
		return x.NewPassword
	}
	return ""
}

type ResetPasswordRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Email                string `protobuf:"bytes,1,opt,name=email,proto3" json:"email,omitempty"`
	EmailVerifyCode      string `protobuf:"bytes,2,opt,name=email_verify_code,json=emailVerifyCode,proto3" json:"email_verify_code,omitempty"`
	EmailVerifyCodeToken string `protobuf:"bytes,3,opt,name=email_verify_code_token,json=emailVerifyCodeToken,proto3" json:"email_verify_code_token,omitempty"`
	NewPassword          string `protobuf:"bytes,4,opt,name=new_password,json=newPassword,proto3" json:"new_password,omitempty"`
}

func (x *ResetPasswordRequest) Reset() {
	*x = ResetPasswordRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_user_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResetPasswordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResetPasswordRequest) ProtoMessage() {}

func (x *ResetPasswordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_protos_user_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResetPasswordRequest.ProtoReflect.Descriptor instead.
func (*ResetPasswordRequest) Descriptor() ([]byte, []int) {
	return file_protos_user_proto_rawDescGZIP(), []int{23}
}

func (x *ResetPasswordRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *ResetPasswordRequest) GetEmailVerifyCode() string {
	if x != nil {
		return x.EmailVerifyCode
	}
	return ""
}

func (x *ResetPasswordRequest) GetEmailVerifyCodeToken() string {
	if x != nil {
		return x.EmailVerifyCodeToken
	}
	return ""
}

func (x *ResetPasswordRequest) GetNewPassword() string {
	if x != nil {
		return x.NewPassword
	}
	return ""
}

type GetProfileRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetProfileRequest) Reset() {
	*x = GetProfileRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_user_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetProfileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProfileRequest) ProtoMessage() {}

func (x *GetProfileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_protos_user_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProfileRequest.ProtoReflect.Descriptor instead.
func (*GetProfileRequest) Descriptor() ([]byte, []int) {
	return file_protos_user_proto_rawDescGZIP(), []int{24}
}

type UserProfile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Nickname    string  `protobuf:"bytes,1,opt,name=nickname,proto3" json:"nickname,omitempty"`                             // 昵称
	Email       string  `protobuf:"bytes,2,opt,name=email,proto3" json:"email,omitempty"`                                   // 邮箱
	Status      int32   `protobuf:"varint,3,opt,name=status,proto3" json:"status,omitempty"`                                // 状态
	Country     string  `protobuf:"bytes,4,opt,name=country,proto3" json:"country,omitempty"`                               // 国家或地区, 不可修改
	Lang        string  `protobuf:"bytes,5,opt,name=lang,proto3" json:"lang,omitempty"`                                     // 语言
	Birthday    uint32  `protobuf:"varint,6,opt,name=birthday,proto3" json:"birthday,omitempty"`                            // 生日
	Points      float32 `protobuf:"fixed32,7,opt,name=points,proto3" json:"points,omitempty"`                               // 用户积分余额
	DeviceId    string  `protobuf:"bytes,8,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`             // 设备ID
	LastLoginAt uint32  `protobuf:"varint,9,opt,name=last_login_at,json=lastLoginAt,proto3" json:"last_login_at,omitempty"` // 最后登录时间
	CreatedAt   uint32  `protobuf:"varint,10,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`        // 创建时间
	UpdatedAt   uint32  `protobuf:"varint,11,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`        // 更新时间
}

func (x *UserProfile) Reset() {
	*x = UserProfile{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_user_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserProfile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserProfile) ProtoMessage() {}

func (x *UserProfile) ProtoReflect() protoreflect.Message {
	mi := &file_protos_user_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserProfile.ProtoReflect.Descriptor instead.
func (*UserProfile) Descriptor() ([]byte, []int) {
	return file_protos_user_proto_rawDescGZIP(), []int{25}
}

func (x *UserProfile) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *UserProfile) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *UserProfile) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *UserProfile) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *UserProfile) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

func (x *UserProfile) GetBirthday() uint32 {
	if x != nil {
		return x.Birthday
	}
	return 0
}

func (x *UserProfile) GetPoints() float32 {
	if x != nil {
		return x.Points
	}
	return 0
}

func (x *UserProfile) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *UserProfile) GetLastLoginAt() uint32 {
	if x != nil {
		return x.LastLoginAt
	}
	return 0
}

func (x *UserProfile) GetCreatedAt() uint32 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *UserProfile) GetUpdatedAt() uint32 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

type DetailProfile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId  uint64       `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Profile *UserProfile `protobuf:"bytes,2,opt,name=profile,proto3" json:"profile,omitempty"`
}

func (x *DetailProfile) Reset() {
	*x = DetailProfile{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_user_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DetailProfile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetailProfile) ProtoMessage() {}

func (x *DetailProfile) ProtoReflect() protoreflect.Message {
	mi := &file_protos_user_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetailProfile.ProtoReflect.Descriptor instead.
func (*DetailProfile) Descriptor() ([]byte, []int) {
	return file_protos_user_proto_rawDescGZIP(), []int{26}
}

func (x *DetailProfile) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *DetailProfile) GetProfile() *UserProfile {
	if x != nil {
		return x.Profile
	}
	return nil
}

type UpdateProfileRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Nickname string `protobuf:"bytes,1,opt,name=nickname,proto3" json:"nickname,omitempty"`  //  更新用户信息请求，字段为空或0， 则代表不更改。
	Lang     string `protobuf:"bytes,2,opt,name=lang,proto3" json:"lang,omitempty"`          // 语言代码
	Birthday uint32 `protobuf:"varint,3,opt,name=birthday,proto3" json:"birthday,omitempty"` // 生日， unix 秒级时间戳
}

func (x *UpdateProfileRequest) Reset() {
	*x = UpdateProfileRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_user_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateProfileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateProfileRequest) ProtoMessage() {}

func (x *UpdateProfileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_protos_user_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateProfileRequest.ProtoReflect.Descriptor instead.
func (*UpdateProfileRequest) Descriptor() ([]byte, []int) {
	return file_protos_user_proto_rawDescGZIP(), []int{27}
}

func (x *UpdateProfileRequest) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *UpdateProfileRequest) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

func (x *UpdateProfileRequest) GetBirthday() uint32 {
	if x != nil {
		return x.Birthday
	}
	return 0
}

type GetEmailVerifyCodeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Email string `protobuf:"bytes,1,opt,name=email,proto3" json:"email,omitempty"` // 邮箱
}

func (x *GetEmailVerifyCodeRequest) Reset() {
	*x = GetEmailVerifyCodeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_user_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEmailVerifyCodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEmailVerifyCodeRequest) ProtoMessage() {}

func (x *GetEmailVerifyCodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_protos_user_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEmailVerifyCodeRequest.ProtoReflect.Descriptor instead.
func (*GetEmailVerifyCodeRequest) Descriptor() ([]byte, []int) {
	return file_protos_user_proto_rawDescGZIP(), []int{28}
}

func (x *GetEmailVerifyCodeRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

type GetEmailVerifyCodeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EmailVerifyCodeToken string `protobuf:"bytes,1,opt,name=email_verify_code_token,json=emailVerifyCodeToken,proto3" json:"email_verify_code_token,omitempty"` // 验证码令牌
	EmailVerifyCode      string `protobuf:"bytes,2,opt,name=email_verify_code,json=emailVerifyCode,proto3" json:"email_verify_code,omitempty"`                  // 验证码, 仅研发环境返回，生产环境不返回此值。
}

func (x *GetEmailVerifyCodeResponse) Reset() {
	*x = GetEmailVerifyCodeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_user_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEmailVerifyCodeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEmailVerifyCodeResponse) ProtoMessage() {}

func (x *GetEmailVerifyCodeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_protos_user_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEmailVerifyCodeResponse.ProtoReflect.Descriptor instead.
func (*GetEmailVerifyCodeResponse) Descriptor() ([]byte, []int) {
	return file_protos_user_proto_rawDescGZIP(), []int{29}
}

func (x *GetEmailVerifyCodeResponse) GetEmailVerifyCodeToken() string {
	if x != nil {
		return x.EmailVerifyCodeToken
	}
	return ""
}

func (x *GetEmailVerifyCodeResponse) GetEmailVerifyCode() string {
	if x != nil {
		return x.EmailVerifyCode
	}
	return ""
}

var File_protos_user_proto protoreflect.FileDescriptor

var file_protos_user_proto_rawDesc = []byte{
	0x0a, 0x11, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x14, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e,
	0x75, 0x73, 0x65, 0x72, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x5b, 0x0a, 0x0d, 0x43, 0x68, 0x61, 0x72, 0x67,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x06, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x42, 0x0f, 0xfa, 0x42, 0x0c, 0x0a, 0x0a, 0x1d,
	0x00, 0x24, 0x74, 0x49, 0x2d, 0x00, 0x00, 0x80, 0x3f, 0x52, 0x06, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x73, 0x12, 0x21, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x58, 0x52, 0x06, 0x72, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x22, 0x57, 0x0a, 0x0b, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x1a, 0x06, 0x18, 0xa0, 0x8d, 0x06, 0x28, 0x01, 0x52, 0x04,
	0x70, 0x61, 0x67, 0x65, 0x12, 0x27, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x1a, 0x05, 0x18, 0x90,
	0x4e, 0x28, 0x01, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0x55, 0x0a,
	0x0c, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x22, 0x8e, 0x01, 0x0a, 0x15, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3d,
	0x0a, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x23, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x75, 0x73, 0x65,
	0x72, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x52, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x36, 0x0a,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x6f,
	0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x67, 0x72,
	0x70, 0x63, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x89, 0x02, 0x0a, 0x0d, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69,
	0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x62, 0x65, 0x6e, 0x65, 0x66,
	0x69, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x62,
	0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x62, 0x65,
	0x6e, 0x65, 0x66, 0x69, 0x74, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0c, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x1f, 0x0a, 0x0b, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x5f, 0x75, 0x73, 0x65, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x0a, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x55, 0x73, 0x65, 0x64,
	0x12, 0x29, 0x0a, 0x10, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x5f, 0x72, 0x65, 0x63, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0f, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x73, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x72,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x22, 0xff, 0x01, 0x0a, 0x0d, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x1a, 0x06, 0x18, 0xa0, 0x8d, 0x06, 0x28, 0x01, 0x52, 0x04,
	0x70, 0x61, 0x67, 0x65, 0x12, 0x27, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x1a, 0x05, 0x18, 0x90,
	0x4e, 0x28, 0x01, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x2a, 0x0a,
	0x10, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x42, 0x79, 0x4e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x42,
	0x79, 0x4e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x73, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x42, 0x79, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x42, 0x79, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12,
	0x2a, 0x0a, 0x10, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x42, 0x79, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x42, 0x79, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0e, 0x73,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x0e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x42, 0x79, 0x55, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x22, 0x9b, 0x01, 0x0a, 0x0e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x39, 0x0a, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f,
	0x6f, 0x6b, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x05, 0x75, 0x73, 0x65, 0x72,
	0x73, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69,
	0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69,
	0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x73, 0x69, 0x7a, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x53, 0x69, 0x7a,
	0x65, 0x22, 0x07, 0x0a, 0x05, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x3b, 0x0a, 0x17, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x50, 0x6c, 0x61, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x2a, 0x02, 0x28, 0x01, 0x52,
	0x06, 0x70, 0x6c, 0x61, 0x6e, 0x49, 0x64, 0x22, 0x3b, 0x0a, 0x20, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x50, 0x6f,
	0x69, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x2a, 0x02, 0x28, 0x01,
	0x52, 0x02, 0x69, 0x64, 0x22, 0x45, 0x0a, 0x0b, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x50, 0x6c,
	0x61, 0x6e, 0x73, 0x12, 0x36, 0x0a, 0x05, 0x70, 0x6c, 0x61, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e,
	0x75, 0x73, 0x65, 0x72, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d,
	0x50, 0x6c, 0x61, 0x6e, 0x52, 0x05, 0x70, 0x6c, 0x61, 0x6e, 0x73, 0x22, 0xb1, 0x02, 0x0a, 0x13,
	0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x50, 0x6f, 0x69,
	0x6e, 0x74, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x2d, 0x0a, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x13, 0xfa, 0x42, 0x10, 0x72, 0x0e, 0x10, 0x02, 0x18, 0x03, 0x32,
	0x08, 0x5e, 0x5b, 0x41, 0x2d, 0x5a, 0x5d, 0x2b, 0x24, 0x52, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x33, 0x0a, 0x0f, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x31, 0x5f, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x42, 0x0a, 0xfa, 0x42, 0x07,
	0x0a, 0x05, 0x2d, 0xcd, 0xcc, 0xcc, 0x3d, 0x52, 0x0e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x31, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x33, 0x0a, 0x0f, 0x66, 0x65, 0x61, 0x74, 0x75,
	0x72, 0x65, 0x32, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02,
	0x42, 0x0a, 0xfa, 0x42, 0x07, 0x0a, 0x05, 0x2d, 0xcd, 0xcc, 0xcc, 0x3d, 0x52, 0x0e, 0x66, 0x65,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x32, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x33, 0x0a, 0x0f,
	0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x33, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x02, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x0a, 0x05, 0x2d, 0x00, 0x00, 0x00,
	0x00, 0x52, 0x0e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x33, 0x50, 0x6f, 0x69, 0x6e, 0x74,
	0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22,
	0x65, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x46, 0x65, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x43, 0x0a, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x29, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e,
	0x75, 0x73, 0x65, 0x72, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d,
	0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x52, 0x07, 0x72,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x22, 0xa7, 0x03, 0x0a, 0x0a, 0x53, 0x79, 0x73, 0x74, 0x65,
	0x6d, 0x50, 0x6c, 0x61, 0x6e, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x70, 0x6c, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c,
	0x61, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70,
	0x6c, 0x61, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2d, 0x0a, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x72, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x13, 0xfa, 0x42, 0x10, 0x72, 0x0e, 0x10,
	0x02, 0x18, 0x03, 0x32, 0x08, 0x5e, 0x5b, 0x41, 0x2d, 0x5a, 0x5d, 0x2b, 0x24, 0x52, 0x07, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x63, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x63, 0x79, 0x12, 0x2e, 0x0a, 0x0e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x31, 0x5f, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a,
	0x02, 0x28, 0x01, 0x52, 0x0d, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x31, 0x54, 0x6f, 0x74,
	0x61, 0x6c, 0x12, 0x2e, 0x0a, 0x0e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x32, 0x5f, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a,
	0x02, 0x28, 0x01, 0x52, 0x0d, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x32, 0x54, 0x6f, 0x74,
	0x61, 0x6c, 0x12, 0x2e, 0x0a, 0x0e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x33, 0x5f, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a,
	0x02, 0x28, 0x00, 0x52, 0x0d, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x33, 0x54, 0x6f, 0x74,
	0x61, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x22, 0xf2, 0x02, 0x0a, 0x08, 0x55, 0x73, 0x65, 0x72, 0x50, 0x6c, 0x61, 0x6e, 0x12, 0x17, 0x0a,
	0x07, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06,
	0x70, 0x6c, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x65, 0x78, 0x70, 0x69,
	0x72, 0x65, 0x64, 0x41, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x31, 0x5f, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e,
	0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x31, 0x52, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x27,
	0x0a, 0x0f, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x32, 0x5f, 0x72, 0x65, 0x6d, 0x61, 0x69,
	0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x32, 0x52, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x27, 0x0a, 0x0f, 0x66, 0x65, 0x61, 0x74, 0x75,
	0x72, 0x65, 0x33, 0x5f, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x33, 0x52, 0x65, 0x6d, 0x61, 0x69, 0x6e,
	0x12, 0x25, 0x0a, 0x0e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x31, 0x5f, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72,
	0x65, 0x31, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x25, 0x0a, 0x0e, 0x66, 0x65, 0x61, 0x74, 0x75,
	0x72, 0x65, 0x32, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0d, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x32, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x25,
	0x0a, 0x0e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x33, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x33,
	0x54, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0xe4, 0x01, 0x0a, 0x14, 0x52, 0x65, 0x64, 0x75, 0x63, 0x65,
	0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2c,
	0x0a, 0x0c, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x1a, 0x04, 0x18, 0x03, 0x28, 0x00, 0x52,
	0x0b, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3c, 0x0a, 0x14,
	0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x5f, 0x72, 0x65, 0x64, 0x75, 0x63, 0x65, 0x5f, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x1a,
	0x05, 0x18, 0x90, 0x4e, 0x28, 0x00, 0x52, 0x12, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x52,
	0x65, 0x64, 0x75, 0x63, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3d, 0x0a, 0x12, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x64, 0x75, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x42, 0x0f, 0xfa, 0x42, 0x0c, 0x0a, 0x0a, 0x1d, 0x00, 0x40,
	0x1c, 0x46, 0x2d, 0x00, 0x00, 0x00, 0x00, 0x52, 0x10, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x65,
	0x64, 0x75, 0x63, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x21, 0x0a, 0x06, 0x72, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04,
	0x10, 0x01, 0x18, 0x58, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0x29, 0x0a, 0x0c,
	0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x08,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x22, 0xc4, 0x01, 0x0a, 0x0a, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x6f, 0x73, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x6f, 0x73, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a,
	0x0b, 0x61, 0x70, 0x70, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x61, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xd6,
	0x02, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x60, 0x01, 0x52, 0x05, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x12, 0x26, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x06, 0x18, 0xbc,
	0x01, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x2d, 0x0a, 0x07, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x13, 0xfa, 0x42,
	0x10, 0x72, 0x0e, 0x10, 0x02, 0x18, 0x03, 0x32, 0x08, 0x5e, 0x5b, 0x41, 0x2d, 0x5a, 0x5d, 0x2b,
	0x24, 0x52, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x25, 0x0a, 0x08, 0x6c, 0x61,
	0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42,
	0x06, 0x72, 0x04, 0x10, 0x02, 0x18, 0x58, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67,
	0x65, 0x12, 0x41, 0x0a, 0x0b, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62,
	0x6f, 0x6f, 0x6b, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x44, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2a, 0x0a, 0x11, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x76, 0x65,
	0x72, 0x69, 0x66, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x35, 0x0a, 0x17, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x76, 0x65, 0x72, 0x69, 0x66, 0x79,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x14, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x43, 0x6f,
	0x64, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x49, 0x0a, 0x0c, 0x4c, 0x6f, 0x67, 0x69, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x60, 0x01, 0x52,
	0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f,
	0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f,
	0x72, 0x64, 0x22, 0x6d, 0x0a, 0x0d, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x03, 0x73, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x65,
	0x78, 0x70, 0x69, 0x72, 0x65, 0x5f, 0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08,
	0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x41, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x22, 0x98, 0x01, 0x0a, 0x14, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x57, 0x69, 0x74, 0x68, 0x43,
	0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x05, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02,
	0x60, 0x01, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x2a, 0x0a, 0x11, 0x65, 0x6d, 0x61,
	0x69, 0x6c, 0x5f, 0x76, 0x65, 0x72, 0x69, 0x66, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x56, 0x65, 0x72, 0x69, 0x66,
	0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x35, 0x0a, 0x17, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x76,
	0x65, 0x72, 0x69, 0x66, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x56, 0x65, 0x72,
	0x69, 0x66, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x5d, 0x0a, 0x15,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x6f, 0x6c, 0x64, 0x5f, 0x70, 0x61, 0x73,
	0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6f, 0x6c, 0x64,
	0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x6e, 0x65, 0x77, 0x5f,
	0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x6e, 0x65, 0x77, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x22, 0xbb, 0x01, 0x0a, 0x14,
	0x52, 0x65, 0x73, 0x65, 0x74, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x60, 0x01, 0x52, 0x05, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x12, 0x2a, 0x0a, 0x11, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x76, 0x65, 0x72,
	0x69, 0x66, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x65, 0x6d, 0x61, 0x69, 0x6c, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x35, 0x0a, 0x17, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x76, 0x65, 0x72, 0x69, 0x66, 0x79, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x14, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x43, 0x6f, 0x64,
	0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x6e, 0x65, 0x77, 0x5f, 0x70, 0x61,
	0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6e, 0x65,
	0x77, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x22, 0x13, 0x0a, 0x11, 0x47, 0x65, 0x74,
	0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0xb8,
	0x02, 0x0a, 0x0b, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x72, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x69, 0x72, 0x74, 0x68, 0x64,
	0x61, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x62, 0x69, 0x72, 0x74, 0x68, 0x64,
	0x61, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x06, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0d, 0x6c, 0x61, 0x73, 0x74, 0x5f,
	0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x5f, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b,
	0x6c, 0x61, 0x73, 0x74, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0x65, 0x0a, 0x0d, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x3b, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f,
	0x6b, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x55, 0x73, 0x65, 0x72,
	0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x07, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x22, 0x62, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x69, 0x72, 0x74,
	0x68, 0x64, 0x61, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x62, 0x69, 0x72, 0x74,
	0x68, 0x64, 0x61, 0x79, 0x22, 0x3a, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x45, 0x6d, 0x61, 0x69, 0x6c,
	0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1d, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x60, 0x01, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x22, 0x7f, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x56, 0x65, 0x72, 0x69,
	0x66, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x35,
	0x0a, 0x17, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x76, 0x65, 0x72, 0x69, 0x66, 0x79, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x14, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x43, 0x6f, 0x64, 0x65,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x2a, 0x0a, 0x11, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x76,
	0x65, 0x72, 0x69, 0x66, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x43, 0x6f, 0x64,
	0x65, 0x32, 0xf6, 0x14, 0x0a, 0x0b, 0x55, 0x73, 0x65, 0x72, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x9f, 0x01, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x56, 0x65,
	0x72, 0x69, 0x66, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x2f, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x61,
	0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e,
	0x47, 0x65, 0x74, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x43, 0x6f,
	0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x63, 0x6f, 0x6d, 0x2e,
	0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x67, 0x72, 0x70, 0x63,
	0x2e, 0x47, 0x65, 0x74, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x43,
	0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x26, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x20, 0x12, 0x1e, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65,
	0x72, 0x2f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x76, 0x65, 0x72, 0x69, 0x66, 0x79, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x12, 0x7a, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65,
	0x72, 0x12, 0x27, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55,
	0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x63, 0x6f, 0x6d,
	0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x67, 0x72, 0x70,
	0x63, 0x2e, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x1e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x18, 0x3a, 0x01, 0x2a, 0x22, 0x13, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x12,
	0x6f, 0x0a, 0x05, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x12, 0x22, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x61,
	0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e,
	0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x63,
	0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x67,
	0x72, 0x70, 0x63, 0x2e, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x1d, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x17, 0x3a, 0x01, 0x2a, 0x22, 0x12, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x6c, 0x6f, 0x67, 0x69, 0x6e,
	0x12, 0x89, 0x01, 0x0a, 0x0d, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x57, 0x69, 0x74, 0x68, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x2a, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e,
	0x75, 0x73, 0x65, 0x72, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x57,
	0x69, 0x74, 0x68, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23,
	0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x75, 0x73, 0x65, 0x72,
	0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x27, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x21, 0x3a, 0x01, 0x2a, 0x22, 0x1c,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x6c, 0x6f, 0x67,
	0x69, 0x6e, 0x5f, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x77, 0x0a, 0x0c,
	0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1b, 0x2e, 0x63,
	0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x67,
	0x72, 0x70, 0x63, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x23, 0x2e, 0x63, 0x6f, 0x6d, 0x2e,
	0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x67, 0x72, 0x70, 0x63,
	0x2e, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x25,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1f, 0x3a, 0x01, 0x2a, 0x22, 0x1a, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x5f,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x76, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x66,
	0x69, 0x6c, 0x65, 0x12, 0x27, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x63,
	0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x67,
	0x72, 0x70, 0x63, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x22,
	0x1c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x16, 0x12, 0x14, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31,
	0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x7f, 0x0a,
	0x0d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x2a,
	0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x75, 0x73, 0x65, 0x72,
	0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x66,
	0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x63, 0x6f, 0x6d,
	0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x67, 0x72, 0x70,
	0x63, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x22, 0x1f, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x19, 0x3a, 0x01, 0x2a, 0x22, 0x14, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x80,
	0x01, 0x0a, 0x0d, 0x52, 0x65, 0x73, 0x65, 0x74, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64,
	0x12, 0x2a, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x52, 0x65, 0x73, 0x65, 0x74, 0x50, 0x61, 0x73,
	0x73, 0x77, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x63,
	0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x67,
	0x72, 0x70, 0x63, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x26, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x20, 0x3a, 0x01, 0x2a, 0x22, 0x1b, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73,
	0x65, 0x72, 0x2f, 0x72, 0x65, 0x73, 0x65, 0x74, 0x5f, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72,
	0x64, 0x12, 0x73, 0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65,
	0x6d, 0x50, 0x6c, 0x61, 0x6e, 0x12, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f,
	0x6f, 0x6b, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x79, 0x73,
	0x74, 0x65, 0x6d, 0x50, 0x6c, 0x61, 0x6e, 0x1a, 0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x69,
	0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x22, 0x20, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1a, 0x3a, 0x01, 0x2a, 0x1a,
	0x15, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x73, 0x79,
	0x73, 0x2f, 0x70, 0x6c, 0x61, 0x6e, 0x12, 0x70, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x53, 0x79, 0x73,
	0x74, 0x65, 0x6d, 0x50, 0x6c, 0x61, 0x6e, 0x73, 0x12, 0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x61,
	0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x21, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f,
	0x6f, 0x6b, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x79, 0x73,
	0x74, 0x65, 0x6d, 0x50, 0x6c, 0x61, 0x6e, 0x73, 0x22, 0x1e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x18,
	0x12, 0x16, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x73,
	0x79, 0x73, 0x2f, 0x70, 0x6c, 0x61, 0x6e, 0x73, 0x12, 0x7d, 0x0a, 0x10, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x50, 0x6c, 0x61, 0x6e, 0x12, 0x2d, 0x2e, 0x63,
	0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x67,
	0x72, 0x70, 0x63, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d,
	0x50, 0x6c, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x63, 0x6f,
	0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x67, 0x72,
	0x70, 0x63, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x1d, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x17,
	0x2a, 0x15, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x73,
	0x79, 0x73, 0x2f, 0x70, 0x6c, 0x61, 0x6e, 0x12, 0x8f, 0x01, 0x0a, 0x19, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x50,
	0x6f, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x29, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f,
	0x6f, 0x6b, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x79, 0x73,
	0x74, 0x65, 0x6d, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73,
	0x1a, 0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x2a, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x24, 0x3a, 0x01, 0x2a, 0x1a, 0x1f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x73, 0x79, 0x73, 0x2f, 0x66, 0x65, 0x61, 0x74, 0x75,
	0x72, 0x65, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x94, 0x01, 0x0a, 0x16, 0x47, 0x65,
	0x74, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x50, 0x6f,
	0x69, 0x6e, 0x74, 0x73, 0x12, 0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f,
	0x6b, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x1a, 0x34, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x79, 0x73, 0x74,
	0x65, 0x6d, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x27, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x21, 0x12,
	0x1f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x73, 0x79,
	0x73, 0x2f, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73,
	0x12, 0x99, 0x01, 0x0a, 0x19, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65,
	0x6d, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x36,
	0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x75, 0x73, 0x65, 0x72,
	0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x79, 0x73, 0x74,
	0x65, 0x6d, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62,
	0x6f, 0x6f, 0x6b, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x22, 0x27, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x21, 0x2a, 0x1f, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x73, 0x79, 0x73, 0x2f, 0x66, 0x65,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x61, 0x0a, 0x07,
	0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x6e, 0x12, 0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x69,
	0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x1a, 0x1e, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f,
	0x6b, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x55, 0x73, 0x65, 0x72,
	0x50, 0x6c, 0x61, 0x6e, 0x22, 0x19, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x13, 0x12, 0x11, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x70, 0x6c, 0x61, 0x6e, 0x12,
	0x81, 0x01, 0x0a, 0x0a, 0x52, 0x65, 0x64, 0x75, 0x63, 0x65, 0x50, 0x6c, 0x61, 0x6e, 0x12, 0x2a,
	0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x75, 0x73, 0x65, 0x72,
	0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x52, 0x65, 0x64, 0x75, 0x63, 0x65, 0x42, 0x65, 0x6e, 0x65,
	0x66, 0x69, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x63, 0x6f, 0x6d,
	0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x67, 0x72, 0x70,
	0x63, 0x2e, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x22, 0x23,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1d, 0x3a, 0x01, 0x2a, 0x22, 0x18, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x70, 0x6c, 0x61, 0x6e, 0x2f, 0x72, 0x65, 0x64,
	0x75, 0x63, 0x65, 0x12, 0x82, 0x01, 0x0a, 0x18, 0x52, 0x6f, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b,
	0x48, 0x61, 0x6c, 0x66, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74,
	0x12, 0x22, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x1a, 0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f,
	0x6b, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x22, 0x25, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1f, 0x3a, 0x01, 0x2a, 0x22, 0x1a, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x70, 0x6c, 0x61, 0x6e, 0x2f,
	0x72, 0x6f, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x81, 0x01, 0x0a, 0x0d, 0x42, 0x65, 0x6e,
	0x65, 0x66, 0x69, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x21, 0x2e, 0x63, 0x6f, 0x6d,
	0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x67, 0x72, 0x70,
	0x63, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e,
	0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e,
	0x67, 0x72, 0x70, 0x63, 0x2e, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x20, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x1a, 0x12, 0x18, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72,
	0x2f, 0x70, 0x6c, 0x61, 0x6e, 0x2f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x70, 0x0a, 0x0c,
	0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x23, 0x2e, 0x63,
	0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x67,
	0x72, 0x70, 0x63, 0x2e, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x1e,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x18, 0x3a, 0x01, 0x2a, 0x22, 0x13, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x12, 0x58,
	0x0a, 0x06, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x61,
	0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f,
	0x6f, 0x6b, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x22, 0x14, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0e, 0x2a, 0x0c, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x12, 0x70, 0x0a, 0x06, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x12, 0x23, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x69, 0x62, 0x6f, 0x6f, 0x6b, 0x2e,
	0x75, 0x73, 0x65, 0x72, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x61, 0x69,
	0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x1b, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x15, 0x12, 0x13, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x75,
	0x73, 0x65, 0x72, 0x2f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x42, 0x13, 0x50, 0x01, 0x5a, 0x0f,
	0x2e, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x70, 0x62, 0x3b, 0x75, 0x73, 0x65, 0x72, 0x70, 0x62, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_protos_user_proto_rawDescOnce sync.Once
	file_protos_user_proto_rawDescData = file_protos_user_proto_rawDesc
)

func file_protos_user_proto_rawDescGZIP() []byte {
	file_protos_user_proto_rawDescOnce.Do(func() {
		file_protos_user_proto_rawDescData = protoimpl.X.CompressGZIP(file_protos_user_proto_rawDescData)
	})
	return file_protos_user_proto_rawDescData
}

var file_protos_user_proto_msgTypes = make([]protoimpl.MessageInfo, 30)
var file_protos_user_proto_goTypes = []interface{}{
	(*ChargeRequest)(nil),                    // 0: com.aibook.user.grpc.ChargeRequest
	(*PageRequest)(nil),                      // 1: com.aibook.user.grpc.PageRequest
	(*PageResponse)(nil),                     // 2: com.aibook.user.grpc.PageResponse
	(*BenefitRecordResponse)(nil),            // 3: com.aibook.user.grpc.BenefitRecordResponse
	(*BenefitRecord)(nil),                    // 4: com.aibook.user.grpc.BenefitRecord
	(*SearchRequest)(nil),                    // 5: com.aibook.user.grpc.SearchRequest
	(*SearchResponse)(nil),                   // 6: com.aibook.user.grpc.SearchResponse
	(*Empty)(nil),                            // 7: com.aibook.user.grpc.Empty
	(*DeleteSystemPlanRequest)(nil),          // 8: com.aibook.user.grpc.DeleteSystemPlanRequest
	(*DeleteSystemFeaturePointsRequest)(nil), // 9: com.aibook.user.grpc.DeleteSystemFeaturePointsRequest
	(*SystemPlans)(nil),                      // 10: com.aibook.user.grpc.SystemPlans
	(*SystemFeaturePoints)(nil),              // 11: com.aibook.user.grpc.SystemFeaturePoints
	(*GetSystemFeaturePointsResponse)(nil),   // 12: com.aibook.user.grpc.GetSystemFeaturePointsResponse
	(*SystemPlan)(nil),                       // 13: com.aibook.user.grpc.SystemPlan
	(*UserPlan)(nil),                         // 14: com.aibook.user.grpc.UserPlan
	(*ReduceBenefitRequest)(nil),             // 15: com.aibook.user.grpc.ReduceBenefitRequest
	(*BenefitOrder)(nil),                     // 16: com.aibook.user.grpc.BenefitOrder
	(*DeviceInfo)(nil),                       // 17: com.aibook.user.grpc.DeviceInfo
	(*CreateUserRequest)(nil),                // 18: com.aibook.user.grpc.CreateUserRequest
	(*LoginRequest)(nil),                     // 19: com.aibook.user.grpc.LoginRequest
	(*LoginResponse)(nil),                    // 20: com.aibook.user.grpc.LoginResponse
	(*LoginWithCodeRequest)(nil),             // 21: com.aibook.user.grpc.LoginWithCodeRequest
	(*UpdatePasswordRequest)(nil),            // 22: com.aibook.user.grpc.UpdatePasswordRequest
	(*ResetPasswordRequest)(nil),             // 23: com.aibook.user.grpc.ResetPasswordRequest
	(*GetProfileRequest)(nil),                // 24: com.aibook.user.grpc.GetProfileRequest
	(*UserProfile)(nil),                      // 25: com.aibook.user.grpc.UserProfile
	(*DetailProfile)(nil),                    // 26: com.aibook.user.grpc.DetailProfile
	(*UpdateProfileRequest)(nil),             // 27: com.aibook.user.grpc.UpdateProfileRequest
	(*GetEmailVerifyCodeRequest)(nil),        // 28: com.aibook.user.grpc.GetEmailVerifyCodeRequest
	(*GetEmailVerifyCodeResponse)(nil),       // 29: com.aibook.user.grpc.GetEmailVerifyCodeResponse
}
var file_protos_user_proto_depIdxs = []int32{
	4,  // 0: com.aibook.user.grpc.BenefitRecordResponse.records:type_name -> com.aibook.user.grpc.BenefitRecord
	2,  // 1: com.aibook.user.grpc.BenefitRecordResponse.page:type_name -> com.aibook.user.grpc.PageResponse
	26, // 2: com.aibook.user.grpc.SearchResponse.users:type_name -> com.aibook.user.grpc.DetailProfile
	13, // 3: com.aibook.user.grpc.SystemPlans.plans:type_name -> com.aibook.user.grpc.SystemPlan
	11, // 4: com.aibook.user.grpc.GetSystemFeaturePointsResponse.records:type_name -> com.aibook.user.grpc.SystemFeaturePoints
	17, // 5: com.aibook.user.grpc.CreateUserRequest.device_info:type_name -> com.aibook.user.grpc.DeviceInfo
	25, // 6: com.aibook.user.grpc.DetailProfile.profile:type_name -> com.aibook.user.grpc.UserProfile
	28, // 7: com.aibook.user.grpc.UserService.GetEmailVerifyCode:input_type -> com.aibook.user.grpc.GetEmailVerifyCodeRequest
	18, // 8: com.aibook.user.grpc.UserService.CreateUser:input_type -> com.aibook.user.grpc.CreateUserRequest
	19, // 9: com.aibook.user.grpc.UserService.Login:input_type -> com.aibook.user.grpc.LoginRequest
	21, // 10: com.aibook.user.grpc.UserService.LoginWithCode:input_type -> com.aibook.user.grpc.LoginWithCodeRequest
	7,  // 11: com.aibook.user.grpc.UserService.RefreshToken:input_type -> com.aibook.user.grpc.Empty
	24, // 12: com.aibook.user.grpc.UserService.GetProfile:input_type -> com.aibook.user.grpc.GetProfileRequest
	27, // 13: com.aibook.user.grpc.UserService.UpdateProfile:input_type -> com.aibook.user.grpc.UpdateProfileRequest
	23, // 14: com.aibook.user.grpc.UserService.ResetPassword:input_type -> com.aibook.user.grpc.ResetPasswordRequest
	13, // 15: com.aibook.user.grpc.UserService.UpdateSystemPlan:input_type -> com.aibook.user.grpc.SystemPlan
	7,  // 16: com.aibook.user.grpc.UserService.GetSystemPlans:input_type -> com.aibook.user.grpc.Empty
	8,  // 17: com.aibook.user.grpc.UserService.DeleteSystemPlan:input_type -> com.aibook.user.grpc.DeleteSystemPlanRequest
	11, // 18: com.aibook.user.grpc.UserService.UpdateSystemFeaturePoints:input_type -> com.aibook.user.grpc.SystemFeaturePoints
	7,  // 19: com.aibook.user.grpc.UserService.GetSystemFeaturePoints:input_type -> com.aibook.user.grpc.Empty
	9,  // 20: com.aibook.user.grpc.UserService.DeleteSystemFeaturePoints:input_type -> com.aibook.user.grpc.DeleteSystemFeaturePointsRequest
	7,  // 21: com.aibook.user.grpc.UserService.GetPlan:input_type -> com.aibook.user.grpc.Empty
	15, // 22: com.aibook.user.grpc.UserService.ReducePlan:input_type -> com.aibook.user.grpc.ReduceBenefitRequest
	16, // 23: com.aibook.user.grpc.UserService.RollbackHalfPointBenefit:input_type -> com.aibook.user.grpc.BenefitOrder
	1,  // 24: com.aibook.user.grpc.UserService.BenefitRecord:input_type -> com.aibook.user.grpc.PageRequest
	0,  // 25: com.aibook.user.grpc.UserService.ChargePoints:input_type -> com.aibook.user.grpc.ChargeRequest
	7,  // 26: com.aibook.user.grpc.UserService.Delete:input_type -> com.aibook.user.grpc.Empty
	5,  // 27: com.aibook.user.grpc.UserService.Search:input_type -> com.aibook.user.grpc.SearchRequest
	29, // 28: com.aibook.user.grpc.UserService.GetEmailVerifyCode:output_type -> com.aibook.user.grpc.GetEmailVerifyCodeResponse
	20, // 29: com.aibook.user.grpc.UserService.CreateUser:output_type -> com.aibook.user.grpc.LoginResponse
	20, // 30: com.aibook.user.grpc.UserService.Login:output_type -> com.aibook.user.grpc.LoginResponse
	20, // 31: com.aibook.user.grpc.UserService.LoginWithCode:output_type -> com.aibook.user.grpc.LoginResponse
	20, // 32: com.aibook.user.grpc.UserService.RefreshToken:output_type -> com.aibook.user.grpc.LoginResponse
	25, // 33: com.aibook.user.grpc.UserService.GetProfile:output_type -> com.aibook.user.grpc.UserProfile
	25, // 34: com.aibook.user.grpc.UserService.UpdateProfile:output_type -> com.aibook.user.grpc.UserProfile
	7,  // 35: com.aibook.user.grpc.UserService.ResetPassword:output_type -> com.aibook.user.grpc.Empty
	7,  // 36: com.aibook.user.grpc.UserService.UpdateSystemPlan:output_type -> com.aibook.user.grpc.Empty
	10, // 37: com.aibook.user.grpc.UserService.GetSystemPlans:output_type -> com.aibook.user.grpc.SystemPlans
	7,  // 38: com.aibook.user.grpc.UserService.DeleteSystemPlan:output_type -> com.aibook.user.grpc.Empty
	7,  // 39: com.aibook.user.grpc.UserService.UpdateSystemFeaturePoints:output_type -> com.aibook.user.grpc.Empty
	12, // 40: com.aibook.user.grpc.UserService.GetSystemFeaturePoints:output_type -> com.aibook.user.grpc.GetSystemFeaturePointsResponse
	7,  // 41: com.aibook.user.grpc.UserService.DeleteSystemFeaturePoints:output_type -> com.aibook.user.grpc.Empty
	14, // 42: com.aibook.user.grpc.UserService.GetPlan:output_type -> com.aibook.user.grpc.UserPlan
	16, // 43: com.aibook.user.grpc.UserService.ReducePlan:output_type -> com.aibook.user.grpc.BenefitOrder
	7,  // 44: com.aibook.user.grpc.UserService.RollbackHalfPointBenefit:output_type -> com.aibook.user.grpc.Empty
	3,  // 45: com.aibook.user.grpc.UserService.BenefitRecord:output_type -> com.aibook.user.grpc.BenefitRecordResponse
	7,  // 46: com.aibook.user.grpc.UserService.ChargePoints:output_type -> com.aibook.user.grpc.Empty
	7,  // 47: com.aibook.user.grpc.UserService.Delete:output_type -> com.aibook.user.grpc.Empty
	6,  // 48: com.aibook.user.grpc.UserService.Search:output_type -> com.aibook.user.grpc.SearchResponse
	28, // [28:49] is the sub-list for method output_type
	7,  // [7:28] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_protos_user_proto_init() }
func file_protos_user_proto_init() {
	if File_protos_user_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_protos_user_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChargeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_user_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_user_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PageResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_user_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BenefitRecordResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_user_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BenefitRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_user_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_user_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_user_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Empty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_user_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteSystemPlanRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_user_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteSystemFeaturePointsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_user_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SystemPlans); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_user_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SystemFeaturePoints); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_user_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSystemFeaturePointsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_user_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SystemPlan); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_user_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserPlan); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_user_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReduceBenefitRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_user_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BenefitOrder); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_user_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeviceInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_user_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateUserRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_user_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoginRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_user_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoginResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_user_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoginWithCodeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_user_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePasswordRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_user_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResetPasswordRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_user_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetProfileRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_user_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserProfile); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_user_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DetailProfile); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_user_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateProfileRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_user_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEmailVerifyCodeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_user_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEmailVerifyCodeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_protos_user_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   30,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_protos_user_proto_goTypes,
		DependencyIndexes: file_protos_user_proto_depIdxs,
		MessageInfos:      file_protos_user_proto_msgTypes,
	}.Build()
	File_protos_user_proto = out.File
	file_protos_user_proto_rawDesc = nil
	file_protos_user_proto_goTypes = nil
	file_protos_user_proto_depIdxs = nil
}
