package db

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestInitSnowflake(t *testing.T) {
	// 测试雪花算法初始化
	err := InitSnowflake(1)
	assert.NoError(t, err)

	// 多次调用应该是安全的（使用 sync.Once）
	err = InitSnowflake(2) // 这个应该被忽略，因为已经初始化过了
	assert.NoError(t, err)
}

func TestGenerateOrderID(t *testing.T) {
	// 确保雪花算法已初始化
	err := InitSnowflake(1)
	assert.NoError(t, err)

	// 生成订单ID
	orderID1 := GenerateOrderID("STRIPE")
	orderID2 := GenerateOrderID("PAYPAL")

	// 验证订单ID不为空
	assert.NotEmpty(t, orderID1)
	assert.NotEmpty(t, orderID2)

	// 验证订单ID包含PSP提供商名称
	assert.Contains(t, orderID1, "STRIPE")
	assert.Contains(t, orderID2, "PAYPAL")

	// 验证订单ID是唯一的
	assert.NotEqual(t, orderID1, orderID2)

	t.Logf("Generated Order ID 1: %s", orderID1)
	t.Logf("Generated Order ID 2: %s", orderID2)
}

func TestGenerateOrderID_WithoutInit(t *testing.T) {
	// 重置雪花算法节点（这在实际使用中不会发生，但用于测试）
	// 注意：这个测试可能会影响其他测试，因为我们使用了全局变量
	// 但由于 sync.Once 的特性，实际上不会重新初始化

	// 生成订单ID（应该自动初始化为默认节点0）
	orderID := GenerateOrderID("TEST")

	// 验证订单ID不为空
	assert.NotEmpty(t, orderID)
	assert.Contains(t, orderID, "TEST")

	t.Logf("Generated Order ID without explicit init: %s", orderID)
}

func TestSnowflakeNodeID_Different(t *testing.T) {
	// 测试不同的节点ID
	err1 := InitSnowflake(1)
	assert.NoError(t, err1)

	// 生成一些ID
	ids := make([]string, 10)
	for i := 0; i < 10; i++ {
		ids[i] = GenerateOrderID("TEST")
	}

	// 验证所有ID都是唯一的
	idSet := make(map[string]bool)
	for _, id := range ids {
		assert.False(t, idSet[id], "Duplicate ID found: %s", id)
		idSet[id] = true
	}

	t.Logf("Generated %d unique IDs", len(ids))
}
