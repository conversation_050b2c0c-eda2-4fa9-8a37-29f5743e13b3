#!/bin/bash

# Payment Backend Test Runner Script
# 用于运行各种测试和生成报告的脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 打印标题
print_title() {
    echo
    print_message $BLUE "=================================================="
    print_message $BLUE "$1"
    print_message $BLUE "=================================================="
    echo
}

# 检查Go环境
check_go() {
    if ! command -v go &> /dev/null; then
        print_message $RED "错误: Go 未安装或不在 PATH 中"
        exit 1
    fi
    print_message $GREEN "Go 版本: $(go version)"
}

# 运行单元测试
run_unit_tests() {
    print_title "运行单元测试"
    
    print_message $YELLOW "运行 Handler 单元测试..."
    go test ./internal/handler -v -run "^TestPaymentHandler_"
    
    if [ $? -eq 0 ]; then
        print_message $GREEN "✅ 单元测试通过"
    else
        print_message $RED "❌ 单元测试失败"
        exit 1
    fi
}

# 运行集成测试
run_integration_tests() {
    print_title "运行集成测试"
    
    print_message $YELLOW "运行集成测试套件..."
    go test ./internal/handler -v -run "TestIntegrationSuite"
    
    if [ $? -eq 0 ]; then
        print_message $GREEN "✅ 集成测试通过"
    else
        print_message $RED "❌ 集成测试失败"
        exit 1
    fi
}

# 运行所有测试
run_all_tests() {
    print_title "运行所有测试"
    
    print_message $YELLOW "运行所有 Handler 测试..."
    go test ./internal/handler/... -v
    
    if [ $? -eq 0 ]; then
        print_message $GREEN "✅ 所有测试通过"
    else
        print_message $RED "❌ 测试失败"
        exit 1
    fi
}

# 生成覆盖率报告
generate_coverage() {
    print_title "生成测试覆盖率报告"
    
    print_message $YELLOW "生成覆盖率数据..."
    go test ./internal/handler -coverprofile=coverage.out
    
    if [ -f coverage.out ]; then
        print_message $YELLOW "生成覆盖率报告..."
        go tool cover -func coverage.out
        
        print_message $YELLOW "生成 HTML 覆盖率报告..."
        go tool cover -html coverage.out -o coverage.html
        
        print_message $GREEN "✅ 覆盖率报告已生成:"
        print_message $GREEN "  - 文本报告: 已显示在上方"
        print_message $GREEN "  - HTML报告: coverage.html"
    else
        print_message $RED "❌ 覆盖率文件生成失败"
        exit 1
    fi
}

# 运行性能测试
run_benchmark() {
    print_title "运行性能测试"
    
    print_message $YELLOW "运行基准测试..."
    go test ./internal/handler -bench=. -benchmem
    
    print_message $GREEN "✅ 性能测试完成"
}

# 清理测试文件
cleanup() {
    print_title "清理测试文件"
    
    print_message $YELLOW "清理临时文件..."
    rm -f coverage.out coverage.html
    
    print_message $GREEN "✅ 清理完成"
}

# 显示帮助信息
show_help() {
    echo "Payment Backend 测试运行脚本"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  unit        运行单元测试"
    echo "  integration 运行集成测试"
    echo "  all         运行所有测试 (默认)"
    echo "  coverage    生成测试覆盖率报告"
    echo "  benchmark   运行性能测试"
    echo "  cleanup     清理测试文件"
    echo "  help        显示此帮助信息"
    echo
    echo "示例:"
    echo "  $0              # 运行所有测试"
    echo "  $0 unit         # 只运行单元测试"
    echo "  $0 coverage     # 生成覆盖率报告"
    echo "  $0 cleanup      # 清理测试文件"
}

# 主函数
main() {
    check_go
    
    case "${1:-all}" in
        "unit")
            run_unit_tests
            ;;
        "integration")
            run_integration_tests
            ;;
        "all")
            run_all_tests
            ;;
        "coverage")
            run_all_tests
            generate_coverage
            ;;
        "benchmark")
            run_benchmark
            ;;
        "cleanup")
            cleanup
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            print_message $RED "错误: 未知选项 '$1'"
            echo
            show_help
            exit 1
            ;;
    esac
    
    print_title "测试完成"
    print_message $GREEN "🎉 所有操作已成功完成!"
}

# 运行主函数
main "$@"
