#!/bin/bash

# 快速测试流量包API
# 使用方法: ./quick_test.sh [server_host]

SERVER_HOST=${1:-"http://localhost:8080"}
echo "测试服务器: $SERVER_HOST"

echo "=== 1. 创建流量包 ==="
curl -X PUT "${SERVER_HOST}/api/v1/pay-service/admin/store-service/packages" \
  -H "Content-Type: application/json" \
  -d '{
    "package_name": "测试流量包",
    "package_desc": "用于测试的流量包",
    "entitlement": "TEST_TRAFFIC",
    "original_price": 19.99,
    "discount_price": 15.99,
    "currency": "USD",
    "country": "US"
  }'

echo -e "\n\n=== 2. 终端用户查询 ==="
curl -X GET "${SERVER_HOST}/api/v1/pay-service/store-service/packages" \
  -H "Content-Type: application/json" \
  -H "x-user-id: test_user" \
  -H "x-role: customer"

echo -e "\n\n=== 3. 管理员查询 ==="
curl -X GET "${SERVER_HOST}/api/v1/pay-service/admin/store-service/packages" \
  -H "Content-Type: application/json"

echo -e "\n\n=== 测试完成 ==="
