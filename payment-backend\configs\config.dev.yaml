# 开发环境配置

# 服务器配置
server:
  port: 8080
  mode: "debug"

# 数据库配置（开发环境使用MySQL数据库）
database:
  driver: "mysql"
  # host: "*************"
  host: "**************"
  # host: "**************"
  port: 3306
  username: "root"
  password: "123456"
  database: "aibook_payment"

  # host: "*************"
  # port: 23306
  # username: "root"
  # password: "Jengcloud@0199"
  # database: "aibook_payment"
  # ssl_mode: "disable"

# 支付配置（开发环境使用模拟网关）
payment:
  providers:
    paypal:
      enabled: true
      api_key: "mock_paypal_api_key_dev"
      secret_key: "mock_paypal_secret_key"
      success_url: "https://your-domain.com/success"
      cancel_url: "https://your-domain.com/cancel"
      webhook:
        url: "http://localhost:8080/api/v1/pay-service/webhooks/paypal"
        secret: "mock_paypal_webhook_secret"
      settings:
        environment: "sandbox"

    stripe:
      enabled: true
      api_key: "sk_test_51RbdvCC53MAl6WmqszVyfUOFJqVBbMNnhJAC4hVyEORaD7uecrN6rqpVNtvezPXabFhZkillLEt0VwMGyhUWIvvA00BCdee8is"
      secret_key: "mock_stripe_secret_key"
      success_url: "https://your-domain.com/success"
      cancel_url: "https://your-domain.com/cancel"
      webhook:
        url: "http://localhost:8080/api/v1/pay-service/webhooks/stripe"
        secret: "mock_stripe_webhook_secret"
      settings:
        environment: "test"

# 日志配置
log:
  level: "debug"
  format: "console"
  output: "stdout"
  filename: ""
  max_size: 30
  max_backups: 6
  max_age: 28

# 雪花算法配置
snowflake:
  node_id: 1

# Nacos配置（统一配置中心和注册中心）
nacos:
  enabled: true # 是否启用Nacos，默认关闭
  endpoints: # Nacos服务器地址列表
    - "*************:8848"
  namespace: "dev1" # 命名空间
  username: "nacos" # 用户名
  password: "aibook" # 密码

  # 配置中心设置
  config:
    enabled: true # 是否启用配置中心
    data_id: "payment-backend.yaml" # 配置文件ID
    group: "DEFAULT_GROUP" # 配置组
    timeout: 30 # 连接超时时间(秒)

  # 注册中心设置
  registry:
    enabled: true # 是否启用注册中心
