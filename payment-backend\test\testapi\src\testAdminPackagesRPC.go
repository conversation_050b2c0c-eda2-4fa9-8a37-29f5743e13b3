package main

import (
	"fmt"
	"io"
	"time"
)

// testAdminAddPackagesRPC 测试内网创建流量包接口 (Dubbo RPC)
func testAdminAddPackagesRPC(config *EnvConfig) TestResult {
	start := time.Now()
	testName := "Admin Add Packages (Internal Dubbo RPC)"

	// 使用 HTTP 协议调用 Dubbo RPC 接口
	url := config.LocalRpcURL + "/com.aibook.payment.grpc.StoreService/AdminAddPackages"
	headers := map[string]string{
		"x-trace-id": generateTraceID(),
	}

	// 构造 RPC 请求体 - 根据 protobuf 定义
	requestBody := map[string]interface{}{
		"package_name":     "RPC测试流量包",
		"package_desc":     "用于RPC API测试的流量包",
		"entitlement":      100,
		"entitlement_desc": "100GB流量",
		"original_price":   19.99,
		"discount_desc":    "",
		"sale_status":      "on_sale",
		"currency":         "USD",
		"country":          "US",
		"extra1":           "rpc",
		"extra2":           "test",
		"extra3":           100,
		"extra4":           30,
	}

	// 记录详细请求信息到日志文件
	if detailLogger != nil {
		detailLogger.Printf("--- %s ---", testName)
		detailLogger.Printf("URL: %s", url)
		detailLogger.Printf("Headers: %+v", headers)
		detailLogger.Printf("Request Body: %+v", requestBody)
	}

	resp, err := makeHTTPRequest("POST", url, headers, requestBody)
	duration := time.Since(start)

	if err != nil {
		if detailLogger != nil {
			detailLogger.Printf("Request failed: %v", err)
		}
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("Request failed: %v", err),
			Duration: duration,
		}
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		if detailLogger != nil {
			detailLogger.Printf("Failed to read response: %v", err)
		}
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("Failed to read response: %v", err),
			Duration: duration,
		}
	}

	// 记录详细响应信息到日志文件
	if detailLogger != nil {
		detailLogger.Printf("Response Status: %d", resp.StatusCode)
		detailLogger.Printf("Response Body: %s", prettyJson(body))
	}

	// 简要信息输出到控制台
	fmt.Printf("  %s: Status=%d\n", testName, resp.StatusCode)
	if resp.StatusCode == 200 {
		return TestResult{
			TestName: testName,
			Success:  true,
			Message:  "Package created successfully via Dubbo RPC",
			Duration: duration,
		}
	} else {
		if detailLogger != nil {
			detailLogger.Printf("Request failed with HTTP %d: %s", resp.StatusCode, string(body))
		}
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("HTTP %d: %s", resp.StatusCode, string(body)),
			Duration: duration,
		}
	}
}

// testAdminListAllPackagesRPC 测试内网获取所有流量包接口 (Dubbo RPC)
func testAdminListAllPackagesRPC(config *EnvConfig) TestResult {
	start := time.Now()
	testName := "Admin List All Packages (Internal Dubbo RPC)"

	// 使用 HTTP 协议调用 Dubbo RPC 接口
	url := config.LocalRpcURL + "/com.aibook.payment.grpc.StoreService/AdminListAllPackages"
	headers := map[string]string{
		"x-trace-id": generateTraceID(),
	}

	// 构造 RPC 请求体 - 根据 protobuf 定义
	requestBody := map[string]interface{}{
		"pagination": map[string]interface{}{
			"limit":  50,
			"offset": 0,
		},
	}

	// 记录详细请求信息到日志文件
	if detailLogger != nil {
		detailLogger.Printf("--- %s ---", testName)
		detailLogger.Printf("URL: %s", url)
		detailLogger.Printf("Headers: %+v", headers)
		detailLogger.Printf("Request Body: %+v", requestBody)
	}

	resp, err := makeHTTPRequest("POST", url, headers, requestBody)
	duration := time.Since(start)

	if err != nil {
		if detailLogger != nil {
			detailLogger.Printf("Request failed: %v", err)
		}
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("Request failed: %v", err),
			Duration: duration,
		}
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		if detailLogger != nil {
			detailLogger.Printf("Failed to read response: %v", err)
		}
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("Failed to read response: %v", err),
			Duration: duration,
		}
	}

	// 记录详细响应信息到日志文件
	if detailLogger != nil {
		detailLogger.Printf("Response Status: %d", resp.StatusCode)
		detailLogger.Printf("Response Body: %s", prettyJson(body))
	}

	// 简要信息输出到控制台
	fmt.Printf("  %s: Status=%d\n", testName, resp.StatusCode)
	if resp.StatusCode == 200 {
		return TestResult{
			TestName: testName,
			Success:  true,
			Message:  "Admin packages retrieved successfully via Dubbo RPC",
			Duration: duration,
		}
	} else {
		if detailLogger != nil {
			detailLogger.Printf("Request failed with HTTP %d: %s", resp.StatusCode, string(body))
		}
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("HTTP %d: %s", resp.StatusCode, string(body)),
			Duration: duration,
		}
	}
}

// testAdminUpdatePackagesRPC 测试内网更新流量包接口 (Dubbo RPC)
func testAdminUpdatePackagesRPC(config *EnvConfig) TestResult {
	start := time.Now()
	testName := "Admin Update Packages (Internal Dubbo RPC)"

	// 使用 HTTP 协议调用 Dubbo RPC 接口
	url := config.LocalRpcURL + "/com.aibook.payment.grpc.StoreService/AdminUpdatePackages"
	headers := map[string]string{
		"x-trace-id": generateTraceID(),
	}

	// 构造 RPC 请求体 - 根据 protobuf 定义
	requestBody := map[string]interface{}{
		"package_id":     "test-package-id-replace-with-real-one", // 需要替换为实际的package_id
		"package_name":   "RPC更新后的测试流量包",
		"original_price": 24.99,
	}

	// 记录详细请求信息到日志文件
	if detailLogger != nil {
		detailLogger.Printf("--- %s ---", testName)
		detailLogger.Printf("URL: %s", url)
		detailLogger.Printf("Headers: %+v", headers)
		detailLogger.Printf("Request Body: %+v", requestBody)
	}

	resp, err := makeHTTPRequest("POST", url, headers, requestBody)
	duration := time.Since(start)

	if err != nil {
		if detailLogger != nil {
			detailLogger.Printf("Request failed: %v", err)
		}
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("Request failed: %v", err),
			Duration: duration,
		}
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		if detailLogger != nil {
			detailLogger.Printf("Failed to read response: %v", err)
		}
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("Failed to read response: %v", err),
			Duration: duration,
		}
	}

	// 记录详细响应信息到日志文件
	if detailLogger != nil {
		detailLogger.Printf("Response Status: %d", resp.StatusCode)
		detailLogger.Printf("Response Body: %s", prettyJson(body))
	}

	// 简要信息输出到控制台
	fmt.Printf("  %s: Status=%d\n", testName, resp.StatusCode)
	if resp.StatusCode == 200 {
		return TestResult{
			TestName: testName,
			Success:  true,
			Message:  "Package updated successfully via Dubbo RPC",
			Duration: duration,
		}
	} else {
		if detailLogger != nil {
			detailLogger.Printf("Request failed with HTTP %d: %s", resp.StatusCode, string(body))
		}
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("HTTP %d: %s", resp.StatusCode, string(body)),
			Duration: duration,
		}
	}
}

// testAdminDeletePackagesRPC 测试内网删除流量包接口 (Dubbo RPC)
func testAdminDeletePackagesRPC(config *EnvConfig) TestResult {
	start := time.Now()
	testName := "Admin Delete Packages (Internal Dubbo RPC)"

	// 使用 HTTP 协议调用 Dubbo RPC 接口
	url := config.LocalRpcURL + "/com.aibook.payment.grpc.StoreService/AdminDeletePackages"
	headers := map[string]string{
		"x-trace-id": generateTraceID(),
	}

	// 构造 RPC 请求体 - 根据 protobuf 定义
	requestBody := map[string]interface{}{
		"package_id": "test-package-id-replace-with-real-one", // 需要替换为实际的package_id
		"currency":   "USD",
		"country":    "US",
	}

	// 记录详细请求信息到日志文件
	if detailLogger != nil {
		detailLogger.Printf("--- %s ---", testName)
		detailLogger.Printf("URL: %s", url)
		detailLogger.Printf("Headers: %+v", headers)
		detailLogger.Printf("Request Body: %+v", requestBody)
	}

	resp, err := makeHTTPRequest("POST", url, headers, requestBody)
	duration := time.Since(start)

	if err != nil {
		if detailLogger != nil {
			detailLogger.Printf("Request failed: %v", err)
		}
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("Request failed: %v", err),
			Duration: duration,
		}
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		if detailLogger != nil {
			detailLogger.Printf("Failed to read response: %v", err)
		}
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("Failed to read response: %v", err),
			Duration: duration,
		}
	}

	// 记录详细响应信息到日志文件
	if detailLogger != nil {
		detailLogger.Printf("Response Status: %d", resp.StatusCode)
		detailLogger.Printf("Response Body: %s", prettyJson(body))
	}

	// 简要信息输出到控制台
	fmt.Printf("  %s: Status=%d\n", testName, resp.StatusCode)
	if resp.StatusCode == 200 {
		return TestResult{
			TestName: testName,
			Success:  true,
			Message:  "Package deleted successfully via Dubbo RPC",
			Duration: duration,
		}
	} else {
		if detailLogger != nil {
			detailLogger.Printf("Request failed with HTTP %d: %s", resp.StatusCode, string(body))
		}
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("HTTP %d: %s", resp.StatusCode, string(body)),
			Duration: duration,
		}
	}
}
