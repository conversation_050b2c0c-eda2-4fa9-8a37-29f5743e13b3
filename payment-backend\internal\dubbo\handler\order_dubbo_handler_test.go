package handler

import (
	"context"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	"payment-backend/internal/domain"
	"payment-backend/internal/dubbo/shared-protos/gen/go/paymentpb"
	"payment-backend/internal/logger"
	"payment-backend/internal/middleware"
)

// MockOrderService 模拟订单服务
type MockOrderService struct {
	mock.Mock
}

func (m *MockOrderService) CreateOrder(userCtx *middleware.UserContext, req *domain.CreateOrderRequest) (*domain.CreateOrderResponse, error) {
	args := m.Called(userCtx, req)
	return args.Get(0).(*domain.CreateOrderResponse), args.Error(1)
}

func (m *MockOrderService) GetOrder(orderID string) (*domain.Order, error) {
	args := m.Called(orderID)
	return args.Get(0).(*domain.Order), args.Error(1)
}

func (m *MockOrderService) GetOrderByID(id uint64) (*domain.Order, error) {
	args := m.Called(id)
	return args.Get(0).(*domain.Order), args.Error(1)
}

func (m *MockOrderService) GetUserOrders(userID string, limit, offset int) ([]*domain.Order, error) {
	args := m.Called(userID, limit, offset)
	return args.Get(0).([]*domain.Order), args.Error(1)
}

func (m *MockOrderService) ListAllOrders(filter *domain.OrderFilter, pagination *domain.PaginationRequest) (*domain.ListOrdersResponse, error) {
	args := m.Called(filter, pagination)
	return args.Get(0).(*domain.ListOrdersResponse), args.Error(1)
}

func (m *MockOrderService) UpdateOrder(orderID string, req *domain.UpdateOrderRequest) error {
	args := m.Called(orderID, req)
	return args.Error(0)
}

func (m *MockOrderService) CancelOrder(orderID string) error {
	args := m.Called(orderID)
	return args.Error(0)
}

func (m *MockOrderService) RefundOrder(orderID string, amount *float64) error {
	args := m.Called(orderID, amount)
	return args.Error(0)
}

func (m *MockOrderService) ProcessWebhook(provider string, header http.Header, payload []byte) error {
	args := m.Called(provider, header, payload)
	return args.Error(0)
}

// MockLogger 模拟日志记录器
type MockLogger struct {
	mock.Mock
}

func (m *MockLogger) Debug(msg string, fields ...zap.Field) {
	m.Called(msg, fields)
}

func (m *MockLogger) Info(msg string, fields ...zap.Field) {
	m.Called(msg, fields)
}

func (m *MockLogger) Warn(msg string, fields ...zap.Field) {
	m.Called(msg, fields)
}

func (m *MockLogger) Error(msg string, fields ...zap.Field) {
	m.Called(msg, fields)
}

func (m *MockLogger) Fatal(msg string, fields ...zap.Field) {
	m.Called(msg, fields)
}

func (m *MockLogger) With(fields ...zap.Field) logger.Logger {
	args := m.Called(fields)
	return args.Get(0).(logger.Logger)
}

func (m *MockLogger) Sync() error {
	args := m.Called()
	return args.Error(0)
}

func (m *MockLogger) GetZapLogger() *zap.Logger {
	args := m.Called()
	return args.Get(0).(*zap.Logger)
}

func TestOrderDubboHandler_ListAllOrders(t *testing.T) {
	tests := []struct {
		name           string
		request        *paymentpb.ListAllOrdersRequest
		mockResponse   *domain.ListOrdersResponse
		mockError      error
		expectedError  bool
		expectedOrders int
	}{
		{
			name: "成功获取订单列表",
			request: &paymentpb.ListAllOrdersRequest{
				Filter: &paymentpb.OrderFilter{
					UserId: stringPtr("user123"),
				},
				Pagination: &paymentpb.PaginationRequest{
					Limit:  10,
					Offset: 0,
				},
			},
			mockResponse: &domain.ListOrdersResponse{
				Orders: []*domain.Order{
					{
						ID:          1,
						OrderID:     "order_123",
						UserID:      "user123",
						ProductID:   "prod_123",
						ProductDesc: "Test Product",
						PriceID:     "price_123",
						Quantity:    1,
						Amount:      99.99,
						NetAmount:   99.99,
						Currency:    "USD",
						PayStatus:   "created",
						PayedMethod: "stripe",
						PSPProvider: "stripe",
						CreatedAt:   time.Now(),
						UpdatedAt:   time.Now(),
					},
				},
				Pagination: &domain.PaginationResponse{
					Total:     1,
					Limit:     10,
					Offset:    0,
					Remaining: 0,
				},
			},
			mockError:      nil,
			expectedError:  false,
			expectedOrders: 1,
		},
		{
			name: "空过滤条件和分页参数",
			request: &paymentpb.ListAllOrdersRequest{
				Filter:     nil,
				Pagination: nil,
			},
			mockResponse: &domain.ListOrdersResponse{
				Orders: []*domain.Order{},
				Pagination: &domain.PaginationResponse{
					Total:     0,
					Limit:     50,
					Offset:    0,
					Remaining: 0,
				},
			},
			mockError:      nil,
			expectedError:  false,
			expectedOrders: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建模拟对象
			mockOrderService := new(MockOrderService)
			mockLogger := new(MockLogger)

			// 设置期望调用
			mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything).Return()
			mockOrderService.On("ListAllOrders", mock.AnythingOfType("*domain.OrderFilter"), mock.AnythingOfType("*domain.PaginationRequest")).Return(tt.mockResponse, tt.mockError)

			// 创建处理器
			handler := NewOrderDubboHandler(mockOrderService, mockLogger)

			// 执行测试
			response, err := handler.ListAllOrders(context.Background(), tt.request)

			// 验证结果
			if tt.expectedError {
				assert.Error(t, err)
				assert.Nil(t, response)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, response)
				assert.Equal(t, tt.expectedOrders, len(response.Orders))
				assert.NotNil(t, response.Pagination)
			}

			// 验证模拟对象的调用
			mockOrderService.AssertExpectations(t)
			mockLogger.AssertExpectations(t)
		})
	}
}

func TestOrderDubboHandler_convertFilterFromProto(t *testing.T) {
	handler := &OrderDubboHandler{}

	tests := []struct {
		name     string
		input    *paymentpb.OrderFilter
		expected *domain.OrderFilter
	}{
		{
			name:     "nil过滤条件",
			input:    nil,
			expected: &domain.OrderFilter{},
		},
		{
			name: "完整过滤条件",
			input: &paymentpb.OrderFilter{
				UserId:      stringPtr("user123"),
				Currency:    stringPtr("USD"),
				PayStatus:   stringPtr("completed"),
				PayedMethod: stringPtr("stripe"),
				PspProvider: stringPtr("stripe"),
			},
			expected: &domain.OrderFilter{
				UserID:      stringPtr("user123"),
				Currency:    stringPtr("USD"),
				PayStatus:   stringPtr("completed"),
				PayedMethod: stringPtr("stripe"),
				PSPProvider: stringPtr("stripe"),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := handler.convertFilterFromProto(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestOrderDubboHandler_convertPaginationFromProto(t *testing.T) {
	handler := &OrderDubboHandler{}

	tests := []struct {
		name     string
		input    *paymentpb.PaginationRequest
		expected *domain.PaginationRequest
	}{
		{
			name:  "nil分页参数",
			input: nil,
			expected: &domain.PaginationRequest{
				Limit:  50,
				Offset: 0,
			},
		},
		{
			name: "正常分页参数",
			input: &paymentpb.PaginationRequest{
				Limit:  20,
				Offset: 10,
			},
			expected: &domain.PaginationRequest{
				Limit:  20,
				Offset: 10,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := handler.convertPaginationFromProto(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestOrderDubboHandler_convertOrderToProto(t *testing.T) {
	handler := &OrderDubboHandler{}
	now := time.Now()

	order := &domain.Order{
		ID:          1,
		OrderID:     "order_123",
		UserID:      "user123",
		ProductID:   "prod_123",
		ProductDesc: "Test Product",
		PriceID:     "price_123",
		Quantity:    1,
		Amount:      99.99,
		NetAmount:   99.99,
		Currency:    "USD",
		PayStatus:   "created",
		PayedMethod: "stripe",
		PSPProvider: "stripe",
		PayedAt:     &now,
		CreatedAt:   now,
		UpdatedAt:   now,
		Deleted:     false,
	}

	result := handler.convertOrderToProto(order)

	assert.Equal(t, order.ID, result.Id)
	assert.Equal(t, order.OrderID, result.OrderId)
	assert.Equal(t, order.UserID, result.UserId)
	assert.Equal(t, order.ProductID, result.ProductId)
	assert.Equal(t, order.ProductDesc, result.ProductDesc)
	assert.Equal(t, order.PriceID, result.PriceId)
	assert.Equal(t, order.Quantity, result.Quantity)
	assert.Equal(t, order.Amount, result.Amount)
	assert.Equal(t, order.NetAmount, result.NetAmount)
	assert.Equal(t, order.Currency, result.Currency)
	assert.Equal(t, order.PayStatus, result.PayStatus)
	assert.Equal(t, order.PayedMethod, result.PayedMethod)
	assert.Equal(t, order.PSPProvider, result.PspProvider)
	assert.Equal(t, timestamppb.New(*order.PayedAt), result.PayedAt)
	assert.Equal(t, timestamppb.New(order.CreatedAt), result.CreatedAt)
	assert.Equal(t, timestamppb.New(order.UpdatedAt), result.UpdatedAt)
	assert.Equal(t, order.Deleted, result.Deleted)
}

// 辅助函数
func stringPtr(s string) *string {
	return &s
}
