// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.21.12
// source: protos/store.proto

package storepb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 流量包信息
type Package struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Id                uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                                               // 自增主键 ID
	PackageId         string                 `protobuf:"bytes,2,opt,name=package_id,json=packageId,proto3" json:"package_id,omitempty"`                                 // 流量包ID (UUID v4)
	PackageName       string                 `protobuf:"bytes,3,opt,name=package_name,json=packageName,proto3" json:"package_name,omitempty"`                           // 流量包名称
	PackageDesc       string                 `protobuf:"bytes,4,opt,name=package_desc,json=packageDesc,proto3" json:"package_desc,omitempty"`                           // 流量包描述
	Entitlement       int32                  `protobuf:"varint,5,opt,name=entitlement,proto3" json:"entitlement,omitempty"`                                             // 购买后获得的权益
	EntitlementDesc   string                 `protobuf:"bytes,6,opt,name=entitlement_desc,json=entitlementDesc,proto3" json:"entitlement_desc,omitempty"`               // 购买后获得的权益描述
	OriginalPrice     float64                `protobuf:"fixed64,7,opt,name=original_price,json=originalPrice,proto3" json:"original_price,omitempty"`                   // 流量包原价
	DiscountPrice     *float64               `protobuf:"fixed64,8,opt,name=discount_price,json=discountPrice,proto3,oneof" json:"discount_price,omitempty"`             // 流量包优惠价，可为空
	DiscountStartTime *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=discount_start_time,json=discountStartTime,proto3,oneof" json:"discount_start_time,omitempty"` // 优惠开始时间
	DiscountEndTime   *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=discount_end_time,json=discountEndTime,proto3,oneof" json:"discount_end_time,omitempty"`      // 优惠结束时间
	DiscountDesc      string                 `protobuf:"bytes,11,opt,name=discount_desc,json=discountDesc,proto3" json:"discount_desc,omitempty"`                       // 优惠活动描述
	SaleStatus        string                 `protobuf:"bytes,12,opt,name=sale_status,json=saleStatus,proto3" json:"sale_status,omitempty"`                             // 出售状态 (on_sale/off_sale)
	Currency          string                 `protobuf:"bytes,13,opt,name=currency,proto3" json:"currency,omitempty"`                                                   // 货币单位
	Country           string                 `protobuf:"bytes,14,opt,name=country,proto3" json:"country,omitempty"`                                                     // 国家
	Extra1            string                 `protobuf:"bytes,15,opt,name=extra1,proto3" json:"extra1,omitempty"`                                                       // 附件字段1
	Extra2            string                 `protobuf:"bytes,16,opt,name=extra2,proto3" json:"extra2,omitempty"`                                                       // 附件字段2
	Extra3            int32                  `protobuf:"varint,17,opt,name=extra3,proto3" json:"extra3,omitempty"`                                                      // 附件字段3
	Extra4            int32                  `protobuf:"varint,18,opt,name=extra4,proto3" json:"extra4,omitempty"`                                                      // 附件字段4
	CreatedAt         *timestamppb.Timestamp `protobuf:"bytes,19,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`                                // 创建时间
	UpdatedAt         *timestamppb.Timestamp `protobuf:"bytes,20,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`                                // 更新时间
	Deleted           bool                   `protobuf:"varint,21,opt,name=deleted,proto3" json:"deleted,omitempty"`                                                    // 是否被逻辑删除
	DeletedAt         *timestamppb.Timestamp `protobuf:"bytes,22,opt,name=deleted_at,json=deletedAt,proto3,oneof" json:"deleted_at,omitempty"`                          // 删除时间（逻辑删除）
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *Package) Reset() {
	*x = Package{}
	mi := &file_protos_store_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Package) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Package) ProtoMessage() {}

func (x *Package) ProtoReflect() protoreflect.Message {
	mi := &file_protos_store_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Package.ProtoReflect.Descriptor instead.
func (*Package) Descriptor() ([]byte, []int) {
	return file_protos_store_proto_rawDescGZIP(), []int{0}
}

func (x *Package) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Package) GetPackageId() string {
	if x != nil {
		return x.PackageId
	}
	return ""
}

func (x *Package) GetPackageName() string {
	if x != nil {
		return x.PackageName
	}
	return ""
}

func (x *Package) GetPackageDesc() string {
	if x != nil {
		return x.PackageDesc
	}
	return ""
}

func (x *Package) GetEntitlement() int32 {
	if x != nil {
		return x.Entitlement
	}
	return 0
}

func (x *Package) GetEntitlementDesc() string {
	if x != nil {
		return x.EntitlementDesc
	}
	return ""
}

func (x *Package) GetOriginalPrice() float64 {
	if x != nil {
		return x.OriginalPrice
	}
	return 0
}

func (x *Package) GetDiscountPrice() float64 {
	if x != nil && x.DiscountPrice != nil {
		return *x.DiscountPrice
	}
	return 0
}

func (x *Package) GetDiscountStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DiscountStartTime
	}
	return nil
}

func (x *Package) GetDiscountEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DiscountEndTime
	}
	return nil
}

func (x *Package) GetDiscountDesc() string {
	if x != nil {
		return x.DiscountDesc
	}
	return ""
}

func (x *Package) GetSaleStatus() string {
	if x != nil {
		return x.SaleStatus
	}
	return ""
}

func (x *Package) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *Package) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *Package) GetExtra1() string {
	if x != nil {
		return x.Extra1
	}
	return ""
}

func (x *Package) GetExtra2() string {
	if x != nil {
		return x.Extra2
	}
	return ""
}

func (x *Package) GetExtra3() int32 {
	if x != nil {
		return x.Extra3
	}
	return 0
}

func (x *Package) GetExtra4() int32 {
	if x != nil {
		return x.Extra4
	}
	return 0
}

func (x *Package) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Package) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *Package) GetDeleted() bool {
	if x != nil {
		return x.Deleted
	}
	return false
}

func (x *Package) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

// 流量包响应（终端用户）
type PackageResponse struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	PackageId         string                 `protobuf:"bytes,1,opt,name=package_id,json=packageId,proto3" json:"package_id,omitempty"`
	PackageName       string                 `protobuf:"bytes,2,opt,name=package_name,json=packageName,proto3" json:"package_name,omitempty"`
	PackageDesc       string                 `protobuf:"bytes,3,opt,name=package_desc,json=packageDesc,proto3" json:"package_desc,omitempty"`
	Entitlement       int32                  `protobuf:"varint,4,opt,name=entitlement,proto3" json:"entitlement,omitempty"`
	EntitlementDesc   string                 `protobuf:"bytes,5,opt,name=entitlement_desc,json=entitlementDesc,proto3" json:"entitlement_desc,omitempty"`               // 购买后获得的权益描述
	Price             float64                `protobuf:"fixed64,6,opt,name=price,proto3" json:"price,omitempty"`                                                        // 实际价格（优惠价或原价）
	OriginalPrice     *float64               `protobuf:"fixed64,7,opt,name=original_price,json=originalPrice,proto3,oneof" json:"original_price,omitempty"`             // 原价（仅在有优惠时显示）
	DiscountStartTime *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=discount_start_time,json=discountStartTime,proto3,oneof" json:"discount_start_time,omitempty"` // 优惠开始时间
	DiscountEndTime   *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=discount_end_time,json=discountEndTime,proto3,oneof" json:"discount_end_time,omitempty"`       // 优惠结束时间
	DiscountDesc      string                 `protobuf:"bytes,10,opt,name=discount_desc,json=discountDesc,proto3" json:"discount_desc,omitempty"`                       // 优惠活动描述
	SaleStatus        string                 `protobuf:"bytes,11,opt,name=sale_status,json=saleStatus,proto3" json:"sale_status,omitempty"`                             // 出售状态
	Currency          string                 `protobuf:"bytes,12,opt,name=currency,proto3" json:"currency,omitempty"`
	Country           string                 `protobuf:"bytes,13,opt,name=country,proto3" json:"country,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *PackageResponse) Reset() {
	*x = PackageResponse{}
	mi := &file_protos_store_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PackageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PackageResponse) ProtoMessage() {}

func (x *PackageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_protos_store_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PackageResponse.ProtoReflect.Descriptor instead.
func (*PackageResponse) Descriptor() ([]byte, []int) {
	return file_protos_store_proto_rawDescGZIP(), []int{1}
}

func (x *PackageResponse) GetPackageId() string {
	if x != nil {
		return x.PackageId
	}
	return ""
}

func (x *PackageResponse) GetPackageName() string {
	if x != nil {
		return x.PackageName
	}
	return ""
}

func (x *PackageResponse) GetPackageDesc() string {
	if x != nil {
		return x.PackageDesc
	}
	return ""
}

func (x *PackageResponse) GetEntitlement() int32 {
	if x != nil {
		return x.Entitlement
	}
	return 0
}

func (x *PackageResponse) GetEntitlementDesc() string {
	if x != nil {
		return x.EntitlementDesc
	}
	return ""
}

func (x *PackageResponse) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *PackageResponse) GetOriginalPrice() float64 {
	if x != nil && x.OriginalPrice != nil {
		return *x.OriginalPrice
	}
	return 0
}

func (x *PackageResponse) GetDiscountStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DiscountStartTime
	}
	return nil
}

func (x *PackageResponse) GetDiscountEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DiscountEndTime
	}
	return nil
}

func (x *PackageResponse) GetDiscountDesc() string {
	if x != nil {
		return x.DiscountDesc
	}
	return ""
}

func (x *PackageResponse) GetSaleStatus() string {
	if x != nil {
		return x.SaleStatus
	}
	return ""
}

func (x *PackageResponse) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *PackageResponse) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

// 流量包响应（管理员）
type AdminPackageResponse struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Id                uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	PackageId         string                 `protobuf:"bytes,2,opt,name=package_id,json=packageId,proto3" json:"package_id,omitempty"`
	PackageName       string                 `protobuf:"bytes,3,opt,name=package_name,json=packageName,proto3" json:"package_name,omitempty"`
	PackageDesc       string                 `protobuf:"bytes,4,opt,name=package_desc,json=packageDesc,proto3" json:"package_desc,omitempty"`
	Entitlement       int32                  `protobuf:"varint,5,opt,name=entitlement,proto3" json:"entitlement,omitempty"`
	EntitlementDesc   string                 `protobuf:"bytes,6,opt,name=entitlement_desc,json=entitlementDesc,proto3" json:"entitlement_desc,omitempty"` // 购买后获得的权益描述
	OriginalPrice     float64                `protobuf:"fixed64,7,opt,name=original_price,json=originalPrice,proto3" json:"original_price,omitempty"`
	DiscountPrice     *float64               `protobuf:"fixed64,8,opt,name=discount_price,json=discountPrice,proto3,oneof" json:"discount_price,omitempty"`
	DiscountStartTime *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=discount_start_time,json=discountStartTime,proto3,oneof" json:"discount_start_time,omitempty"`
	DiscountEndTime   *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=discount_end_time,json=discountEndTime,proto3,oneof" json:"discount_end_time,omitempty"`
	DiscountDesc      string                 `protobuf:"bytes,11,opt,name=discount_desc,json=discountDesc,proto3" json:"discount_desc,omitempty"` // 优惠活动描述
	SaleStatus        string                 `protobuf:"bytes,12,opt,name=sale_status,json=saleStatus,proto3" json:"sale_status,omitempty"`
	Currency          string                 `protobuf:"bytes,13,opt,name=currency,proto3" json:"currency,omitempty"`
	Country           string                 `protobuf:"bytes,14,opt,name=country,proto3" json:"country,omitempty"`
	Extra1            string                 `protobuf:"bytes,15,opt,name=extra1,proto3" json:"extra1,omitempty"`
	Extra2            string                 `protobuf:"bytes,16,opt,name=extra2,proto3" json:"extra2,omitempty"`
	Extra3            int32                  `protobuf:"varint,17,opt,name=extra3,proto3" json:"extra3,omitempty"`
	Extra4            int32                  `protobuf:"varint,18,opt,name=extra4,proto3" json:"extra4,omitempty"`
	CreatedAt         *timestamppb.Timestamp `protobuf:"bytes,19,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt         *timestamppb.Timestamp `protobuf:"bytes,20,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *AdminPackageResponse) Reset() {
	*x = AdminPackageResponse{}
	mi := &file_protos_store_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AdminPackageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdminPackageResponse) ProtoMessage() {}

func (x *AdminPackageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_protos_store_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdminPackageResponse.ProtoReflect.Descriptor instead.
func (*AdminPackageResponse) Descriptor() ([]byte, []int) {
	return file_protos_store_proto_rawDescGZIP(), []int{2}
}

func (x *AdminPackageResponse) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AdminPackageResponse) GetPackageId() string {
	if x != nil {
		return x.PackageId
	}
	return ""
}

func (x *AdminPackageResponse) GetPackageName() string {
	if x != nil {
		return x.PackageName
	}
	return ""
}

func (x *AdminPackageResponse) GetPackageDesc() string {
	if x != nil {
		return x.PackageDesc
	}
	return ""
}

func (x *AdminPackageResponse) GetEntitlement() int32 {
	if x != nil {
		return x.Entitlement
	}
	return 0
}

func (x *AdminPackageResponse) GetEntitlementDesc() string {
	if x != nil {
		return x.EntitlementDesc
	}
	return ""
}

func (x *AdminPackageResponse) GetOriginalPrice() float64 {
	if x != nil {
		return x.OriginalPrice
	}
	return 0
}

func (x *AdminPackageResponse) GetDiscountPrice() float64 {
	if x != nil && x.DiscountPrice != nil {
		return *x.DiscountPrice
	}
	return 0
}

func (x *AdminPackageResponse) GetDiscountStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DiscountStartTime
	}
	return nil
}

func (x *AdminPackageResponse) GetDiscountEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DiscountEndTime
	}
	return nil
}

func (x *AdminPackageResponse) GetDiscountDesc() string {
	if x != nil {
		return x.DiscountDesc
	}
	return ""
}

func (x *AdminPackageResponse) GetSaleStatus() string {
	if x != nil {
		return x.SaleStatus
	}
	return ""
}

func (x *AdminPackageResponse) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *AdminPackageResponse) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *AdminPackageResponse) GetExtra1() string {
	if x != nil {
		return x.Extra1
	}
	return ""
}

func (x *AdminPackageResponse) GetExtra2() string {
	if x != nil {
		return x.Extra2
	}
	return ""
}

func (x *AdminPackageResponse) GetExtra3() int32 {
	if x != nil {
		return x.Extra3
	}
	return 0
}

func (x *AdminPackageResponse) GetExtra4() int32 {
	if x != nil {
		return x.Extra4
	}
	return 0
}

func (x *AdminPackageResponse) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *AdminPackageResponse) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

// 流量包过滤条件
type PackageFilter struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Currency      *string                `protobuf:"bytes,1,opt,name=currency,proto3,oneof" json:"currency,omitempty"` // 过滤：货币单位
	Country       *string                `protobuf:"bytes,2,opt,name=country,proto3,oneof" json:"country,omitempty"`   // 过滤：国家
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PackageFilter) Reset() {
	*x = PackageFilter{}
	mi := &file_protos_store_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PackageFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PackageFilter) ProtoMessage() {}

func (x *PackageFilter) ProtoReflect() protoreflect.Message {
	mi := &file_protos_store_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PackageFilter.ProtoReflect.Descriptor instead.
func (*PackageFilter) Descriptor() ([]byte, []int) {
	return file_protos_store_proto_rawDescGZIP(), []int{3}
}

func (x *PackageFilter) GetCurrency() string {
	if x != nil && x.Currency != nil {
		return *x.Currency
	}
	return ""
}

func (x *PackageFilter) GetCountry() string {
	if x != nil && x.Country != nil {
		return *x.Country
	}
	return ""
}

// 分页请求
type PaginationRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Limit         int32                  `protobuf:"varint,1,opt,name=limit,proto3" json:"limit,omitempty"`   // 每页数量
	Offset        int32                  `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"` // 偏移量
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PaginationRequest) Reset() {
	*x = PaginationRequest{}
	mi := &file_protos_store_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PaginationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaginationRequest) ProtoMessage() {}

func (x *PaginationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_protos_store_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaginationRequest.ProtoReflect.Descriptor instead.
func (*PaginationRequest) Descriptor() ([]byte, []int) {
	return file_protos_store_proto_rawDescGZIP(), []int{4}
}

func (x *PaginationRequest) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *PaginationRequest) GetOffset() int32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

// 分页响应
type PaginationResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Total         int64                  `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`         // 总记录数
	Limit         int32                  `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`         // 当前页大小
	Offset        int32                  `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`       // 当前偏移量
	Remaining     int64                  `protobuf:"varint,4,opt,name=remaining,proto3" json:"remaining,omitempty"` // 剩余记录数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PaginationResponse) Reset() {
	*x = PaginationResponse{}
	mi := &file_protos_store_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PaginationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaginationResponse) ProtoMessage() {}

func (x *PaginationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_protos_store_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaginationResponse.ProtoReflect.Descriptor instead.
func (*PaginationResponse) Descriptor() ([]byte, []int) {
	return file_protos_store_proto_rawDescGZIP(), []int{5}
}

func (x *PaginationResponse) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *PaginationResponse) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *PaginationResponse) GetOffset() int32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *PaginationResponse) GetRemaining() int64 {
	if x != nil {
		return x.Remaining
	}
	return 0
}

// 用户上下文
type UserContext struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"` // 用户ID
	Role          string                 `protobuf:"bytes,2,opt,name=role,proto3" json:"role,omitempty"`                   // 用户角色
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserContext) Reset() {
	*x = UserContext{}
	mi := &file_protos_store_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserContext) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserContext) ProtoMessage() {}

func (x *UserContext) ProtoReflect() protoreflect.Message {
	mi := &file_protos_store_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserContext.ProtoReflect.Descriptor instead.
func (*UserContext) Descriptor() ([]byte, []int) {
	return file_protos_store_proto_rawDescGZIP(), []int{6}
}

func (x *UserContext) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UserContext) GetRole() string {
	if x != nil {
		return x.Role
	}
	return ""
}

// 获取所有流量包请求（终端用户）
type ListAllPackagesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserContext   *UserContext           `protobuf:"bytes,1,opt,name=user_context,json=userContext,proto3" json:"user_context,omitempty"` // 用户上下文
	Filter        *PackageFilter         `protobuf:"bytes,2,opt,name=filter,proto3,oneof" json:"filter,omitempty"`                        // 过滤条件
	Pagination    *PaginationRequest     `protobuf:"bytes,3,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`                // 分页参数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAllPackagesRequest) Reset() {
	*x = ListAllPackagesRequest{}
	mi := &file_protos_store_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAllPackagesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAllPackagesRequest) ProtoMessage() {}

func (x *ListAllPackagesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_protos_store_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAllPackagesRequest.ProtoReflect.Descriptor instead.
func (*ListAllPackagesRequest) Descriptor() ([]byte, []int) {
	return file_protos_store_proto_rawDescGZIP(), []int{7}
}

func (x *ListAllPackagesRequest) GetUserContext() *UserContext {
	if x != nil {
		return x.UserContext
	}
	return nil
}

func (x *ListAllPackagesRequest) GetFilter() *PackageFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListAllPackagesRequest) GetPagination() *PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// 获取所有流量包响应（终端用户）
type ListAllPackagesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Packages      []*PackageResponse     `protobuf:"bytes,1,rep,name=packages,proto3" json:"packages,omitempty"`     // 流量包列表
	Pagination    *PaginationResponse    `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"` // 分页响应信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAllPackagesResponse) Reset() {
	*x = ListAllPackagesResponse{}
	mi := &file_protos_store_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAllPackagesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAllPackagesResponse) ProtoMessage() {}

func (x *ListAllPackagesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_protos_store_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAllPackagesResponse.ProtoReflect.Descriptor instead.
func (*ListAllPackagesResponse) Descriptor() ([]byte, []int) {
	return file_protos_store_proto_rawDescGZIP(), []int{8}
}

func (x *ListAllPackagesResponse) GetPackages() []*PackageResponse {
	if x != nil {
		return x.Packages
	}
	return nil
}

func (x *ListAllPackagesResponse) GetPagination() *PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// 创建流量包请求
type CreatePackageRequest struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	PackageName       string                 `protobuf:"bytes,1,opt,name=package_name,json=packageName,proto3" json:"package_name,omitempty"`                           // 流量包名称
	PackageDesc       string                 `protobuf:"bytes,2,opt,name=package_desc,json=packageDesc,proto3" json:"package_desc,omitempty"`                           // 流量包描述
	Entitlement       int32                  `protobuf:"varint,3,opt,name=entitlement,proto3" json:"entitlement,omitempty"`                                             // 购买后获得的权益
	EntitlementDesc   string                 `protobuf:"bytes,4,opt,name=entitlement_desc,json=entitlementDesc,proto3" json:"entitlement_desc,omitempty"`               // 购买后获得的权益描述
	OriginalPrice     float64                `protobuf:"fixed64,5,opt,name=original_price,json=originalPrice,proto3" json:"original_price,omitempty"`                   // 流量包原价
	DiscountPrice     *float64               `protobuf:"fixed64,6,opt,name=discount_price,json=discountPrice,proto3,oneof" json:"discount_price,omitempty"`             // 流量包优惠价
	DiscountStartTime *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=discount_start_time,json=discountStartTime,proto3,oneof" json:"discount_start_time,omitempty"` // 优惠开始时间
	DiscountEndTime   *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=discount_end_time,json=discountEndTime,proto3,oneof" json:"discount_end_time,omitempty"`       // 优惠结束时间
	DiscountDesc      string                 `protobuf:"bytes,9,opt,name=discount_desc,json=discountDesc,proto3" json:"discount_desc,omitempty"`                        // 优惠活动描述
	SaleStatus        string                 `protobuf:"bytes,10,opt,name=sale_status,json=saleStatus,proto3" json:"sale_status,omitempty"`                             // 出售状态
	Currency          string                 `protobuf:"bytes,11,opt,name=currency,proto3" json:"currency,omitempty"`                                                   // 货币单位
	Country           string                 `protobuf:"bytes,12,opt,name=country,proto3" json:"country,omitempty"`                                                     // 国家
	Extra1            string                 `protobuf:"bytes,13,opt,name=extra1,proto3" json:"extra1,omitempty"`                                                       // 附件字段1
	Extra2            string                 `protobuf:"bytes,14,opt,name=extra2,proto3" json:"extra2,omitempty"`                                                       // 附件字段2
	Extra3            int32                  `protobuf:"varint,15,opt,name=extra3,proto3" json:"extra3,omitempty"`                                                      // 附件字段3
	Extra4            int32                  `protobuf:"varint,16,opt,name=extra4,proto3" json:"extra4,omitempty"`                                                      // 附件字段4
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *CreatePackageRequest) Reset() {
	*x = CreatePackageRequest{}
	mi := &file_protos_store_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreatePackageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePackageRequest) ProtoMessage() {}

func (x *CreatePackageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_protos_store_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePackageRequest.ProtoReflect.Descriptor instead.
func (*CreatePackageRequest) Descriptor() ([]byte, []int) {
	return file_protos_store_proto_rawDescGZIP(), []int{9}
}

func (x *CreatePackageRequest) GetPackageName() string {
	if x != nil {
		return x.PackageName
	}
	return ""
}

func (x *CreatePackageRequest) GetPackageDesc() string {
	if x != nil {
		return x.PackageDesc
	}
	return ""
}

func (x *CreatePackageRequest) GetEntitlement() int32 {
	if x != nil {
		return x.Entitlement
	}
	return 0
}

func (x *CreatePackageRequest) GetEntitlementDesc() string {
	if x != nil {
		return x.EntitlementDesc
	}
	return ""
}

func (x *CreatePackageRequest) GetOriginalPrice() float64 {
	if x != nil {
		return x.OriginalPrice
	}
	return 0
}

func (x *CreatePackageRequest) GetDiscountPrice() float64 {
	if x != nil && x.DiscountPrice != nil {
		return *x.DiscountPrice
	}
	return 0
}

func (x *CreatePackageRequest) GetDiscountStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DiscountStartTime
	}
	return nil
}

func (x *CreatePackageRequest) GetDiscountEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DiscountEndTime
	}
	return nil
}

func (x *CreatePackageRequest) GetDiscountDesc() string {
	if x != nil {
		return x.DiscountDesc
	}
	return ""
}

func (x *CreatePackageRequest) GetSaleStatus() string {
	if x != nil {
		return x.SaleStatus
	}
	return ""
}

func (x *CreatePackageRequest) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *CreatePackageRequest) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *CreatePackageRequest) GetExtra1() string {
	if x != nil {
		return x.Extra1
	}
	return ""
}

func (x *CreatePackageRequest) GetExtra2() string {
	if x != nil {
		return x.Extra2
	}
	return ""
}

func (x *CreatePackageRequest) GetExtra3() int32 {
	if x != nil {
		return x.Extra3
	}
	return 0
}

func (x *CreatePackageRequest) GetExtra4() int32 {
	if x != nil {
		return x.Extra4
	}
	return 0
}

// 创建流量包响应
type CreatePackageResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Message       string                 `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"` // 响应消息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreatePackageResponse) Reset() {
	*x = CreatePackageResponse{}
	mi := &file_protos_store_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreatePackageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePackageResponse) ProtoMessage() {}

func (x *CreatePackageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_protos_store_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePackageResponse.ProtoReflect.Descriptor instead.
func (*CreatePackageResponse) Descriptor() ([]byte, []int) {
	return file_protos_store_proto_rawDescGZIP(), []int{10}
}

func (x *CreatePackageResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 更新流量包请求
type UpdatePackageRequest struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	PackageId         string                 `protobuf:"bytes,1,opt,name=package_id,json=packageId,proto3" json:"package_id,omitempty"`                                 // 流量包ID
	PackageName       *string                `protobuf:"bytes,2,opt,name=package_name,json=packageName,proto3,oneof" json:"package_name,omitempty"`                     // 流量包名称
	PackageDesc       *string                `protobuf:"bytes,3,opt,name=package_desc,json=packageDesc,proto3,oneof" json:"package_desc,omitempty"`                     // 流量包描述
	Entitlement       *int32                 `protobuf:"varint,4,opt,name=entitlement,proto3,oneof" json:"entitlement,omitempty"`                                       // 购买后获得的权益
	EntitlementDesc   *string                `protobuf:"bytes,5,opt,name=entitlement_desc,json=entitlementDesc,proto3,oneof" json:"entitlement_desc,omitempty"`         // 购买后获得的权益描述
	OriginalPrice     *float64               `protobuf:"fixed64,6,opt,name=original_price,json=originalPrice,proto3,oneof" json:"original_price,omitempty"`             // 流量包原价
	DiscountPrice     *float64               `protobuf:"fixed64,7,opt,name=discount_price,json=discountPrice,proto3,oneof" json:"discount_price,omitempty"`             // 流量包优惠价
	DiscountStartTime *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=discount_start_time,json=discountStartTime,proto3,oneof" json:"discount_start_time,omitempty"` // 优惠开始时间
	DiscountEndTime   *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=discount_end_time,json=discountEndTime,proto3,oneof" json:"discount_end_time,omitempty"`       // 优惠结束时间
	DiscountDesc      *string                `protobuf:"bytes,10,opt,name=discount_desc,json=discountDesc,proto3,oneof" json:"discount_desc,omitempty"`                 // 优惠活动描述
	SaleStatus        *string                `protobuf:"bytes,11,opt,name=sale_status,json=saleStatus,proto3,oneof" json:"sale_status,omitempty"`                       // 出售状态 on_sale(起售), off_sale(停售)
	Currency          *string                `protobuf:"bytes,12,opt,name=currency,proto3,oneof" json:"currency,omitempty"`                                             // 货币单位
	Country           *string                `protobuf:"bytes,13,opt,name=country,proto3,oneof" json:"country,omitempty"`                                               // 国家
	Extra1            *string                `protobuf:"bytes,14,opt,name=extra1,proto3,oneof" json:"extra1,omitempty"`                                                 // 附件字段1. 暂未使用.
	Extra2            *string                `protobuf:"bytes,15,opt,name=extra2,proto3,oneof" json:"extra2,omitempty"`                                                 // 附件字段2. 暂未使用.
	Extra3            *int32                 `protobuf:"varint,16,opt,name=extra3,proto3,oneof" json:"extra3,omitempty"`                                                // 附件字段3. 暂未使用.
	Extra4            *int32                 `protobuf:"varint,17,opt,name=extra4,proto3,oneof" json:"extra4,omitempty"`                                                // 附件字段4. 暂未使用.
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *UpdatePackageRequest) Reset() {
	*x = UpdatePackageRequest{}
	mi := &file_protos_store_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdatePackageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePackageRequest) ProtoMessage() {}

func (x *UpdatePackageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_protos_store_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePackageRequest.ProtoReflect.Descriptor instead.
func (*UpdatePackageRequest) Descriptor() ([]byte, []int) {
	return file_protos_store_proto_rawDescGZIP(), []int{11}
}

func (x *UpdatePackageRequest) GetPackageId() string {
	if x != nil {
		return x.PackageId
	}
	return ""
}

func (x *UpdatePackageRequest) GetPackageName() string {
	if x != nil && x.PackageName != nil {
		return *x.PackageName
	}
	return ""
}

func (x *UpdatePackageRequest) GetPackageDesc() string {
	if x != nil && x.PackageDesc != nil {
		return *x.PackageDesc
	}
	return ""
}

func (x *UpdatePackageRequest) GetEntitlement() int32 {
	if x != nil && x.Entitlement != nil {
		return *x.Entitlement
	}
	return 0
}

func (x *UpdatePackageRequest) GetEntitlementDesc() string {
	if x != nil && x.EntitlementDesc != nil {
		return *x.EntitlementDesc
	}
	return ""
}

func (x *UpdatePackageRequest) GetOriginalPrice() float64 {
	if x != nil && x.OriginalPrice != nil {
		return *x.OriginalPrice
	}
	return 0
}

func (x *UpdatePackageRequest) GetDiscountPrice() float64 {
	if x != nil && x.DiscountPrice != nil {
		return *x.DiscountPrice
	}
	return 0
}

func (x *UpdatePackageRequest) GetDiscountStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DiscountStartTime
	}
	return nil
}

func (x *UpdatePackageRequest) GetDiscountEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DiscountEndTime
	}
	return nil
}

func (x *UpdatePackageRequest) GetDiscountDesc() string {
	if x != nil && x.DiscountDesc != nil {
		return *x.DiscountDesc
	}
	return ""
}

func (x *UpdatePackageRequest) GetSaleStatus() string {
	if x != nil && x.SaleStatus != nil {
		return *x.SaleStatus
	}
	return ""
}

func (x *UpdatePackageRequest) GetCurrency() string {
	if x != nil && x.Currency != nil {
		return *x.Currency
	}
	return ""
}

func (x *UpdatePackageRequest) GetCountry() string {
	if x != nil && x.Country != nil {
		return *x.Country
	}
	return ""
}

func (x *UpdatePackageRequest) GetExtra1() string {
	if x != nil && x.Extra1 != nil {
		return *x.Extra1
	}
	return ""
}

func (x *UpdatePackageRequest) GetExtra2() string {
	if x != nil && x.Extra2 != nil {
		return *x.Extra2
	}
	return ""
}

func (x *UpdatePackageRequest) GetExtra3() int32 {
	if x != nil && x.Extra3 != nil {
		return *x.Extra3
	}
	return 0
}

func (x *UpdatePackageRequest) GetExtra4() int32 {
	if x != nil && x.Extra4 != nil {
		return *x.Extra4
	}
	return 0
}

// 更新流量包响应
type UpdatePackageResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Message       string                 `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"` // 响应消息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdatePackageResponse) Reset() {
	*x = UpdatePackageResponse{}
	mi := &file_protos_store_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdatePackageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePackageResponse) ProtoMessage() {}

func (x *UpdatePackageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_protos_store_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePackageResponse.ProtoReflect.Descriptor instead.
func (*UpdatePackageResponse) Descriptor() ([]byte, []int) {
	return file_protos_store_proto_rawDescGZIP(), []int{12}
}

func (x *UpdatePackageResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 删除流量包请求
type DeletePackageRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PackageId     string                 `protobuf:"bytes,1,opt,name=package_id,json=packageId,proto3" json:"package_id,omitempty"` // 流量包ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeletePackageRequest) Reset() {
	*x = DeletePackageRequest{}
	mi := &file_protos_store_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeletePackageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePackageRequest) ProtoMessage() {}

func (x *DeletePackageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_protos_store_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePackageRequest.ProtoReflect.Descriptor instead.
func (*DeletePackageRequest) Descriptor() ([]byte, []int) {
	return file_protos_store_proto_rawDescGZIP(), []int{13}
}

func (x *DeletePackageRequest) GetPackageId() string {
	if x != nil {
		return x.PackageId
	}
	return ""
}

// 删除流量包响应
type DeletePackageResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Message       string                 `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"` // 响应消息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeletePackageResponse) Reset() {
	*x = DeletePackageResponse{}
	mi := &file_protos_store_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeletePackageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePackageResponse) ProtoMessage() {}

func (x *DeletePackageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_protos_store_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePackageResponse.ProtoReflect.Descriptor instead.
func (*DeletePackageResponse) Descriptor() ([]byte, []int) {
	return file_protos_store_proto_rawDescGZIP(), []int{14}
}

func (x *DeletePackageResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 获取所有流量包请求（管理员）
type AdminListAllPackagesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Filter        *PackageFilter         `protobuf:"bytes,1,opt,name=filter,proto3,oneof" json:"filter,omitempty"`         // 过滤条件
	Pagination    *PaginationRequest     `protobuf:"bytes,2,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"` // 分页参数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AdminListAllPackagesRequest) Reset() {
	*x = AdminListAllPackagesRequest{}
	mi := &file_protos_store_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AdminListAllPackagesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdminListAllPackagesRequest) ProtoMessage() {}

func (x *AdminListAllPackagesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_protos_store_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdminListAllPackagesRequest.ProtoReflect.Descriptor instead.
func (*AdminListAllPackagesRequest) Descriptor() ([]byte, []int) {
	return file_protos_store_proto_rawDescGZIP(), []int{15}
}

func (x *AdminListAllPackagesRequest) GetFilter() *PackageFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *AdminListAllPackagesRequest) GetPagination() *PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// 获取所有流量包响应（管理员）
type AdminListAllPackagesResponse struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Packages      []*AdminPackageResponse `protobuf:"bytes,1,rep,name=packages,proto3" json:"packages,omitempty"`     // 流量包列表
	Pagination    *PaginationResponse     `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"` // 分页响应信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AdminListAllPackagesResponse) Reset() {
	*x = AdminListAllPackagesResponse{}
	mi := &file_protos_store_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AdminListAllPackagesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdminListAllPackagesResponse) ProtoMessage() {}

func (x *AdminListAllPackagesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_protos_store_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdminListAllPackagesResponse.ProtoReflect.Descriptor instead.
func (*AdminListAllPackagesResponse) Descriptor() ([]byte, []int) {
	return file_protos_store_proto_rawDescGZIP(), []int{16}
}

func (x *AdminListAllPackagesResponse) GetPackages() []*AdminPackageResponse {
	if x != nil {
		return x.Packages
	}
	return nil
}

func (x *AdminListAllPackagesResponse) GetPagination() *PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

var File_protos_store_proto protoreflect.FileDescriptor

const file_protos_store_proto_rawDesc = "" +
	"\n" +
	"\x12protos/store.proto\x12\x17com.aibook.storepb.grpc\x1a\x1fgoogle/protobuf/timestamp.proto\"\xb8\a\n" +
	"\aPackage\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12\x1d\n" +
	"\n" +
	"package_id\x18\x02 \x01(\tR\tpackageId\x12!\n" +
	"\fpackage_name\x18\x03 \x01(\tR\vpackageName\x12!\n" +
	"\fpackage_desc\x18\x04 \x01(\tR\vpackageDesc\x12 \n" +
	"\ventitlement\x18\x05 \x01(\x05R\ventitlement\x12)\n" +
	"\x10entitlement_desc\x18\x06 \x01(\tR\x0fentitlementDesc\x12%\n" +
	"\x0eoriginal_price\x18\a \x01(\x01R\roriginalPrice\x12*\n" +
	"\x0ediscount_price\x18\b \x01(\x01H\x00R\rdiscountPrice\x88\x01\x01\x12O\n" +
	"\x13discount_start_time\x18\t \x01(\v2\x1a.google.protobuf.TimestampH\x01R\x11discountStartTime\x88\x01\x01\x12K\n" +
	"\x11discount_end_time\x18\n" +
	" \x01(\v2\x1a.google.protobuf.TimestampH\x02R\x0fdiscountEndTime\x88\x01\x01\x12#\n" +
	"\rdiscount_desc\x18\v \x01(\tR\fdiscountDesc\x12\x1f\n" +
	"\vsale_status\x18\f \x01(\tR\n" +
	"saleStatus\x12\x1a\n" +
	"\bcurrency\x18\r \x01(\tR\bcurrency\x12\x18\n" +
	"\acountry\x18\x0e \x01(\tR\acountry\x12\x16\n" +
	"\x06extra1\x18\x0f \x01(\tR\x06extra1\x12\x16\n" +
	"\x06extra2\x18\x10 \x01(\tR\x06extra2\x12\x16\n" +
	"\x06extra3\x18\x11 \x01(\x05R\x06extra3\x12\x16\n" +
	"\x06extra4\x18\x12 \x01(\x05R\x06extra4\x129\n" +
	"\n" +
	"created_at\x18\x13 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\x14 \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\x12\x18\n" +
	"\adeleted\x18\x15 \x01(\bR\adeleted\x12>\n" +
	"\n" +
	"deleted_at\x18\x16 \x01(\v2\x1a.google.protobuf.TimestampH\x03R\tdeletedAt\x88\x01\x01B\x11\n" +
	"\x0f_discount_priceB\x16\n" +
	"\x14_discount_start_timeB\x14\n" +
	"\x12_discount_end_timeB\r\n" +
	"\v_deleted_at\"\xe0\x04\n" +
	"\x0fPackageResponse\x12\x1d\n" +
	"\n" +
	"package_id\x18\x01 \x01(\tR\tpackageId\x12!\n" +
	"\fpackage_name\x18\x02 \x01(\tR\vpackageName\x12!\n" +
	"\fpackage_desc\x18\x03 \x01(\tR\vpackageDesc\x12 \n" +
	"\ventitlement\x18\x04 \x01(\x05R\ventitlement\x12)\n" +
	"\x10entitlement_desc\x18\x05 \x01(\tR\x0fentitlementDesc\x12\x14\n" +
	"\x05price\x18\x06 \x01(\x01R\x05price\x12*\n" +
	"\x0eoriginal_price\x18\a \x01(\x01H\x00R\roriginalPrice\x88\x01\x01\x12O\n" +
	"\x13discount_start_time\x18\b \x01(\v2\x1a.google.protobuf.TimestampH\x01R\x11discountStartTime\x88\x01\x01\x12K\n" +
	"\x11discount_end_time\x18\t \x01(\v2\x1a.google.protobuf.TimestampH\x02R\x0fdiscountEndTime\x88\x01\x01\x12#\n" +
	"\rdiscount_desc\x18\n" +
	" \x01(\tR\fdiscountDesc\x12\x1f\n" +
	"\vsale_status\x18\v \x01(\tR\n" +
	"saleStatus\x12\x1a\n" +
	"\bcurrency\x18\f \x01(\tR\bcurrency\x12\x18\n" +
	"\acountry\x18\r \x01(\tR\acountryB\x11\n" +
	"\x0f_original_priceB\x16\n" +
	"\x14_discount_start_timeB\x14\n" +
	"\x12_discount_end_time\"\xdc\x06\n" +
	"\x14AdminPackageResponse\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12\x1d\n" +
	"\n" +
	"package_id\x18\x02 \x01(\tR\tpackageId\x12!\n" +
	"\fpackage_name\x18\x03 \x01(\tR\vpackageName\x12!\n" +
	"\fpackage_desc\x18\x04 \x01(\tR\vpackageDesc\x12 \n" +
	"\ventitlement\x18\x05 \x01(\x05R\ventitlement\x12)\n" +
	"\x10entitlement_desc\x18\x06 \x01(\tR\x0fentitlementDesc\x12%\n" +
	"\x0eoriginal_price\x18\a \x01(\x01R\roriginalPrice\x12*\n" +
	"\x0ediscount_price\x18\b \x01(\x01H\x00R\rdiscountPrice\x88\x01\x01\x12O\n" +
	"\x13discount_start_time\x18\t \x01(\v2\x1a.google.protobuf.TimestampH\x01R\x11discountStartTime\x88\x01\x01\x12K\n" +
	"\x11discount_end_time\x18\n" +
	" \x01(\v2\x1a.google.protobuf.TimestampH\x02R\x0fdiscountEndTime\x88\x01\x01\x12#\n" +
	"\rdiscount_desc\x18\v \x01(\tR\fdiscountDesc\x12\x1f\n" +
	"\vsale_status\x18\f \x01(\tR\n" +
	"saleStatus\x12\x1a\n" +
	"\bcurrency\x18\r \x01(\tR\bcurrency\x12\x18\n" +
	"\acountry\x18\x0e \x01(\tR\acountry\x12\x16\n" +
	"\x06extra1\x18\x0f \x01(\tR\x06extra1\x12\x16\n" +
	"\x06extra2\x18\x10 \x01(\tR\x06extra2\x12\x16\n" +
	"\x06extra3\x18\x11 \x01(\x05R\x06extra3\x12\x16\n" +
	"\x06extra4\x18\x12 \x01(\x05R\x06extra4\x129\n" +
	"\n" +
	"created_at\x18\x13 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\x14 \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAtB\x11\n" +
	"\x0f_discount_priceB\x16\n" +
	"\x14_discount_start_timeB\x14\n" +
	"\x12_discount_end_time\"h\n" +
	"\rPackageFilter\x12\x1f\n" +
	"\bcurrency\x18\x01 \x01(\tH\x00R\bcurrency\x88\x01\x01\x12\x1d\n" +
	"\acountry\x18\x02 \x01(\tH\x01R\acountry\x88\x01\x01B\v\n" +
	"\t_currencyB\n" +
	"\n" +
	"\b_country\"A\n" +
	"\x11PaginationRequest\x12\x14\n" +
	"\x05limit\x18\x01 \x01(\x05R\x05limit\x12\x16\n" +
	"\x06offset\x18\x02 \x01(\x05R\x06offset\"v\n" +
	"\x12PaginationResponse\x12\x14\n" +
	"\x05total\x18\x01 \x01(\x03R\x05total\x12\x14\n" +
	"\x05limit\x18\x02 \x01(\x05R\x05limit\x12\x16\n" +
	"\x06offset\x18\x03 \x01(\x05R\x06offset\x12\x1c\n" +
	"\tremaining\x18\x04 \x01(\x03R\tremaining\":\n" +
	"\vUserContext\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x12\n" +
	"\x04role\x18\x02 \x01(\tR\x04role\"\x91\x02\n" +
	"\x16ListAllPackagesRequest\x12G\n" +
	"\fuser_context\x18\x01 \x01(\v2$.com.aibook.storepb.grpc.UserContextR\vuserContext\x12C\n" +
	"\x06filter\x18\x02 \x01(\v2&.com.aibook.storepb.grpc.PackageFilterH\x00R\x06filter\x88\x01\x01\x12O\n" +
	"\n" +
	"pagination\x18\x03 \x01(\v2*.com.aibook.storepb.grpc.PaginationRequestH\x01R\n" +
	"pagination\x88\x01\x01B\t\n" +
	"\a_filterB\r\n" +
	"\v_pagination\"\xac\x01\n" +
	"\x17ListAllPackagesResponse\x12D\n" +
	"\bpackages\x18\x01 \x03(\v2(.com.aibook.storepb.grpc.PackageResponseR\bpackages\x12K\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2+.com.aibook.storepb.grpc.PaginationResponseR\n" +
	"pagination\"\xb7\x05\n" +
	"\x14CreatePackageRequest\x12!\n" +
	"\fpackage_name\x18\x01 \x01(\tR\vpackageName\x12!\n" +
	"\fpackage_desc\x18\x02 \x01(\tR\vpackageDesc\x12 \n" +
	"\ventitlement\x18\x03 \x01(\x05R\ventitlement\x12)\n" +
	"\x10entitlement_desc\x18\x04 \x01(\tR\x0fentitlementDesc\x12%\n" +
	"\x0eoriginal_price\x18\x05 \x01(\x01R\roriginalPrice\x12*\n" +
	"\x0ediscount_price\x18\x06 \x01(\x01H\x00R\rdiscountPrice\x88\x01\x01\x12O\n" +
	"\x13discount_start_time\x18\a \x01(\v2\x1a.google.protobuf.TimestampH\x01R\x11discountStartTime\x88\x01\x01\x12K\n" +
	"\x11discount_end_time\x18\b \x01(\v2\x1a.google.protobuf.TimestampH\x02R\x0fdiscountEndTime\x88\x01\x01\x12#\n" +
	"\rdiscount_desc\x18\t \x01(\tR\fdiscountDesc\x12\x1f\n" +
	"\vsale_status\x18\n" +
	" \x01(\tR\n" +
	"saleStatus\x12\x1a\n" +
	"\bcurrency\x18\v \x01(\tR\bcurrency\x12\x18\n" +
	"\acountry\x18\f \x01(\tR\acountry\x12\x16\n" +
	"\x06extra1\x18\r \x01(\tR\x06extra1\x12\x16\n" +
	"\x06extra2\x18\x0e \x01(\tR\x06extra2\x12\x16\n" +
	"\x06extra3\x18\x0f \x01(\x05R\x06extra3\x12\x16\n" +
	"\x06extra4\x18\x10 \x01(\x05R\x06extra4B\x11\n" +
	"\x0f_discount_priceB\x16\n" +
	"\x14_discount_start_timeB\x14\n" +
	"\x12_discount_end_time\"1\n" +
	"\x15CreatePackageResponse\x12\x18\n" +
	"\amessage\x18\x01 \x01(\tR\amessage\"\xd8\a\n" +
	"\x14UpdatePackageRequest\x12\x1d\n" +
	"\n" +
	"package_id\x18\x01 \x01(\tR\tpackageId\x12&\n" +
	"\fpackage_name\x18\x02 \x01(\tH\x00R\vpackageName\x88\x01\x01\x12&\n" +
	"\fpackage_desc\x18\x03 \x01(\tH\x01R\vpackageDesc\x88\x01\x01\x12%\n" +
	"\ventitlement\x18\x04 \x01(\x05H\x02R\ventitlement\x88\x01\x01\x12.\n" +
	"\x10entitlement_desc\x18\x05 \x01(\tH\x03R\x0fentitlementDesc\x88\x01\x01\x12*\n" +
	"\x0eoriginal_price\x18\x06 \x01(\x01H\x04R\roriginalPrice\x88\x01\x01\x12*\n" +
	"\x0ediscount_price\x18\a \x01(\x01H\x05R\rdiscountPrice\x88\x01\x01\x12O\n" +
	"\x13discount_start_time\x18\b \x01(\v2\x1a.google.protobuf.TimestampH\x06R\x11discountStartTime\x88\x01\x01\x12K\n" +
	"\x11discount_end_time\x18\t \x01(\v2\x1a.google.protobuf.TimestampH\aR\x0fdiscountEndTime\x88\x01\x01\x12(\n" +
	"\rdiscount_desc\x18\n" +
	" \x01(\tH\bR\fdiscountDesc\x88\x01\x01\x12$\n" +
	"\vsale_status\x18\v \x01(\tH\tR\n" +
	"saleStatus\x88\x01\x01\x12\x1f\n" +
	"\bcurrency\x18\f \x01(\tH\n" +
	"R\bcurrency\x88\x01\x01\x12\x1d\n" +
	"\acountry\x18\r \x01(\tH\vR\acountry\x88\x01\x01\x12\x1b\n" +
	"\x06extra1\x18\x0e \x01(\tH\fR\x06extra1\x88\x01\x01\x12\x1b\n" +
	"\x06extra2\x18\x0f \x01(\tH\rR\x06extra2\x88\x01\x01\x12\x1b\n" +
	"\x06extra3\x18\x10 \x01(\x05H\x0eR\x06extra3\x88\x01\x01\x12\x1b\n" +
	"\x06extra4\x18\x11 \x01(\x05H\x0fR\x06extra4\x88\x01\x01B\x0f\n" +
	"\r_package_nameB\x0f\n" +
	"\r_package_descB\x0e\n" +
	"\f_entitlementB\x13\n" +
	"\x11_entitlement_descB\x11\n" +
	"\x0f_original_priceB\x11\n" +
	"\x0f_discount_priceB\x16\n" +
	"\x14_discount_start_timeB\x14\n" +
	"\x12_discount_end_timeB\x10\n" +
	"\x0e_discount_descB\x0e\n" +
	"\f_sale_statusB\v\n" +
	"\t_currencyB\n" +
	"\n" +
	"\b_countryB\t\n" +
	"\a_extra1B\t\n" +
	"\a_extra2B\t\n" +
	"\a_extra3B\t\n" +
	"\a_extra4\"1\n" +
	"\x15UpdatePackageResponse\x12\x18\n" +
	"\amessage\x18\x01 \x01(\tR\amessage\"5\n" +
	"\x14DeletePackageRequest\x12\x1d\n" +
	"\n" +
	"package_id\x18\x01 \x01(\tR\tpackageId\"1\n" +
	"\x15DeletePackageResponse\x12\x18\n" +
	"\amessage\x18\x01 \x01(\tR\amessage\"\xcd\x01\n" +
	"\x1bAdminListAllPackagesRequest\x12C\n" +
	"\x06filter\x18\x01 \x01(\v2&.com.aibook.storepb.grpc.PackageFilterH\x00R\x06filter\x88\x01\x01\x12O\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2*.com.aibook.storepb.grpc.PaginationRequestH\x01R\n" +
	"pagination\x88\x01\x01B\t\n" +
	"\a_filterB\r\n" +
	"\v_pagination\"\xb6\x01\n" +
	"\x1cAdminListAllPackagesResponse\x12I\n" +
	"\bpackages\x18\x01 \x03(\v2-.com.aibook.storepb.grpc.AdminPackageResponseR\bpackages\x12K\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2+.com.aibook.storepb.grpc.PaginationResponseR\n" +
	"pagination2\xe9\x04\n" +
	"\fStoreService\x12t\n" +
	"\x0fListAllPackages\x12/.com.aibook.storepb.grpc.ListAllPackagesRequest\x1a0.com.aibook.storepb.grpc.ListAllPackagesResponse\x12q\n" +
	"\x10AdminAddPackages\x12-.com.aibook.storepb.grpc.CreatePackageRequest\x1a..com.aibook.storepb.grpc.CreatePackageResponse\x12t\n" +
	"\x13AdminDeletePackages\x12-.com.aibook.storepb.grpc.DeletePackageRequest\x1a..com.aibook.storepb.grpc.DeletePackageResponse\x12t\n" +
	"\x13AdminUpdatePackages\x12-.com.aibook.storepb.grpc.UpdatePackageRequest\x1a..com.aibook.storepb.grpc.UpdatePackageResponse\x12\x83\x01\n" +
	"\x14AdminListAllPackages\x124.com.aibook.storepb.grpc.AdminListAllPackagesRequest\x1a5.com.aibook.storepb.grpc.AdminListAllPackagesResponseB\x15P\x01Z\x11./storepb;storepbb\x06proto3"

var (
	file_protos_store_proto_rawDescOnce sync.Once
	file_protos_store_proto_rawDescData []byte
)

func file_protos_store_proto_rawDescGZIP() []byte {
	file_protos_store_proto_rawDescOnce.Do(func() {
		file_protos_store_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_protos_store_proto_rawDesc), len(file_protos_store_proto_rawDesc)))
	})
	return file_protos_store_proto_rawDescData
}

var file_protos_store_proto_msgTypes = make([]protoimpl.MessageInfo, 17)
var file_protos_store_proto_goTypes = []any{
	(*Package)(nil),                      // 0: com.aibook.storepb.grpc.Package
	(*PackageResponse)(nil),              // 1: com.aibook.storepb.grpc.PackageResponse
	(*AdminPackageResponse)(nil),         // 2: com.aibook.storepb.grpc.AdminPackageResponse
	(*PackageFilter)(nil),                // 3: com.aibook.storepb.grpc.PackageFilter
	(*PaginationRequest)(nil),            // 4: com.aibook.storepb.grpc.PaginationRequest
	(*PaginationResponse)(nil),           // 5: com.aibook.storepb.grpc.PaginationResponse
	(*UserContext)(nil),                  // 6: com.aibook.storepb.grpc.UserContext
	(*ListAllPackagesRequest)(nil),       // 7: com.aibook.storepb.grpc.ListAllPackagesRequest
	(*ListAllPackagesResponse)(nil),      // 8: com.aibook.storepb.grpc.ListAllPackagesResponse
	(*CreatePackageRequest)(nil),         // 9: com.aibook.storepb.grpc.CreatePackageRequest
	(*CreatePackageResponse)(nil),        // 10: com.aibook.storepb.grpc.CreatePackageResponse
	(*UpdatePackageRequest)(nil),         // 11: com.aibook.storepb.grpc.UpdatePackageRequest
	(*UpdatePackageResponse)(nil),        // 12: com.aibook.storepb.grpc.UpdatePackageResponse
	(*DeletePackageRequest)(nil),         // 13: com.aibook.storepb.grpc.DeletePackageRequest
	(*DeletePackageResponse)(nil),        // 14: com.aibook.storepb.grpc.DeletePackageResponse
	(*AdminListAllPackagesRequest)(nil),  // 15: com.aibook.storepb.grpc.AdminListAllPackagesRequest
	(*AdminListAllPackagesResponse)(nil), // 16: com.aibook.storepb.grpc.AdminListAllPackagesResponse
	(*timestamppb.Timestamp)(nil),        // 17: google.protobuf.Timestamp
}
var file_protos_store_proto_depIdxs = []int32{
	17, // 0: com.aibook.storepb.grpc.Package.discount_start_time:type_name -> google.protobuf.Timestamp
	17, // 1: com.aibook.storepb.grpc.Package.discount_end_time:type_name -> google.protobuf.Timestamp
	17, // 2: com.aibook.storepb.grpc.Package.created_at:type_name -> google.protobuf.Timestamp
	17, // 3: com.aibook.storepb.grpc.Package.updated_at:type_name -> google.protobuf.Timestamp
	17, // 4: com.aibook.storepb.grpc.Package.deleted_at:type_name -> google.protobuf.Timestamp
	17, // 5: com.aibook.storepb.grpc.PackageResponse.discount_start_time:type_name -> google.protobuf.Timestamp
	17, // 6: com.aibook.storepb.grpc.PackageResponse.discount_end_time:type_name -> google.protobuf.Timestamp
	17, // 7: com.aibook.storepb.grpc.AdminPackageResponse.discount_start_time:type_name -> google.protobuf.Timestamp
	17, // 8: com.aibook.storepb.grpc.AdminPackageResponse.discount_end_time:type_name -> google.protobuf.Timestamp
	17, // 9: com.aibook.storepb.grpc.AdminPackageResponse.created_at:type_name -> google.protobuf.Timestamp
	17, // 10: com.aibook.storepb.grpc.AdminPackageResponse.updated_at:type_name -> google.protobuf.Timestamp
	6,  // 11: com.aibook.storepb.grpc.ListAllPackagesRequest.user_context:type_name -> com.aibook.storepb.grpc.UserContext
	3,  // 12: com.aibook.storepb.grpc.ListAllPackagesRequest.filter:type_name -> com.aibook.storepb.grpc.PackageFilter
	4,  // 13: com.aibook.storepb.grpc.ListAllPackagesRequest.pagination:type_name -> com.aibook.storepb.grpc.PaginationRequest
	1,  // 14: com.aibook.storepb.grpc.ListAllPackagesResponse.packages:type_name -> com.aibook.storepb.grpc.PackageResponse
	5,  // 15: com.aibook.storepb.grpc.ListAllPackagesResponse.pagination:type_name -> com.aibook.storepb.grpc.PaginationResponse
	17, // 16: com.aibook.storepb.grpc.CreatePackageRequest.discount_start_time:type_name -> google.protobuf.Timestamp
	17, // 17: com.aibook.storepb.grpc.CreatePackageRequest.discount_end_time:type_name -> google.protobuf.Timestamp
	17, // 18: com.aibook.storepb.grpc.UpdatePackageRequest.discount_start_time:type_name -> google.protobuf.Timestamp
	17, // 19: com.aibook.storepb.grpc.UpdatePackageRequest.discount_end_time:type_name -> google.protobuf.Timestamp
	3,  // 20: com.aibook.storepb.grpc.AdminListAllPackagesRequest.filter:type_name -> com.aibook.storepb.grpc.PackageFilter
	4,  // 21: com.aibook.storepb.grpc.AdminListAllPackagesRequest.pagination:type_name -> com.aibook.storepb.grpc.PaginationRequest
	2,  // 22: com.aibook.storepb.grpc.AdminListAllPackagesResponse.packages:type_name -> com.aibook.storepb.grpc.AdminPackageResponse
	5,  // 23: com.aibook.storepb.grpc.AdminListAllPackagesResponse.pagination:type_name -> com.aibook.storepb.grpc.PaginationResponse
	7,  // 24: com.aibook.storepb.grpc.StoreService.ListAllPackages:input_type -> com.aibook.storepb.grpc.ListAllPackagesRequest
	9,  // 25: com.aibook.storepb.grpc.StoreService.AdminAddPackages:input_type -> com.aibook.storepb.grpc.CreatePackageRequest
	13, // 26: com.aibook.storepb.grpc.StoreService.AdminDeletePackages:input_type -> com.aibook.storepb.grpc.DeletePackageRequest
	11, // 27: com.aibook.storepb.grpc.StoreService.AdminUpdatePackages:input_type -> com.aibook.storepb.grpc.UpdatePackageRequest
	15, // 28: com.aibook.storepb.grpc.StoreService.AdminListAllPackages:input_type -> com.aibook.storepb.grpc.AdminListAllPackagesRequest
	8,  // 29: com.aibook.storepb.grpc.StoreService.ListAllPackages:output_type -> com.aibook.storepb.grpc.ListAllPackagesResponse
	10, // 30: com.aibook.storepb.grpc.StoreService.AdminAddPackages:output_type -> com.aibook.storepb.grpc.CreatePackageResponse
	14, // 31: com.aibook.storepb.grpc.StoreService.AdminDeletePackages:output_type -> com.aibook.storepb.grpc.DeletePackageResponse
	12, // 32: com.aibook.storepb.grpc.StoreService.AdminUpdatePackages:output_type -> com.aibook.storepb.grpc.UpdatePackageResponse
	16, // 33: com.aibook.storepb.grpc.StoreService.AdminListAllPackages:output_type -> com.aibook.storepb.grpc.AdminListAllPackagesResponse
	29, // [29:34] is the sub-list for method output_type
	24, // [24:29] is the sub-list for method input_type
	24, // [24:24] is the sub-list for extension type_name
	24, // [24:24] is the sub-list for extension extendee
	0,  // [0:24] is the sub-list for field type_name
}

func init() { file_protos_store_proto_init() }
func file_protos_store_proto_init() {
	if File_protos_store_proto != nil {
		return
	}
	file_protos_store_proto_msgTypes[0].OneofWrappers = []any{}
	file_protos_store_proto_msgTypes[1].OneofWrappers = []any{}
	file_protos_store_proto_msgTypes[2].OneofWrappers = []any{}
	file_protos_store_proto_msgTypes[3].OneofWrappers = []any{}
	file_protos_store_proto_msgTypes[7].OneofWrappers = []any{}
	file_protos_store_proto_msgTypes[9].OneofWrappers = []any{}
	file_protos_store_proto_msgTypes[11].OneofWrappers = []any{}
	file_protos_store_proto_msgTypes[15].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_protos_store_proto_rawDesc), len(file_protos_store_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   17,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_protos_store_proto_goTypes,
		DependencyIndexes: file_protos_store_proto_depIdxs,
		MessageInfos:      file_protos_store_proto_msgTypes,
	}.Build()
	File_protos_store_proto = out.File
	file_protos_store_proto_goTypes = nil
	file_protos_store_proto_depIdxs = nil
}
