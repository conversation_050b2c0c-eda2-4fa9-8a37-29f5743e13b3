//*
//后台管理平台基础服务定义

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.31.1
// source: protos/protocol.proto

package admin

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	ProtocolService_GetProtocol_FullMethodName = "/com.aibook.admin.grpc.ProtocolService/getProtocol"
)

// ProtocolServiceClient is the client API for ProtocolService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// ProtocolService 用于按照国家/地区和类型获取协议信息。
type ProtocolServiceClient interface {
	GetProtocol(ctx context.Context, in *ProtocolRequest, opts ...grpc.CallOption) (*ProtocolResponse, error)
}

type protocolServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewProtocolServiceClient(cc grpc.ClientConnInterface) ProtocolServiceClient {
	return &protocolServiceClient{cc}
}

func (c *protocolServiceClient) GetProtocol(ctx context.Context, in *ProtocolRequest, opts ...grpc.CallOption) (*ProtocolResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ProtocolResponse)
	err := c.cc.Invoke(ctx, ProtocolService_GetProtocol_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ProtocolServiceServer is the server API for ProtocolService service.
// All implementations must embed UnimplementedProtocolServiceServer
// for forward compatibility.
//
// ProtocolService 用于按照国家/地区和类型获取协议信息。
type ProtocolServiceServer interface {
	GetProtocol(context.Context, *ProtocolRequest) (*ProtocolResponse, error)
	mustEmbedUnimplementedProtocolServiceServer()
}

// UnimplementedProtocolServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedProtocolServiceServer struct{}

func (UnimplementedProtocolServiceServer) GetProtocol(context.Context, *ProtocolRequest) (*ProtocolResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetProtocol not implemented")
}
func (UnimplementedProtocolServiceServer) mustEmbedUnimplementedProtocolServiceServer() {}
func (UnimplementedProtocolServiceServer) testEmbeddedByValue()                         {}

// UnsafeProtocolServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ProtocolServiceServer will
// result in compilation errors.
type UnsafeProtocolServiceServer interface {
	mustEmbedUnimplementedProtocolServiceServer()
}

func RegisterProtocolServiceServer(s grpc.ServiceRegistrar, srv ProtocolServiceServer) {
	// If the following call pancis, it indicates UnimplementedProtocolServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&ProtocolService_ServiceDesc, srv)
}

func _ProtocolService_GetProtocol_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProtocolRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProtocolServiceServer).GetProtocol(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ProtocolService_GetProtocol_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProtocolServiceServer).GetProtocol(ctx, req.(*ProtocolRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ProtocolService_ServiceDesc is the grpc.ServiceDesc for ProtocolService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ProtocolService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "com.aibook.admin.grpc.ProtocolService",
	HandlerType: (*ProtocolServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "getProtocol",
			Handler:    _ProtocolService_GetProtocol_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "protos/protocol.proto",
}
