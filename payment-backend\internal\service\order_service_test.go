package service

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"payment-backend/internal/config"
	"payment-backend/internal/domain"
	"payment-backend/internal/logger"
)

// MockOrderRepository 模拟订单仓储
type MockOrderRepository struct {
	mock.Mock
}

func (m *MockOrderRepository) Create(order *domain.Order) error {
	args := m.Called(order)
	return args.Error(0)
}

func (m *MockOrderRepository) GetByID(id uint64) (*domain.Order, error) {
	args := m.Called(id)
	return args.Get(0).(*domain.Order), args.Error(1)
}

func (m *MockOrderRepository) GetByOrderID(orderID string) (*domain.Order, error) {
	args := m.Called(orderID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*domain.Order), args.Error(1)
}

func (m *MockOrderRepository) GetByUserID(userID string, limit, offset int) ([]*domain.Order, error) {
	args := m.Called(userID, limit, offset)
	return args.Get(0).([]*domain.Order), args.Error(1)
}

func (m *MockOrderRepository) GetByPSPPaymentID(pspPaymentID string) (*domain.Order, error) {
	args := m.Called(pspPaymentID)
	return args.Get(0).(*domain.Order), args.Error(1)
}

func (m *MockOrderRepository) Update(order *domain.Order) error {
	args := m.Called(order)
	return args.Error(0)
}

func (m *MockOrderRepository) UpdateStatus(orderID string, payStatus string) error {
	args := m.Called(orderID, payStatus)
	return args.Error(0)
}

func (m *MockOrderRepository) SoftDelete(orderID string) error {
	args := m.Called(orderID)
	return args.Error(0)
}

func (m *MockOrderRepository) List(limit, offset int) ([]*domain.Order, error) {
	args := m.Called(limit, offset)
	return args.Get(0).([]*domain.Order), args.Error(1)
}

func (m *MockOrderRepository) Count() (int64, error) {
	args := m.Called()
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockOrderRepository) CountByUserID(userID string) (int64, error) {
	args := m.Called(userID)
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockOrderRepository) ListWithFilters(filter *domain.OrderFilter, pagination *domain.PaginationRequest) ([]*domain.Order, int64, error) {
	args := m.Called(filter, pagination)
	return args.Get(0).([]*domain.Order), args.Get(1).(int64), args.Error(2)
}

// 事务方法
func (m *MockOrderRepository) CreateWithTransaction(tx any, order *domain.Order) error {
	args := m.Called(tx, order)
	return args.Error(0)
}

func (m *MockOrderRepository) GetByIDWithTransaction(tx any, id uint64) (*domain.Order, error) {
	args := m.Called(tx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*domain.Order), args.Error(1)
}

func (m *MockOrderRepository) GetByOrderIDWithTransaction(tx any, orderID string) (*domain.Order, error) {
	args := m.Called(tx, orderID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*domain.Order), args.Error(1)
}

func (m *MockOrderRepository) UpdateWithTransaction(tx any, order *domain.Order) error {
	args := m.Called(tx, order)
	return args.Error(0)
}

func (m *MockOrderRepository) UpdateStatusWithTransaction(tx any, orderID string, payStatus string) error {
	args := m.Called(tx, orderID, payStatus)
	return args.Error(0)
}

func (m *MockOrderRepository) SoftDeleteWithTransaction(tx any, orderID string) error {
	args := m.Called(tx, orderID)
	return args.Error(0)
}

// MockPaymentGateway 模拟支付网关
type MockPaymentGateway struct {
	mock.Mock
}

func (m *MockPaymentGateway) CreateCheckoutSession(order *domain.Order, successURL, cancelURL string) (string, error) {
	args := m.Called(order, successURL, cancelURL)
	return args.String(0), args.Error(1)
}

func (m *MockPaymentGateway) RefundPayment(pspPaymentID string, amount float64) error {
	args := m.Called(pspPaymentID, amount)
	return args.Error(0)
}

func (m *MockPaymentGateway) VerifyWebhook(payload []byte, signature string) error {
	args := m.Called(payload, signature)
	return args.Error(0)
}

func TestOrderService_GetOrder(t *testing.T) {
	// 创建模拟对象
	mockRepo := new(MockOrderRepository)
	mockLogger, _ := logger.NewDevelopmentLogger()

	// 创建模拟配置
	cfg := &config.Config{
		Payment: config.PaymentConfig{
			Providers: map[string]config.ProviderConfig{
				"stripe": {
					SuccessURL: "https://example.com/success",
					CancelURL:  "https://example.com/cancel",
				},
			},
		},
	}

	// 创建服务
	service := NewOrderService(mockRepo, nil, cfg, mockLogger)

	// 准备测试数据
	expectedOrder := &domain.Order{
		ID:      1,
		OrderID: "test-order-123",
		UserID:  "user-123",
		Amount:  99.99,
	}

	// 设置模拟期望
	mockRepo.On("GetByOrderID", "test-order-123").Return(expectedOrder, nil)

	// 执行测试
	result, err := service.GetOrder("test-order-123")

	// 验证结果
	assert.NoError(t, err)
	assert.Equal(t, expectedOrder, result)

	// 验证模拟调用
	mockRepo.AssertExpectations(t)
}

func TestOrderService_GetUserOrders(t *testing.T) {
	// 创建模拟对象
	mockRepo := new(MockOrderRepository)
	mockLogger, _ := logger.NewDevelopmentLogger()

	// 创建模拟配置
	cfg := &config.Config{
		Payment: config.PaymentConfig{
			Providers: map[string]config.ProviderConfig{
				"stripe": {
					SuccessURL: "https://example.com/success",
					CancelURL:  "https://example.com/cancel",
				},
			},
		},
	}

	// 创建服务
	service := NewOrderService(mockRepo, nil, cfg, mockLogger)

	// 准备测试数据
	expectedOrders := []*domain.Order{
		{
			ID:      1,
			OrderID: "test-order-123",
			UserID:  "user-123",
			Amount:  99.99,
		},
		{
			ID:      2,
			OrderID: "test-order-456",
			UserID:  "user-123",
			Amount:  199.99,
		},
	}

	// 设置模拟期望
	mockRepo.On("GetByUserID", "user-123", 20, 0).Return(expectedOrders, nil)

	// 执行测试
	result, err := service.GetUserOrders("user-123", 20, 0)

	// 验证结果
	assert.NoError(t, err)
	assert.Equal(t, expectedOrders, result)
	assert.Len(t, result, 2)

	// 验证模拟调用
	mockRepo.AssertExpectations(t)
}

func TestSnowflakeInitialization(t *testing.T) {
	// 测试雪花算法初始化的协程安全性
	err := InitSnowflake(1)
	assert.NoError(t, err)

	// 多次调用应该是安全的
	err = InitSnowflake(2) // 这个应该被忽略，因为已经初始化过了
	assert.NoError(t, err)
}
