# Swagger API 文档

## 概述

本项目使用 Swagger 生成 API 文档，提供了完整的接口规范和交互式文档界面。

## 生成文档

### 自动生成

使用提供的脚本自动生成 Swagger 文档：

**Windows:**
```bash
scripts\generate_swagger.bat
```

**Linux/macOS:**
```bash
scripts/generate_swagger.sh
```

### 手动生成

如果需要手动生成文档：

1. 安装 swag 工具：
```bash
go install github.com/swaggo/swag/cmd/swag@latest
```

2. 生成文档：
```bash
swag init -g main.go -o docs/api/
```

## 生成的文件

生成的文档包含以下文件：

- `docs/api/docs.go` - Go 代码形式的文档定义
- `docs/api/swagger.json` - JSON 格式的 API 规范
- `docs/api/swagger.yaml` - YAML 格式的 API 规范

## 查看文档

### 在线查看

启动服务后，可以通过以下地址访问交互式 API 文档：

```
http://localhost:8080/swagger/index.html
```

### 本地查看

也可以使用其他 Swagger UI 工具查看生成的 JSON 或 YAML 文件：

- [Swagger Editor](https://editor.swagger.io/)
- [Swagger UI](https://swagger.io/tools/swagger-ui/)

## API 文档内容

当前已添加 Swagger 注释的接口：

### 创建订单 (POST /api/v1/order-service/orders)

- **描述**: 创建新的订单并生成支付链接
- **认证**: 需要 x-user-id 和 x-role 头部
- **请求体**: CreateOrderRequest
- **响应**: CreateOrderResponse

#### 请求参数

| 字段 | 类型 | 必填 | 描述 | 示例 |
|------|------|------|------|------|
| product_id | string | 是 | 产品ID | "prod_123" |
| product_desc | string | 否 | 产品描述 | "Premium Subscription" |
| price_id | string | 是 | 价格ID | "price_456" |
| quantity | integer | 是 | 购买数量 | 1 |
| currency | string | 否 | 货币代码 | "USD" |
| payed_method | string | 否 | 支付方式 | "stripe" |
| psp_provider | string | 是 | 支付服务提供商 | "stripe" |

#### 响应字段

| 字段 | 类型 | 描述 | 示例 |
|------|------|------|------|
| order_id | string | 订单ID | "20250710153045999stripe1234567890123456789" |
| checkout_url | string | 支付链接URL | "https://mock-payment.example.com/..." |
| amount | number | 订单金额 | 99.99 |
| currency | string | 货币代码 | "USD" |
| expires_at | string | 过期时间 | "2025-07-11T15:30:45Z" |

## 添加新接口的 Swagger 注释

为新接口添加 Swagger 注释的步骤：

1. **在处理器方法上添加注释**：
```go
// CreateOrder 创建订单
// @Summary 创建订单
// @Description 创建新的订单并生成支付链接，需要用户认证
// @Tags 订单管理
// @Accept json
// @Produce json
// @Param x-user-id header string true "用户ID" example("user123")
// @Param x-role header string true "用户角色" example("customer")
// @Param request body domain.CreateOrderRequest true "创建订单请求"
// @Success 303 {object} domain.CreateOrderResponse "订单创建成功，返回支付链接"
// @Failure 400 {object} domain.ErrorResponse "请求参数错误"
// @Failure 401 {object} domain.ErrorResponse "未授权，缺少认证信息"
// @Failure 500 {object} domain.ErrorResponse "服务器内部错误"
// @Router /api/v1/order-service/orders [post]
func (h *OrderHandler) CreateOrder(c *gin.Context) {
    // 实现代码...
}
```

2. **为结构体添加示例和描述**：
```go
type CreateOrderRequest struct {
    ProductID   string `json:"product_id" binding:"required" example:"prod_123" comment:"产品ID"`
    ProductDesc string `json:"product_desc" example:"Premium Subscription" comment:"产品描述"`
    // ...
}
```

3. **重新生成文档**：
```bash
swag init -g main.go -o docs/api/
```

## 注释语法参考

### 常用注释标签

- `@Summary` - 接口简要描述
- `@Description` - 接口详细描述
- `@Tags` - 接口分组标签
- `@Accept` - 接受的内容类型
- `@Produce` - 返回的内容类型
- `@Param` - 参数定义
- `@Success` - 成功响应
- `@Failure` - 错误响应
- `@Router` - 路由定义

### 参数类型

- `header` - 请求头参数
- `query` - 查询参数
- `path` - 路径参数
- `body` - 请求体参数

## 最佳实践

1. **保持注释更新**: 修改接口时同时更新 Swagger 注释
2. **提供示例**: 为所有字段提供有意义的示例值
3. **详细描述**: 提供清晰的接口和参数描述
4. **错误处理**: 文档化所有可能的错误响应
5. **分组管理**: 使用 Tags 对相关接口进行分组

## 故障排除

### 常见问题

1. **生成失败**: 检查注释语法是否正确
2. **字段缺失**: 确保结构体字段有正确的 json 标签
3. **路由不匹配**: 检查 @Router 注释是否与实际路由一致

### 调试技巧

- 使用 `swag init --parseVendor` 解析 vendor 目录
- 使用 `swag init --parseDependency` 解析依赖包
- 检查生成的 docs.go 文件确认结构是否正确
