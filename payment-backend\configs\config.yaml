# 支付后端服务配置文件

# 服务器配置
server:
  host: "0.0.0.0"
  port: 8080
  read_timeout: 30
  write_timeout: 30
  mode: "release" # debug, release, test

# 数据库配置
database:
  driver: "mysql"
  host: "localhost"
  port: 3306
  username: "payment_user"
  password: "payment_password"
  database: "payment_db"
  ssl_mode: "disable"

# 支付配置
payment:
  providers:
    paypal:
      enabled: true
      api_key: "your_paypal_api_key"
      secret_key: "your_paypal_secret_key"
      success_url: "https://your-domain.com/success"
      cancel_url: "https://your-domain.com/cancel"
      webhook:
        url: "https://your-domain.com/api/v1/pay-service/webhooks/paypal"
        secret: "your_paypal_webhook_secret"
      settings:
        environment: "sandbox" # sandbox, live

    stripe:
      enabled: true
      api_key: "your_stripe_api_key"
      secret_key: "your_stripe_secret_key"
      success_url: "https://your-domain.com/success"
      cancel_url: "https://your-domain.com/cancel"
      webhook:
        url: "https://your-domain.com/api/v1/pay-service/webhooks/stripe"
        secret: "your_stripe_webhook_secret"
      settings:
        environment: "test" # test, live

# 日志配置
log:
  level: "info" # debug, info, warn, error
  format: "json" # json, console
  output: "stdout" # stdout, file
  filename: "logs/payment-backend.log"
  max_size: 50 # MB
  max_backups: 14
  max_age: 28 # days

# 雪花算法配置
snowflake:
  node_id: 1 # 节点ID，用于生成唯一的雪花算法ID，在分布式环境中每个节点应该有不同的ID

# 管理员配置
admin:
  allowed_roles: # 允许访问管理员接口的角色列表
    - "admin"
    - "super_admin"
  pagination: # 分页配置
    default_limit: 50 # 默认每页数量
    max_limit: 500 # 最大每页数量

# Dubbo配置
dubbo:
  enabled: true # 是否启用Dubbo服务，默认关闭
  port: 20000 # Dubbo服务端口
  ip: "0.0.0.0" # Dubbo服务IP地址

# Nacos配置（统一配置中心和注册中心）
nacos:
  enabled: true # 是否启用Nacos，默认关闭
  endpoints: # Nacos服务器地址列表
    - "127.0.0.1:8848"
  namespace: "test-payment-service" # 命名空间
  username: "test-nacos" # 用户名
  password: "test-nacos" # 密码

  # 配置中心设置
  config:
    enabled: true # 是否启用配置中心
    data_id: "payment-backend.yaml" # 配置文件ID
    group: "DEFAULT_GROUP" # 配置组
    timeout: 30 # 连接超时时间(秒)

  # 注册中心设置
  registry:
    enabled: true # 是否启用注册中心
