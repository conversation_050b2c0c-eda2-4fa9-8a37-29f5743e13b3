@echo off

setlocal

echo Generating Swagger documentation...

REM check swag
where swag >nul 2>nul
if %errorlevel% neq 0 (
    echo Installing swag...
    go install github.com/swaggo/swag/cmd/swag@latest
    if %errorlevel% neq 0 (
        echo Failed to install swag
        exit /b 1
    )
)

REM ge Swagger doc
echo Running swag init...
swag init -g main.go -o docs/api/
if %errorlevel% neq 0 (
    echo Failed to generate Swagger documentation
    exit /b 1
)

echo Swagger documentation generated successfully!
echo Files generated:
echo   - docs/api/docs.go
echo   - docs/api/swagger.json
echo   - docs/api/swagger.yaml

echo.
echo You can now access the API documentation at:
echo   http://localhost:8080/swagger/index.html
