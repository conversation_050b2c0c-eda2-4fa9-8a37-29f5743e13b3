// Code generated by protoc-gen-triple. DO NOT EDIT.
//
// Source: protos/area.proto
package admin

import (
	"context"
)

import (
	"dubbo.apache.org/dubbo-go/v3"
	"dubbo.apache.org/dubbo-go/v3/client"
	"dubbo.apache.org/dubbo-go/v3/common"
	"dubbo.apache.org/dubbo-go/v3/common/constant"
	"dubbo.apache.org/dubbo-go/v3/protocol/triple/triple_protocol"
	"dubbo.apache.org/dubbo-go/v3/server"
)

// This is a compile-time assertion to ensure that this generated file and the Triple package
// are compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of Triple newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of Triple or updating the Triple
// version compiled into your binary.
const _ = triple_protocol.IsAtLeastVersion0_1_0

const (
	// AreaServiceName is the fully-qualified name of the AreaService service.
	AreaServiceName = "com.aibook.admin.grpc.AreaService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// AreaServicegetAreasProcedure is the fully-qualified name of the AreaService's getAreas RPC.
	AreaServicegetAreasProcedure = "/com.aibook.admin.grpc.AreaService/getAreas"
)

var (
	_ AreaService = (*AreaServiceImpl)(nil)
)

// AreaService is a client for the com.aibook.admin.grpc.AreaService service.
type AreaService interface {
	GetAreas(ctx context.Context, req *Empty, opts ...client.CallOption) (*AreaResponse, error)
}

// NewAreaService constructs a client for the admin.AreaService service.
func NewAreaService(cli *client.Client, opts ...client.ReferenceOption) (AreaService, error) {
	conn, err := cli.DialWithInfo("com.aibook.admin.grpc.AreaService", &AreaService_ClientInfo, opts...)
	if err != nil {
		return nil, err
	}
	return &AreaServiceImpl{
		conn: conn,
	}, nil
}

func SetConsumerAreaService(srv common.RPCService) {
	dubbo.SetConsumerServiceWithInfo(srv, &AreaService_ClientInfo)
}

// AreaServiceImpl implements AreaService.
type AreaServiceImpl struct {
	conn *client.Connection
}

func (c *AreaServiceImpl) GetAreas(ctx context.Context, req *Empty, opts ...client.CallOption) (*AreaResponse, error) {
	resp := new(AreaResponse)
	if err := c.conn.CallUnary(ctx, []interface{}{req}, resp, "getAreas", opts...); err != nil {
		return nil, err
	}
	return resp, nil
}

var AreaService_ClientInfo = client.ClientInfo{
	InterfaceName: "com.aibook.admin.grpc.AreaService",
	MethodNames:   []string{"getAreas"},
	ConnectionInjectFunc: func(dubboCliRaw interface{}, conn *client.Connection) {
		dubboCli := dubboCliRaw.(*AreaServiceImpl)
		dubboCli.conn = conn
	},
}

// AreaServiceHandler is an implementation of the com.aibook.admin.grpc.AreaService service.
type AreaServiceHandler interface {
	GetAreas(context.Context, *Empty) (*AreaResponse, error)
}

func RegisterAreaServiceHandler(srv *server.Server, hdlr AreaServiceHandler, opts ...server.ServiceOption) error {
	return srv.Register(hdlr, &AreaService_ServiceInfo, opts...)
}

func SetProviderAreaService(srv common.RPCService) {
	dubbo.SetProviderServiceWithInfo(srv, &AreaService_ServiceInfo)
}

var AreaService_ServiceInfo = server.ServiceInfo{
	InterfaceName: "com.aibook.admin.grpc.AreaService",
	ServiceType:   (*AreaServiceHandler)(nil),
	Methods: []server.MethodInfo{
		{
			Name: "getAreas",
			Type: constant.CallUnary,
			ReqInitFunc: func() interface{} {
				return new(Empty)
			},
			MethodFunc: func(ctx context.Context, args []interface{}, handler interface{}) (interface{}, error) {
				req := args[0].(*Empty)
				res, err := handler.(AreaServiceHandler).GetAreas(ctx, req)
				if err != nil {
					return nil, err
				}
				return triple_protocol.NewResponse(res), nil
			},
		},
	},
}
