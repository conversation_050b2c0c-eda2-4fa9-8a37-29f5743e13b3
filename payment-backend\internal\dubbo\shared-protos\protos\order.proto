syntax = "proto3";

package com.aibook.payment.grpc;
option java_multiple_files = true;
option go_package = "./paymentpb;paymentpb";

import "google/protobuf/timestamp.proto";

message ProductDescMeta {
  string country = 1; // 订单国家
  string product_title = 2; // 产品名称
  string general_info = 3; // 产品概要说明
}

// 订单信息
message Order {
  uint64 id = 1; // 自增主键 ID
  string order_id = 2; // 订单 ID（业务主键）
  string user_id = 3; // 用户 ID
  string product_id = 4; // 产品 ID
  string product_desc = 5; // 产品描述. 这里存的是产品描述元数据。对应订单中的 ProductDesc 字段。 ProductDesc 存储的是 ProductDescMeta 结构 json 序列化之后再 base64 编码得到的字符串。
  string price_id = 6; // 价格 ID
  uint32 quantity = 7; // 购买数量
  double amount = 8; // 订单总金额（含税/手续费）
  double net_amount = 9; // 净金额（去税/手续费）
  string currency = 10; // 币种（ISO-4217，如 USD）
  string pay_status = 11; // 支付状态（如 created、paid、succeeded 等）
  string pay_ret = 12; // 支付错误信息
  string payed_method = 13; // 支付方式（如 card、paypal 等）
  string psp_provider = 14; // 支付服务商名称（PSP，如 stripe、paypal）
  string card_number = 15; // 支付卡尾号
  optional google.protobuf.Timestamp payed_at = 16; // 支付时间
  string refund_status = 17; // 退款状态（如 none、requested、succeeded）
  optional google.protobuf.Timestamp refunded_at = 18; // 退款时间
  string psp_product_id = 19; // 支付平台上的产品 ID
  string psp_product_desc = 20; // 支付平台上的产品描述
  string psp_price_id = 21; // 支付平台上的价格 ID
  string psp_payment_id = 22; // 支付平台生成的支付 ID
  string psp_payment_intent_id = 23; // PSP Intent ID 
  string psp_payment_refund_id = 24; // PSP 最后一次退款的 ID  
  string psp_payment_refund_ret = 25; // PSP 最后一次退款的结果或失败原因
  string psp_customer_id = 26; // 支付平台生成的客户 ID
  string psp_customer_email = 27; // 支付平台客户的邮箱
  string psp_subscription_id = 28; // 支付平台的订阅 ID
  google.protobuf.Timestamp created_at = 29; // 创建时间
  google.protobuf.Timestamp updated_at = 30; // 更新时间
  bool deleted = 31; // 是否被逻辑删除（true 表示删除）
  optional google.protobuf.Timestamp deleted_at = 32; // 删除时间（逻辑删除）
}

// 订单过滤条件
message OrderFilter {
  optional string user_id = 1; // 过滤：用户 ID
  optional string currency = 2; // 过滤：币种
  optional string pay_status = 3; // 过滤：支付状态
  optional string payed_method = 4; // 过滤：支付方式
  optional string psp_provider = 5; // 过滤：支付服务商
  optional google.protobuf.Timestamp payed_at_start = 6; // 过滤：支付开始时间
  optional google.protobuf.Timestamp payed_at_end = 7; // 过滤：支付结束时间
  optional string refund_status = 8; // 过滤：退款状态
  optional google.protobuf.Timestamp refunded_at_start = 9; // 过滤：退款开始时间
  optional google.protobuf.Timestamp refunded_at_end = 10; // 过滤：退款结束时间
  optional string psp_price_id = 11; // 过滤：支付平台价格 ID
  optional string psp_customer_email = 12; // 过滤：支付平台客户邮箱
  optional string psp_subscription_id = 13; // 过滤：支付平台订阅 ID
}

// 分页请求
message PaginationRequest {
  int32 limit = 1; // 每页数量
  int32 offset = 2; // 偏移量
}

// 分页响应
message PaginationResponse {
  int64 total = 1; // 总记录数
  int32 limit = 2; // 当前页大小
  int32 offset = 3; // 当前偏移量
  int64 remaining = 4; // 剩余记录数
}

// 获取所有订单请求
message ListAllOrdersRequest {
  optional OrderFilter filter = 1; // 过滤条件
  optional PaginationRequest pagination = 2; // 分页参数
}

// 获取所有订单响应
message ListAllOrdersResponse {
  repeated Order orders = 1; // 订单列表
  PaginationResponse pagination = 2; // 分页响应信息
}

// 订单服务定义
service OrderService {
  // 获取所有订单列表（管理员接口）
  rpc ListAllOrders(ListAllOrdersRequest) returns (ListAllOrdersResponse);
}
