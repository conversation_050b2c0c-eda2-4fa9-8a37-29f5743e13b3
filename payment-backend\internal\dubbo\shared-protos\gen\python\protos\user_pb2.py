# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: protos/user.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'protos/user.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
import validate_pb2 as validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x11protos/user.proto\x12\x14\x63om.aibook.user.grpc\x1a\x1cgoogle/api/annotations.proto\x1a\x0evalidate.proto\"K\n\rChargeRequest\x12\x1f\n\x06points\x18\x01 \x01(\x02\x42\x0f\xfa\x42\x0c\n\n\x1d\x00$tI-\x00\x00\x80?\x12\x19\n\x06reason\x18\x02 \x01(\tB\t\xfa\x42\x06r\x04\x10\x01\x18X\"G\n\x0bPageRequest\x12\x19\n\x04page\x18\x01 \x01(\x05\x42\x0b\xfa\x42\x08\x1a\x06\x18\xa0\x8d\x06(\x01\x12\x1d\n\tpage_size\x18\x02 \x01(\x05\x42\n\xfa\x42\x07\x1a\x05\x18\x90N(\x01\">\n\x0cPageResponse\x12\x0c\n\x04page\x18\x01 \x01(\x05\x12\x11\n\tpage_size\x18\x02 \x01(\x05\x12\r\n\x05total\x18\x03 \x01(\x05\"\x7f\n\x15\x42\x65nefitRecordResponse\x12\x34\n\x07records\x18\x01 \x03(\x0b\x32#.com.aibook.user.grpc.BenefitRecord\x12\x30\n\x04page\x18\x02 \x01(\x0b\x32\".com.aibook.user.grpc.PageResponse\"\xaf\x01\n\rBenefitRecord\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x14\n\x0c\x62\x65nefit_type\x18\x02 \x01(\r\x12\x15\n\rbenefit_count\x18\x03 \x01(\r\x12\x13\n\x0bpoints_used\x18\x04 \x01(\x02\x12\x18\n\x10points_recharged\x18\x05 \x01(\x02\x12\x0e\n\x06reason\x18\x06 \x01(\t\x12\x12\n\ncreated_at\x18\x07 \x01(\r\x12\x12\n\nupdated_at\x18\x08 \x01(\r\"\xac\x01\n\rSearchRequest\x12\x19\n\x04page\x18\x01 \x01(\x05\x42\x0b\xfa\x42\x08\x1a\x06\x18\xa0\x8d\x06(\x01\x12\x1d\n\tpage_size\x18\x02 \x01(\x05\x42\n\xfa\x42\x07\x1a\x05\x18\x90N(\x01\x12\x18\n\x10searchByNickname\x18\x03 \x01(\t\x12\x15\n\rsearchByEmail\x18\x04 \x01(\t\x12\x18\n\x10searchByDeviceId\x18\x05 \x01(\t\x12\x16\n\x0esearchByUserId\x18\x06 \x01(\x04\"y\n\x0eSearchResponse\x12\x32\n\x05users\x18\x01 \x03(\x0b\x32#.com.aibook.user.grpc.DetailProfile\x12\x0c\n\x04page\x18\x02 \x01(\x05\x12\x11\n\tpage_size\x18\x03 \x01(\x05\x12\x12\n\ntotal_size\x18\x04 \x01(\x05\"\x07\n\x05\x45mpty\"3\n\x17\x44\x65leteSystemPlanRequest\x12\x18\n\x07plan_id\x18\x01 \x01(\rB\x07\xfa\x42\x04*\x02(\x01\"7\n DeleteSystemFeaturePointsRequest\x12\x13\n\x02id\x18\x01 \x01(\rB\x07\xfa\x42\x04*\x02(\x01\">\n\x0bSystemPlans\x12/\n\x05plans\x18\x01 \x03(\x0b\x32 .com.aibook.user.grpc.SystemPlan\"\xde\x01\n\x13SystemFeaturePoints\x12\n\n\x02id\x18\x01 \x01(\r\x12$\n\x07\x63ountry\x18\x02 \x01(\tB\x13\xfa\x42\x10r\x0e\x10\x02\x18\x03\x32\x08^[A-Z]+$\x12#\n\x0f\x66\x65\x61ture1_points\x18\x03 \x01(\x02\x42\n\xfa\x42\x07\n\x05-\xcd\xcc\xcc=\x12#\n\x0f\x66\x65\x61ture2_points\x18\x04 \x01(\x02\x42\n\xfa\x42\x07\n\x05-\xcd\xcc\xcc=\x12#\n\x0f\x66\x65\x61ture3_points\x18\x05 \x01(\x02\x42\n\xfa\x42\x07\n\x05-\x00\x00\x00\x00\x12\x12\n\ncreated_at\x18\x06 \x01(\r\x12\x12\n\nupdated_at\x18\x07 \x01(\r\"\\\n\x1eGetSystemFeaturePointsResponse\x12:\n\x07records\x18\x01 \x03(\x0b\x32).com.aibook.user.grpc.SystemFeaturePoints\"\xa5\x02\n\nSystemPlan\x12\x0f\n\x07plan_id\x18\x01 \x01(\r\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x12\r\n\x05price\x18\x04 \x01(\x02\x12\x11\n\tplan_type\x18\x05 \x01(\x05\x12$\n\x07\x63ountry\x18\x06 \x01(\tB\x13\xfa\x42\x10r\x0e\x10\x02\x18\x03\x32\x08^[A-Z]+$\x12\x10\n\x08\x63urrency\x18\x07 \x01(\t\x12\x1f\n\x0e\x66\x65\x61ture1_total\x18\x08 \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02(\x01\x12\x1f\n\x0e\x66\x65\x61ture2_total\x18\t \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02(\x01\x12\x1f\n\x0e\x66\x65\x61ture3_total\x18\n \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02(\x00\x12\x12\n\ncreated_at\x18\x0b \x01(\r\x12\x12\n\nupdated_at\x18\x0c \x01(\r\"\xeb\x01\n\x08UserPlan\x12\x0f\n\x07plan_id\x18\x01 \x01(\x05\x12\x11\n\tplan_type\x18\x02 \x01(\x05\x12\x14\n\x0c\x61\x63tivated_at\x18\x03 \x01(\r\x12\x12\n\nexpired_at\x18\x04 \x01(\r\x12\x17\n\x0f\x66\x65\x61ture1_remain\x18\x05 \x01(\x05\x12\x17\n\x0f\x66\x65\x61ture2_remain\x18\x06 \x01(\x05\x12\x17\n\x0f\x66\x65\x61ture3_remain\x18\x07 \x01(\x05\x12\x16\n\x0e\x66\x65\x61ture1_total\x18\x08 \x01(\x05\x12\x16\n\x0e\x66\x65\x61ture2_total\x18\t \x01(\x05\x12\x16\n\x0e\x66\x65\x61ture3_total\x18\n \x01(\x05\"\xa9\x01\n\x14ReduceBenefitRequest\x12\x1f\n\x0c\x62\x65nefit_type\x18\x01 \x01(\x05\x42\t\xfa\x42\x06\x1a\x04\x18\x03(\x00\x12(\n\x14\x62\x65nefit_reduce_count\x18\x02 \x01(\x05\x42\n\xfa\x42\x07\x1a\x05\x18\x90N(\x00\x12+\n\x12point_reduce_count\x18\x03 \x01(\x02\x42\x0f\xfa\x42\x0c\n\n\x1d\x00@\x1c\x46-\x00\x00\x00\x00\x12\x19\n\x06reason\x18\x04 \x01(\tB\t\xfa\x42\x06r\x04\x10\x01\x18X\" \n\x0c\x42\x65nefitOrder\x12\x10\n\x08order_id\x18\x01 \x01(\x04\"\x83\x01\n\nDeviceInfo\x12\x11\n\tdevice_id\x18\x01 \x01(\t\x12\x13\n\x0b\x64\x65vice_name\x18\x02 \x01(\t\x12\x13\n\x0b\x64\x65vice_type\x18\x03 \x01(\t\x12\x0f\n\x07os_name\x18\x04 \x01(\t\x12\x12\n\nos_version\x18\x05 \x01(\t\x12\x13\n\x0b\x61pp_version\x18\x06 \x01(\t\"\xff\x01\n\x11\x43reateUserRequest\x12\x16\n\x05\x65mail\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02`\x01\x12\x1c\n\x08password\x18\x02 \x01(\tB\n\xfa\x42\x07r\x05\x10\x06\x18\xbc\x01\x12$\n\x07\x63ountry\x18\x03 \x01(\tB\x13\xfa\x42\x10r\x0e\x10\x02\x18\x03\x32\x08^[A-Z]+$\x12\x1b\n\x08language\x18\x04 \x01(\tB\t\xfa\x42\x06r\x04\x10\x02\x18X\x12\x35\n\x0b\x64\x65vice_info\x18\x05 \x01(\x0b\x32 .com.aibook.user.grpc.DeviceInfo\x12\x19\n\x11\x65mail_verify_code\x18\x06 \x01(\t\x12\x1f\n\x17\x65mail_verify_code_token\x18\x07 \x01(\t\"8\n\x0cLoginRequest\x12\x16\n\x05\x65mail\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02`\x01\x12\x10\n\x08password\x18\x02 \x01(\t\"O\n\rLoginResponse\x12\x0b\n\x03seq\x18\x01 \x01(\r\x12\r\n\x05token\x18\x02 \x01(\t\x12\x11\n\texpire_at\x18\x03 \x01(\r\x12\x0f\n\x07user_id\x18\x04 \x01(\x04\"j\n\x14LoginWithCodeRequest\x12\x16\n\x05\x65mail\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02`\x01\x12\x19\n\x11\x65mail_verify_code\x18\x02 \x01(\t\x12\x1f\n\x17\x65mail_verify_code_token\x18\x03 \x01(\t\"C\n\x15UpdatePasswordRequest\x12\x14\n\x0cold_password\x18\x01 \x01(\t\x12\x14\n\x0cnew_password\x18\x02 \x01(\t\"\x80\x01\n\x14ResetPasswordRequest\x12\x16\n\x05\x65mail\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02`\x01\x12\x19\n\x11\x65mail_verify_code\x18\x02 \x01(\t\x12\x1f\n\x17\x65mail_verify_code_token\x18\x03 \x01(\t\x12\x14\n\x0cnew_password\x18\x04 \x01(\t\"\x13\n\x11GetProfileRequest\"\xd1\x01\n\x0bUserProfile\x12\x10\n\x08nickname\x18\x01 \x01(\t\x12\r\n\x05\x65mail\x18\x02 \x01(\t\x12\x0e\n\x06status\x18\x03 \x01(\x05\x12\x0f\n\x07\x63ountry\x18\x04 \x01(\t\x12\x0c\n\x04lang\x18\x05 \x01(\t\x12\x10\n\x08\x62irthday\x18\x06 \x01(\r\x12\x0e\n\x06points\x18\x07 \x01(\x02\x12\x11\n\tdevice_id\x18\x08 \x01(\t\x12\x15\n\rlast_login_at\x18\t \x01(\r\x12\x12\n\ncreated_at\x18\n \x01(\r\x12\x12\n\nupdated_at\x18\x0b \x01(\r\"T\n\rDetailProfile\x12\x0f\n\x07user_id\x18\x01 \x01(\x04\x12\x32\n\x07profile\x18\x02 \x01(\x0b\x32!.com.aibook.user.grpc.UserProfile\"H\n\x14UpdateProfileRequest\x12\x10\n\x08nickname\x18\x01 \x01(\t\x12\x0c\n\x04lang\x18\x02 \x01(\t\x12\x10\n\x08\x62irthday\x18\x03 \x01(\r\"3\n\x19GetEmailVerifyCodeRequest\x12\x16\n\x05\x65mail\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02`\x01\"X\n\x1aGetEmailVerifyCodeResponse\x12\x1f\n\x17\x65mail_verify_code_token\x18\x01 \x01(\t\x12\x19\n\x11\x65mail_verify_code\x18\x02 \x01(\t2\xf6\x14\n\x0bUserService\x12\x9f\x01\n\x12GetEmailVerifyCode\x12/.com.aibook.user.grpc.GetEmailVerifyCodeRequest\x1a\x30.com.aibook.user.grpc.GetEmailVerifyCodeResponse\"&\x82\xd3\xe4\x93\x02 \x12\x1e/api/v1/user/email_verify_code\x12z\n\nCreateUser\x12\'.com.aibook.user.grpc.CreateUserRequest\x1a#.com.aibook.user.grpc.LoginResponse\"\x1e\x82\xd3\xe4\x93\x02\x18\"\x13/api/v1/user/create:\x01*\x12o\n\x05Login\x12\".com.aibook.user.grpc.LoginRequest\x1a#.com.aibook.user.grpc.LoginResponse\"\x1d\x82\xd3\xe4\x93\x02\x17\"\x12/api/v1/user/login:\x01*\x12\x89\x01\n\rLoginWithCode\x12*.com.aibook.user.grpc.LoginWithCodeRequest\x1a#.com.aibook.user.grpc.LoginResponse\"\'\x82\xd3\xe4\x93\x02!\"\x1c/api/v1/user/login_with_code:\x01*\x12w\n\x0cRefreshToken\x12\x1b.com.aibook.user.grpc.Empty\x1a#.com.aibook.user.grpc.LoginResponse\"%\x82\xd3\xe4\x93\x02\x1f\"\x1a/api/v1/user/refresh_token:\x01*\x12v\n\nGetProfile\x12\'.com.aibook.user.grpc.GetProfileRequest\x1a!.com.aibook.user.grpc.UserProfile\"\x1c\x82\xd3\xe4\x93\x02\x16\x12\x14/api/v1/user/profile\x12\x7f\n\rUpdateProfile\x12*.com.aibook.user.grpc.UpdateProfileRequest\x1a!.com.aibook.user.grpc.UserProfile\"\x1f\x82\xd3\xe4\x93\x02\x19\"\x14/api/v1/user/profile:\x01*\x12\x80\x01\n\rResetPassword\x12*.com.aibook.user.grpc.ResetPasswordRequest\x1a\x1b.com.aibook.user.grpc.Empty\"&\x82\xd3\xe4\x93\x02 \"\x1b/api/v1/user/reset_password:\x01*\x12s\n\x10UpdateSystemPlan\x12 .com.aibook.user.grpc.SystemPlan\x1a\x1b.com.aibook.user.grpc.Empty\" \x82\xd3\xe4\x93\x02\x1a\x1a\x15/api/v1/user/sys/plan:\x01*\x12p\n\x0eGetSystemPlans\x12\x1b.com.aibook.user.grpc.Empty\x1a!.com.aibook.user.grpc.SystemPlans\"\x1e\x82\xd3\xe4\x93\x02\x18\x12\x16/api/v1/user/sys/plans\x12}\n\x10\x44\x65leteSystemPlan\x12-.com.aibook.user.grpc.DeleteSystemPlanRequest\x1a\x1b.com.aibook.user.grpc.Empty\"\x1d\x82\xd3\xe4\x93\x02\x17*\x15/api/v1/user/sys/plan\x12\x8f\x01\n\x19UpdateSystemFeaturePoints\x12).com.aibook.user.grpc.SystemFeaturePoints\x1a\x1b.com.aibook.user.grpc.Empty\"*\x82\xd3\xe4\x93\x02$\x1a\x1f/api/v1/user/sys/feature_points:\x01*\x12\x94\x01\n\x16GetSystemFeaturePoints\x12\x1b.com.aibook.user.grpc.Empty\x1a\x34.com.aibook.user.grpc.GetSystemFeaturePointsResponse\"\'\x82\xd3\xe4\x93\x02!\x12\x1f/api/v1/user/sys/feature_points\x12\x99\x01\n\x19\x44\x65leteSystemFeaturePoints\x12\x36.com.aibook.user.grpc.DeleteSystemFeaturePointsRequest\x1a\x1b.com.aibook.user.grpc.Empty\"\'\x82\xd3\xe4\x93\x02!*\x1f/api/v1/user/sys/feature_points\x12\x61\n\x07GetPlan\x12\x1b.com.aibook.user.grpc.Empty\x1a\x1e.com.aibook.user.grpc.UserPlan\"\x19\x82\xd3\xe4\x93\x02\x13\x12\x11/api/v1/user/plan\x12\x81\x01\n\nReducePlan\x12*.com.aibook.user.grpc.ReduceBenefitRequest\x1a\".com.aibook.user.grpc.BenefitOrder\"#\x82\xd3\xe4\x93\x02\x1d\"\x18/api/v1/user/plan/reduce:\x01*\x12\x82\x01\n\x18RollbackHalfPointBenefit\x12\".com.aibook.user.grpc.BenefitOrder\x1a\x1b.com.aibook.user.grpc.Empty\"%\x82\xd3\xe4\x93\x02\x1f\"\x1a/api/v1/user/plan/rollback:\x01*\x12\x81\x01\n\rBenefitRecord\x12!.com.aibook.user.grpc.PageRequest\x1a+.com.aibook.user.grpc.BenefitRecordResponse\" \x82\xd3\xe4\x93\x02\x1a\x12\x18/api/v1/user/plan/record\x12p\n\x0c\x43hargePoints\x12#.com.aibook.user.grpc.ChargeRequest\x1a\x1b.com.aibook.user.grpc.Empty\"\x1e\x82\xd3\xe4\x93\x02\x18\"\x13/api/v1/user/charge:\x01*\x12X\n\x06\x44\x65lete\x12\x1b.com.aibook.user.grpc.Empty\x1a\x1b.com.aibook.user.grpc.Empty\"\x14\x82\xd3\xe4\x93\x02\x0e*\x0c/api/v1/user\x12p\n\x06Search\x12#.com.aibook.user.grpc.SearchRequest\x1a$.com.aibook.user.grpc.SearchResponse\"\x1b\x82\xd3\xe4\x93\x02\x15\x12\x13/api/v1/user/searchB\x13P\x01Z\x0f./userpb;userpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'protos.user_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'P\001Z\017./userpb;userpb'
  _globals['_CHARGEREQUEST'].fields_by_name['points']._loaded_options = None
  _globals['_CHARGEREQUEST'].fields_by_name['points']._serialized_options = b'\372B\014\n\n\035\000$tI-\000\000\200?'
  _globals['_CHARGEREQUEST'].fields_by_name['reason']._loaded_options = None
  _globals['_CHARGEREQUEST'].fields_by_name['reason']._serialized_options = b'\372B\006r\004\020\001\030X'
  _globals['_PAGEREQUEST'].fields_by_name['page']._loaded_options = None
  _globals['_PAGEREQUEST'].fields_by_name['page']._serialized_options = b'\372B\010\032\006\030\240\215\006(\001'
  _globals['_PAGEREQUEST'].fields_by_name['page_size']._loaded_options = None
  _globals['_PAGEREQUEST'].fields_by_name['page_size']._serialized_options = b'\372B\007\032\005\030\220N(\001'
  _globals['_SEARCHREQUEST'].fields_by_name['page']._loaded_options = None
  _globals['_SEARCHREQUEST'].fields_by_name['page']._serialized_options = b'\372B\010\032\006\030\240\215\006(\001'
  _globals['_SEARCHREQUEST'].fields_by_name['page_size']._loaded_options = None
  _globals['_SEARCHREQUEST'].fields_by_name['page_size']._serialized_options = b'\372B\007\032\005\030\220N(\001'
  _globals['_DELETESYSTEMPLANREQUEST'].fields_by_name['plan_id']._loaded_options = None
  _globals['_DELETESYSTEMPLANREQUEST'].fields_by_name['plan_id']._serialized_options = b'\372B\004*\002(\001'
  _globals['_DELETESYSTEMFEATUREPOINTSREQUEST'].fields_by_name['id']._loaded_options = None
  _globals['_DELETESYSTEMFEATUREPOINTSREQUEST'].fields_by_name['id']._serialized_options = b'\372B\004*\002(\001'
  _globals['_SYSTEMFEATUREPOINTS'].fields_by_name['country']._loaded_options = None
  _globals['_SYSTEMFEATUREPOINTS'].fields_by_name['country']._serialized_options = b'\372B\020r\016\020\002\030\0032\010^[A-Z]+$'
  _globals['_SYSTEMFEATUREPOINTS'].fields_by_name['feature1_points']._loaded_options = None
  _globals['_SYSTEMFEATUREPOINTS'].fields_by_name['feature1_points']._serialized_options = b'\372B\007\n\005-\315\314\314='
  _globals['_SYSTEMFEATUREPOINTS'].fields_by_name['feature2_points']._loaded_options = None
  _globals['_SYSTEMFEATUREPOINTS'].fields_by_name['feature2_points']._serialized_options = b'\372B\007\n\005-\315\314\314='
  _globals['_SYSTEMFEATUREPOINTS'].fields_by_name['feature3_points']._loaded_options = None
  _globals['_SYSTEMFEATUREPOINTS'].fields_by_name['feature3_points']._serialized_options = b'\372B\007\n\005-\000\000\000\000'
  _globals['_SYSTEMPLAN'].fields_by_name['country']._loaded_options = None
  _globals['_SYSTEMPLAN'].fields_by_name['country']._serialized_options = b'\372B\020r\016\020\002\030\0032\010^[A-Z]+$'
  _globals['_SYSTEMPLAN'].fields_by_name['feature1_total']._loaded_options = None
  _globals['_SYSTEMPLAN'].fields_by_name['feature1_total']._serialized_options = b'\372B\004\032\002(\001'
  _globals['_SYSTEMPLAN'].fields_by_name['feature2_total']._loaded_options = None
  _globals['_SYSTEMPLAN'].fields_by_name['feature2_total']._serialized_options = b'\372B\004\032\002(\001'
  _globals['_SYSTEMPLAN'].fields_by_name['feature3_total']._loaded_options = None
  _globals['_SYSTEMPLAN'].fields_by_name['feature3_total']._serialized_options = b'\372B\004\032\002(\000'
  _globals['_REDUCEBENEFITREQUEST'].fields_by_name['benefit_type']._loaded_options = None
  _globals['_REDUCEBENEFITREQUEST'].fields_by_name['benefit_type']._serialized_options = b'\372B\006\032\004\030\003(\000'
  _globals['_REDUCEBENEFITREQUEST'].fields_by_name['benefit_reduce_count']._loaded_options = None
  _globals['_REDUCEBENEFITREQUEST'].fields_by_name['benefit_reduce_count']._serialized_options = b'\372B\007\032\005\030\220N(\000'
  _globals['_REDUCEBENEFITREQUEST'].fields_by_name['point_reduce_count']._loaded_options = None
  _globals['_REDUCEBENEFITREQUEST'].fields_by_name['point_reduce_count']._serialized_options = b'\372B\014\n\n\035\000@\034F-\000\000\000\000'
  _globals['_REDUCEBENEFITREQUEST'].fields_by_name['reason']._loaded_options = None
  _globals['_REDUCEBENEFITREQUEST'].fields_by_name['reason']._serialized_options = b'\372B\006r\004\020\001\030X'
  _globals['_CREATEUSERREQUEST'].fields_by_name['email']._loaded_options = None
  _globals['_CREATEUSERREQUEST'].fields_by_name['email']._serialized_options = b'\372B\004r\002`\001'
  _globals['_CREATEUSERREQUEST'].fields_by_name['password']._loaded_options = None
  _globals['_CREATEUSERREQUEST'].fields_by_name['password']._serialized_options = b'\372B\007r\005\020\006\030\274\001'
  _globals['_CREATEUSERREQUEST'].fields_by_name['country']._loaded_options = None
  _globals['_CREATEUSERREQUEST'].fields_by_name['country']._serialized_options = b'\372B\020r\016\020\002\030\0032\010^[A-Z]+$'
  _globals['_CREATEUSERREQUEST'].fields_by_name['language']._loaded_options = None
  _globals['_CREATEUSERREQUEST'].fields_by_name['language']._serialized_options = b'\372B\006r\004\020\002\030X'
  _globals['_LOGINREQUEST'].fields_by_name['email']._loaded_options = None
  _globals['_LOGINREQUEST'].fields_by_name['email']._serialized_options = b'\372B\004r\002`\001'
  _globals['_LOGINWITHCODEREQUEST'].fields_by_name['email']._loaded_options = None
  _globals['_LOGINWITHCODEREQUEST'].fields_by_name['email']._serialized_options = b'\372B\004r\002`\001'
  _globals['_RESETPASSWORDREQUEST'].fields_by_name['email']._loaded_options = None
  _globals['_RESETPASSWORDREQUEST'].fields_by_name['email']._serialized_options = b'\372B\004r\002`\001'
  _globals['_GETEMAILVERIFYCODEREQUEST'].fields_by_name['email']._loaded_options = None
  _globals['_GETEMAILVERIFYCODEREQUEST'].fields_by_name['email']._serialized_options = b'\372B\004r\002`\001'
  _globals['_USERSERVICE'].methods_by_name['GetEmailVerifyCode']._loaded_options = None
  _globals['_USERSERVICE'].methods_by_name['GetEmailVerifyCode']._serialized_options = b'\202\323\344\223\002 \022\036/api/v1/user/email_verify_code'
  _globals['_USERSERVICE'].methods_by_name['CreateUser']._loaded_options = None
  _globals['_USERSERVICE'].methods_by_name['CreateUser']._serialized_options = b'\202\323\344\223\002\030\"\023/api/v1/user/create:\001*'
  _globals['_USERSERVICE'].methods_by_name['Login']._loaded_options = None
  _globals['_USERSERVICE'].methods_by_name['Login']._serialized_options = b'\202\323\344\223\002\027\"\022/api/v1/user/login:\001*'
  _globals['_USERSERVICE'].methods_by_name['LoginWithCode']._loaded_options = None
  _globals['_USERSERVICE'].methods_by_name['LoginWithCode']._serialized_options = b'\202\323\344\223\002!\"\034/api/v1/user/login_with_code:\001*'
  _globals['_USERSERVICE'].methods_by_name['RefreshToken']._loaded_options = None
  _globals['_USERSERVICE'].methods_by_name['RefreshToken']._serialized_options = b'\202\323\344\223\002\037\"\032/api/v1/user/refresh_token:\001*'
  _globals['_USERSERVICE'].methods_by_name['GetProfile']._loaded_options = None
  _globals['_USERSERVICE'].methods_by_name['GetProfile']._serialized_options = b'\202\323\344\223\002\026\022\024/api/v1/user/profile'
  _globals['_USERSERVICE'].methods_by_name['UpdateProfile']._loaded_options = None
  _globals['_USERSERVICE'].methods_by_name['UpdateProfile']._serialized_options = b'\202\323\344\223\002\031\"\024/api/v1/user/profile:\001*'
  _globals['_USERSERVICE'].methods_by_name['ResetPassword']._loaded_options = None
  _globals['_USERSERVICE'].methods_by_name['ResetPassword']._serialized_options = b'\202\323\344\223\002 \"\033/api/v1/user/reset_password:\001*'
  _globals['_USERSERVICE'].methods_by_name['UpdateSystemPlan']._loaded_options = None
  _globals['_USERSERVICE'].methods_by_name['UpdateSystemPlan']._serialized_options = b'\202\323\344\223\002\032\032\025/api/v1/user/sys/plan:\001*'
  _globals['_USERSERVICE'].methods_by_name['GetSystemPlans']._loaded_options = None
  _globals['_USERSERVICE'].methods_by_name['GetSystemPlans']._serialized_options = b'\202\323\344\223\002\030\022\026/api/v1/user/sys/plans'
  _globals['_USERSERVICE'].methods_by_name['DeleteSystemPlan']._loaded_options = None
  _globals['_USERSERVICE'].methods_by_name['DeleteSystemPlan']._serialized_options = b'\202\323\344\223\002\027*\025/api/v1/user/sys/plan'
  _globals['_USERSERVICE'].methods_by_name['UpdateSystemFeaturePoints']._loaded_options = None
  _globals['_USERSERVICE'].methods_by_name['UpdateSystemFeaturePoints']._serialized_options = b'\202\323\344\223\002$\032\037/api/v1/user/sys/feature_points:\001*'
  _globals['_USERSERVICE'].methods_by_name['GetSystemFeaturePoints']._loaded_options = None
  _globals['_USERSERVICE'].methods_by_name['GetSystemFeaturePoints']._serialized_options = b'\202\323\344\223\002!\022\037/api/v1/user/sys/feature_points'
  _globals['_USERSERVICE'].methods_by_name['DeleteSystemFeaturePoints']._loaded_options = None
  _globals['_USERSERVICE'].methods_by_name['DeleteSystemFeaturePoints']._serialized_options = b'\202\323\344\223\002!*\037/api/v1/user/sys/feature_points'
  _globals['_USERSERVICE'].methods_by_name['GetPlan']._loaded_options = None
  _globals['_USERSERVICE'].methods_by_name['GetPlan']._serialized_options = b'\202\323\344\223\002\023\022\021/api/v1/user/plan'
  _globals['_USERSERVICE'].methods_by_name['ReducePlan']._loaded_options = None
  _globals['_USERSERVICE'].methods_by_name['ReducePlan']._serialized_options = b'\202\323\344\223\002\035\"\030/api/v1/user/plan/reduce:\001*'
  _globals['_USERSERVICE'].methods_by_name['RollbackHalfPointBenefit']._loaded_options = None
  _globals['_USERSERVICE'].methods_by_name['RollbackHalfPointBenefit']._serialized_options = b'\202\323\344\223\002\037\"\032/api/v1/user/plan/rollback:\001*'
  _globals['_USERSERVICE'].methods_by_name['BenefitRecord']._loaded_options = None
  _globals['_USERSERVICE'].methods_by_name['BenefitRecord']._serialized_options = b'\202\323\344\223\002\032\022\030/api/v1/user/plan/record'
  _globals['_USERSERVICE'].methods_by_name['ChargePoints']._loaded_options = None
  _globals['_USERSERVICE'].methods_by_name['ChargePoints']._serialized_options = b'\202\323\344\223\002\030\"\023/api/v1/user/charge:\001*'
  _globals['_USERSERVICE'].methods_by_name['Delete']._loaded_options = None
  _globals['_USERSERVICE'].methods_by_name['Delete']._serialized_options = b'\202\323\344\223\002\016*\014/api/v1/user'
  _globals['_USERSERVICE'].methods_by_name['Search']._loaded_options = None
  _globals['_USERSERVICE'].methods_by_name['Search']._serialized_options = b'\202\323\344\223\002\025\022\023/api/v1/user/search'
  _globals['_CHARGEREQUEST']._serialized_start=89
  _globals['_CHARGEREQUEST']._serialized_end=164
  _globals['_PAGEREQUEST']._serialized_start=166
  _globals['_PAGEREQUEST']._serialized_end=237
  _globals['_PAGERESPONSE']._serialized_start=239
  _globals['_PAGERESPONSE']._serialized_end=301
  _globals['_BENEFITRECORDRESPONSE']._serialized_start=303
  _globals['_BENEFITRECORDRESPONSE']._serialized_end=430
  _globals['_BENEFITRECORD']._serialized_start=433
  _globals['_BENEFITRECORD']._serialized_end=608
  _globals['_SEARCHREQUEST']._serialized_start=611
  _globals['_SEARCHREQUEST']._serialized_end=783
  _globals['_SEARCHRESPONSE']._serialized_start=785
  _globals['_SEARCHRESPONSE']._serialized_end=906
  _globals['_EMPTY']._serialized_start=908
  _globals['_EMPTY']._serialized_end=915
  _globals['_DELETESYSTEMPLANREQUEST']._serialized_start=917
  _globals['_DELETESYSTEMPLANREQUEST']._serialized_end=968
  _globals['_DELETESYSTEMFEATUREPOINTSREQUEST']._serialized_start=970
  _globals['_DELETESYSTEMFEATUREPOINTSREQUEST']._serialized_end=1025
  _globals['_SYSTEMPLANS']._serialized_start=1027
  _globals['_SYSTEMPLANS']._serialized_end=1089
  _globals['_SYSTEMFEATUREPOINTS']._serialized_start=1092
  _globals['_SYSTEMFEATUREPOINTS']._serialized_end=1314
  _globals['_GETSYSTEMFEATUREPOINTSRESPONSE']._serialized_start=1316
  _globals['_GETSYSTEMFEATUREPOINTSRESPONSE']._serialized_end=1408
  _globals['_SYSTEMPLAN']._serialized_start=1411
  _globals['_SYSTEMPLAN']._serialized_end=1704
  _globals['_USERPLAN']._serialized_start=1707
  _globals['_USERPLAN']._serialized_end=1942
  _globals['_REDUCEBENEFITREQUEST']._serialized_start=1945
  _globals['_REDUCEBENEFITREQUEST']._serialized_end=2114
  _globals['_BENEFITORDER']._serialized_start=2116
  _globals['_BENEFITORDER']._serialized_end=2148
  _globals['_DEVICEINFO']._serialized_start=2151
  _globals['_DEVICEINFO']._serialized_end=2282
  _globals['_CREATEUSERREQUEST']._serialized_start=2285
  _globals['_CREATEUSERREQUEST']._serialized_end=2540
  _globals['_LOGINREQUEST']._serialized_start=2542
  _globals['_LOGINREQUEST']._serialized_end=2598
  _globals['_LOGINRESPONSE']._serialized_start=2600
  _globals['_LOGINRESPONSE']._serialized_end=2679
  _globals['_LOGINWITHCODEREQUEST']._serialized_start=2681
  _globals['_LOGINWITHCODEREQUEST']._serialized_end=2787
  _globals['_UPDATEPASSWORDREQUEST']._serialized_start=2789
  _globals['_UPDATEPASSWORDREQUEST']._serialized_end=2856
  _globals['_RESETPASSWORDREQUEST']._serialized_start=2859
  _globals['_RESETPASSWORDREQUEST']._serialized_end=2987
  _globals['_GETPROFILEREQUEST']._serialized_start=2989
  _globals['_GETPROFILEREQUEST']._serialized_end=3008
  _globals['_USERPROFILE']._serialized_start=3011
  _globals['_USERPROFILE']._serialized_end=3220
  _globals['_DETAILPROFILE']._serialized_start=3222
  _globals['_DETAILPROFILE']._serialized_end=3306
  _globals['_UPDATEPROFILEREQUEST']._serialized_start=3308
  _globals['_UPDATEPROFILEREQUEST']._serialized_end=3380
  _globals['_GETEMAILVERIFYCODEREQUEST']._serialized_start=3382
  _globals['_GETEMAILVERIFYCODEREQUEST']._serialized_end=3433
  _globals['_GETEMAILVERIFYCODERESPONSE']._serialized_start=3435
  _globals['_GETEMAILVERIFYCODERESPONSE']._serialized_end=3523
  _globals['_USERSERVICE']._serialized_start=3526
  _globals['_USERSERVICE']._serialized_end=6204
# @@protoc_insertion_point(module_scope)
