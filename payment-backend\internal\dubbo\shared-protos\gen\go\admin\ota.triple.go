// Code generated by protoc-gen-triple. DO NOT EDIT.
//
// Source: protos/ota.proto
package admin

import (
	"context"
)

import (
	"dubbo.apache.org/dubbo-go/v3"
	"dubbo.apache.org/dubbo-go/v3/client"
	"dubbo.apache.org/dubbo-go/v3/common"
	"dubbo.apache.org/dubbo-go/v3/common/constant"
	"dubbo.apache.org/dubbo-go/v3/protocol/triple/triple_protocol"
	"dubbo.apache.org/dubbo-go/v3/server"
)

// This is a compile-time assertion to ensure that this generated file and the Triple package
// are compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of Triple newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of Triple or updating the Triple
// version compiled into your binary.
const _ = triple_protocol.IsAtLeastVersion0_1_0

const (
	// OtaServiceName is the fully-qualified name of the OtaService service.
	OtaServiceName = "com.aibook.admin.grpc.OtaService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// OtaServiceCheckUpdateProcedure is the fully-qualified name of the OtaService's CheckUpdate RPC.
	OtaServiceCheckUpdateProcedure = "/com.aibook.admin.grpc.OtaService/CheckUpdate"
)

var (
	_ OtaService = (*OtaServiceImpl)(nil)
)

// OtaService is a client for the com.aibook.admin.grpc.OtaService service.
type OtaService interface {
	CheckUpdate(ctx context.Context, req *CheckUpdateRequest, opts ...client.CallOption) (*CheckUpdateResponse, error)
}

// NewOtaService constructs a client for the admin.OtaService service.
func NewOtaService(cli *client.Client, opts ...client.ReferenceOption) (OtaService, error) {
	conn, err := cli.DialWithInfo("com.aibook.admin.grpc.OtaService", &OtaService_ClientInfo, opts...)
	if err != nil {
		return nil, err
	}
	return &OtaServiceImpl{
		conn: conn,
	}, nil
}

func SetConsumerOtaService(srv common.RPCService) {
	dubbo.SetConsumerServiceWithInfo(srv, &OtaService_ClientInfo)
}

// OtaServiceImpl implements OtaService.
type OtaServiceImpl struct {
	conn *client.Connection
}

func (c *OtaServiceImpl) CheckUpdate(ctx context.Context, req *CheckUpdateRequest, opts ...client.CallOption) (*CheckUpdateResponse, error) {
	resp := new(CheckUpdateResponse)
	if err := c.conn.CallUnary(ctx, []interface{}{req}, resp, "CheckUpdate", opts...); err != nil {
		return nil, err
	}
	return resp, nil
}

var OtaService_ClientInfo = client.ClientInfo{
	InterfaceName: "com.aibook.admin.grpc.OtaService",
	MethodNames:   []string{"CheckUpdate"},
	ConnectionInjectFunc: func(dubboCliRaw interface{}, conn *client.Connection) {
		dubboCli := dubboCliRaw.(*OtaServiceImpl)
		dubboCli.conn = conn
	},
}

// OtaServiceHandler is an implementation of the com.aibook.admin.grpc.OtaService service.
type OtaServiceHandler interface {
	CheckUpdate(context.Context, *CheckUpdateRequest) (*CheckUpdateResponse, error)
}

func RegisterOtaServiceHandler(srv *server.Server, hdlr OtaServiceHandler, opts ...server.ServiceOption) error {
	return srv.Register(hdlr, &OtaService_ServiceInfo, opts...)
}

func SetProviderOtaService(srv common.RPCService) {
	dubbo.SetProviderServiceWithInfo(srv, &OtaService_ServiceInfo)
}

var OtaService_ServiceInfo = server.ServiceInfo{
	InterfaceName: "com.aibook.admin.grpc.OtaService",
	ServiceType:   (*OtaServiceHandler)(nil),
	Methods: []server.MethodInfo{
		{
			Name: "CheckUpdate",
			Type: constant.CallUnary,
			ReqInitFunc: func() interface{} {
				return new(CheckUpdateRequest)
			},
			MethodFunc: func(ctx context.Context, args []interface{}, handler interface{}) (interface{}, error) {
				req := args[0].(*CheckUpdateRequest)
				res, err := handler.(OtaServiceHandler).CheckUpdate(ctx, req)
				if err != nil {
					return nil, err
				}
				return triple_protocol.NewResponse(res), nil
			},
		},
	},
}
