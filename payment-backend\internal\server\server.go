package server

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/fx"

	"payment-backend/internal/config"
	"payment-backend/internal/handler"
	storeHandler "payment-backend/internal/handler/store"
	"payment-backend/internal/logger"
	"payment-backend/internal/middleware"
	"payment-backend/internal/router"
)

// Server HTTP服务器
type Server struct {
	server *http.Server
	logger logger.Logger
}

// NewServer 创建HTTP服务器
func NewServer(
	cfg *config.Config,
	orderHandler *handler.OrderHandler,
	storeHandler *storeHandler.PackageHandler,
	lg logger.Logger,
) *Server {

	lg.Info("NewServer HTTP", logger.Object("cfg.Server", cfg.Server))

	// 设置Gin模式
	gin.SetMode(cfg.Server.Mode)

	// 创建路由器
	httpRouter := gin.New()

	// 添加中间件
	httpRouter.Use(gin.Logger())
	httpRouter.Use(gin.Recovery())
	httpRouter.Use(corsMiddleware())
	httpRouter.Use(middleware.RequestIDMiddleware())

	// 注册路由
	httpRouter = router.SetupRoutes(httpRouter, orderHandler, storeHandler, lg, cfg)

	// 创建HTTP服务器
	server := &http.Server{
		Addr:         cfg.Server.GetAddress(),
		Handler:      httpRouter,
		ReadTimeout:  time.Duration(cfg.Server.ReadTimeout) * time.Second,
		WriteTimeout: time.Duration(cfg.Server.WriteTimeout) * time.Second,
	}

	return &Server{
		server: server,
		logger: lg,
	}
}

// Start 启动服务器
func (s *Server) Start() error {
	s.logger.Debug("===================== Starting HTTP server (Debug)", logger.String("address", s.server.Addr))
	s.logger.Info("===================== Starting HTTP server (Info)", logger.String("address", s.server.Addr))
	s.logger.Warn("=====================>>>>>>>>>>>>>>>>>>>>>>>>>>>> Starting HTTP server (Warn)", logger.String("address", s.server.Addr))

	if err := s.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
		return fmt.Errorf("failed to start server: %w", err)
	}

	return nil
}

// Stop 停止服务器
func (s *Server) Stop(ctx context.Context) error {
	s.logger.Info("Stopping HTTP server")
	return s.server.Shutdown(ctx)
}

// corsMiddleware CORS中间件
func corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, X-Signature")
		c.Header("Access-Control-Expose-Headers", "Content-Length")
		c.Header("Access-Control-Allow-Credentials", "true")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}

// ServerModule fx模块
var ServerModule = fx.Module("server",
	fx.Provide(NewServer),
	fx.Invoke(func(lc fx.Lifecycle, server *Server) {
		lc.Append(fx.Hook{
			OnStart: func(ctx context.Context) error {
				go func() {
					if err := server.Start(); err != nil {
						server.logger.Error("Server start failed", logger.Error(err))
					}
				}()
				return nil
			},
			OnStop: func(ctx context.Context) error {
				return server.Stop(ctx)
			},
		})
	}),
)
