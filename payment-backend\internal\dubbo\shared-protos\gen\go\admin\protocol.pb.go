//*
//后台管理平台基础服务定义

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.31.1
// source: protos/protocol.proto

package admin

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ProtocolRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 国家/地区
	Country string `protobuf:"bytes,1,opt,name=country,proto3" json:"country,omitempty"`
	// 协议类型
	// 1：用户隐私协议 2：支付协议 3：创作绘本协议
	Type          int32 `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ProtocolRequest) Reset() {
	*x = ProtocolRequest{}
	mi := &file_protos_protocol_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProtocolRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProtocolRequest) ProtoMessage() {}

func (x *ProtocolRequest) ProtoReflect() protoreflect.Message {
	mi := &file_protos_protocol_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProtocolRequest.ProtoReflect.Descriptor instead.
func (*ProtocolRequest) Descriptor() ([]byte, []int) {
	return file_protos_protocol_proto_rawDescGZIP(), []int{0}
}

func (x *ProtocolRequest) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *ProtocolRequest) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

type ProtocolResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 协议内容，为富文本格式，带段落标识
	Content string `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	// 协议版本，一般获取的都是最新版本
	Version       int32 `protobuf:"varint,2,opt,name=version,proto3" json:"version,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ProtocolResponse) Reset() {
	*x = ProtocolResponse{}
	mi := &file_protos_protocol_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProtocolResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProtocolResponse) ProtoMessage() {}

func (x *ProtocolResponse) ProtoReflect() protoreflect.Message {
	mi := &file_protos_protocol_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProtocolResponse.ProtoReflect.Descriptor instead.
func (*ProtocolResponse) Descriptor() ([]byte, []int) {
	return file_protos_protocol_proto_rawDescGZIP(), []int{1}
}

func (x *ProtocolResponse) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *ProtocolResponse) GetVersion() int32 {
	if x != nil {
		return x.Version
	}
	return 0
}

var File_protos_protocol_proto protoreflect.FileDescriptor

const file_protos_protocol_proto_rawDesc = "" +
	"\n" +
	"\x15protos/protocol.proto\x12\x15com.aibook.admin.grpc\x1a\x1cgoogle/api/annotations.proto\"?\n" +
	"\x0fProtocolRequest\x12\x18\n" +
	"\acountry\x18\x01 \x01(\tR\acountry\x12\x12\n" +
	"\x04type\x18\x02 \x01(\x05R\x04type\"F\n" +
	"\x10ProtocolResponse\x12\x18\n" +
	"\acontent\x18\x01 \x01(\tR\acontent\x12\x18\n" +
	"\aversion\x18\x02 \x01(\x05R\aversion2\x91\x01\n" +
	"\x0fProtocolService\x12~\n" +
	"\vgetProtocol\x12&.com.aibook.admin.grpc.ProtocolRequest\x1a'.com.aibook.admin.grpc.ProtocolResponse\"\x1e\x82\xd3\xe4\x93\x02\x18\x12\x16/api/v1/admin/protocolB\x11P\x01Z\r./admin;adminb\x06proto3"

var (
	file_protos_protocol_proto_rawDescOnce sync.Once
	file_protos_protocol_proto_rawDescData []byte
)

func file_protos_protocol_proto_rawDescGZIP() []byte {
	file_protos_protocol_proto_rawDescOnce.Do(func() {
		file_protos_protocol_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_protos_protocol_proto_rawDesc), len(file_protos_protocol_proto_rawDesc)))
	})
	return file_protos_protocol_proto_rawDescData
}

var file_protos_protocol_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_protos_protocol_proto_goTypes = []any{
	(*ProtocolRequest)(nil),  // 0: com.aibook.admin.grpc.ProtocolRequest
	(*ProtocolResponse)(nil), // 1: com.aibook.admin.grpc.ProtocolResponse
}
var file_protos_protocol_proto_depIdxs = []int32{
	0, // 0: com.aibook.admin.grpc.ProtocolService.getProtocol:input_type -> com.aibook.admin.grpc.ProtocolRequest
	1, // 1: com.aibook.admin.grpc.ProtocolService.getProtocol:output_type -> com.aibook.admin.grpc.ProtocolResponse
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_protos_protocol_proto_init() }
func file_protos_protocol_proto_init() {
	if File_protos_protocol_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_protos_protocol_proto_rawDesc), len(file_protos_protocol_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_protos_protocol_proto_goTypes,
		DependencyIndexes: file_protos_protocol_proto_depIdxs,
		MessageInfos:      file_protos_protocol_proto_msgTypes,
	}.Build()
	File_protos_protocol_proto = out.File
	file_protos_protocol_proto_goTypes = nil
	file_protos_protocol_proto_depIdxs = nil
}
