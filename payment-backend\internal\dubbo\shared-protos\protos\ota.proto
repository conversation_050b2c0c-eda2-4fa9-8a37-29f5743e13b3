/**
 * App在线升级(OTA)服务定义
 */
syntax = "proto3";

package com.aibook.admin.grpc;
option java_multiple_files = true;
option go_package = "./admin;admin";

import "google/api/annotations.proto";

// 更新检查请求
message CheckUpdateRequest {
  // 应用ID
  string app_id = 1;
  // 当前版本名
  string version_name = 2;
  // 当前版本号
  int32 version_code = 3;
  // 设备ID
  string device_id = 4;
  // 操作系统版本
  string os_version = 5;
  // 语言
  string language = 6;
}

// 更新检查响应
message CheckUpdateResponse {
  // 是否有新版本
  bool has_update = 1;
  // 是否强制更新
  bool is_force_update = 2;
  // 新版本名
  string version_name = 3;
  // 新版本号
  int32 version_code = 4;
  // 新版本下载地址
  string download_url = 5;
  // 安装包大小 (Bytes)
  int64 package_size = 6;
  // 安装包MD5
  string md5 = 7;
  // 更新日志
  string update_log = 8;
}

// OTA服务
service OtaService {
  // 检查更新
  rpc CheckUpdate(CheckUpdateRequest) returns (CheckUpdateResponse) {
    option (google.api.http) = {
      get : "/api/v1/admin/check-update"
    };
  }

}
