package db

import (
	"fmt"
	"reflect"
	"strings"

	"gorm.io/gorm"
)

// IndexManager 索引管理器
type IndexManager struct {
	db *gorm.DB
}

// NewIndexManager 创建索引管理器
func NewIndexManager(db *gorm.DB) *IndexManager {
	return &IndexManager{db: db}
}

// ExtractIndexesFromModel 从模型中提取索引信息
func (im *IndexManager) ExtractIndexesFromModel(model interface{}) []ModelIndex {
	var indexes []ModelIndex
	
	modelType := reflect.TypeOf(model)
	if modelType.Kind() == reflect.Ptr {
		modelType = modelType.Elem()
	}

	// 获取表名
	tableName := ""
	if tableNamer, ok := model.(interface{ TableName() string }); ok {
		tableName = tableNamer.TableName()
	} else {
		tableName = strings.ToLower(modelType.Name()) + "s"
	}

	// 遍历字段
	for i := 0; i < modelType.NumField(); i++ {
		field := modelType.Field(i)
		gormTag := field.Tag.Get("gorm")
		
		if gormTag == "" {
			continue
		}

		// 解析 gorm 标签
		tags := parseGormTag(gormTag)
		columnName := getColumnName(field, tags)

		// 检查索引类型
		if indexName, exists := tags["index"]; exists {
			indexes = append(indexes, ModelIndex{
				Name:       indexName,
				Table:      tableName,
				Column:     columnName,
				Unique:     false,
				FieldName:  field.Name,
			})
		}

		if indexName, exists := tags["uniqueIndex"]; exists {
			indexes = append(indexes, ModelIndex{
				Name:       indexName,
				Table:      tableName,
				Column:     columnName,
				Unique:     true,
				FieldName:  field.Name,
			})
		}
	}

	return indexes
}

// ModelIndex 模型索引信息
type ModelIndex struct {
	Name      string // 索引名称
	Table     string // 表名
	Column    string // 列名
	Unique    bool   // 是否唯一索引
	FieldName string // 字段名
}

// CreateModelIndexes 创建模型中定义的索引
func (im *IndexManager) CreateModelIndexes(model interface{}) error {
	indexes := im.ExtractIndexesFromModel(model)
	
	for _, idx := range indexes {
		if err := im.createSingleIndex(idx); err != nil {
			return fmt.Errorf("failed to create index %s: %w", idx.Name, err)
		}
	}
	
	return nil
}

// createSingleIndex 创建单个索引
func (im *IndexManager) createSingleIndex(idx ModelIndex) error {
	indexType := "INDEX"
	if idx.Unique {
		indexType = "UNIQUE INDEX"
	}

	sql := fmt.Sprintf("CREATE %s IF NOT EXISTS %s ON %s (%s)",
		indexType, idx.Name, idx.Table, idx.Column)

	return im.db.Exec(sql).Error
}

// DropModelIndexes 删除模型中定义的索引
func (im *IndexManager) DropModelIndexes(model interface{}) error {
	indexes := im.ExtractIndexesFromModel(model)
	
	for _, idx := range indexes {
		sql := fmt.Sprintf("DROP INDEX IF EXISTS %s ON %s", idx.Name, idx.Table)
		if err := im.db.Exec(sql).Error; err != nil {
			return fmt.Errorf("failed to drop index %s: %w", idx.Name, err)
		}
	}
	
	return nil
}

// ListModelIndexes 列出模型中定义的索引
func (im *IndexManager) ListModelIndexes(model interface{}) {
	indexes := im.ExtractIndexesFromModel(model)
	
	fmt.Printf("Indexes for model %T:\n", model)
	for _, idx := range indexes {
		indexType := "INDEX"
		if idx.Unique {
			indexType = "UNIQUE INDEX"
		}
		fmt.Printf("  %s: %s ON %s.%s (field: %s)\n", 
			indexType, idx.Name, idx.Table, idx.Column, idx.FieldName)
	}
}

// parseGormTag 解析 gorm 标签
func parseGormTag(tag string) map[string]string {
	tags := make(map[string]string)
	parts := strings.Split(tag, ";")
	
	for _, part := range parts {
		part = strings.TrimSpace(part)
		if part == "" {
			continue
		}
		
		if strings.Contains(part, ":") {
			kv := strings.SplitN(part, ":", 2)
			tags[kv[0]] = kv[1]
		} else {
			tags[part] = ""
		}
	}
	
	return tags
}

// getColumnName 获取列名
func getColumnName(field reflect.StructField, tags map[string]string) string {
	if column, exists := tags["column"]; exists {
		return column
	}
	
	// 如果没有指定 column，使用字段名的蛇形命名
	return toSnakeCase(field.Name)
}

// toSnakeCase 转换为蛇形命名
func toSnakeCase(str string) string {
	var result strings.Builder
	for i, r := range str {
		if i > 0 && r >= 'A' && r <= 'Z' {
			result.WriteByte('_')
		}
		result.WriteRune(r)
	}
	return strings.ToLower(result.String())
}

// ValidateIndexes 验证索引是否存在
func (im *IndexManager) ValidateIndexes(model interface{}) error {
	indexes := im.ExtractIndexesFromModel(model)
	
	for _, idx := range indexes {
		exists, err := im.indexExists(idx.Table, idx.Name)
		if err != nil {
			return fmt.Errorf("failed to check index %s: %w", idx.Name, err)
		}
		
		if !exists {
			return fmt.Errorf("index %s does not exist on table %s", idx.Name, idx.Table)
		}
	}
	
	return nil
}

// indexExists 检查索引是否存在
func (im *IndexManager) indexExists(tableName, indexName string) (bool, error) {
	var count int64
	err := im.db.Raw("SELECT COUNT(*) FROM information_schema.statistics WHERE table_schema = DATABASE() AND table_name = ? AND index_name = ?", 
		tableName, indexName).Scan(&count).Error
	
	return count > 0, err
}
