#!/bin/bash

echo "Generating Swagger documentation..."

# 检查 swag 命令是否存在
if ! command -v swag &> /dev/null; then
    echo "Installing swag..."
    go install github.com/swaggo/swag/cmd/swag@latest
    if [ $? -ne 0 ]; then
        echo "Failed to install swag"
        exit 1
    fi
fi

# 生成 Swagger 文档
echo "Running swag init..."
swag init -g main.go -o docs/api/
if [ $? -ne 0 ]; then
    echo "Failed to generate Swagger documentation"
    exit 1
fi

echo "Swagger documentation generated successfully!"
echo "Files generated:"
echo "  - docs/api/docs.go"
echo "  - docs/api/swagger.json"
echo "  - docs/api/swagger.yaml"

echo ""
echo "You can now access the API documentation at:"
echo "  http://localhost:8080/swagger/index.html"
