package main

import (
	"fmt"
	"io"
	"time"
)

// testListAllPackagesExternal 测试外网获取流量包列表接口 (Gin HTTP)
func testListAllPackagesExternal(config *EnvConfig, token string) TestResult {
	start := time.Now()
	testName := "List All Packages (External Gin HTTP)"

	url := config.ExternalURL + "/api/v1/pay-service/store-service/packages?limit=50&offset=0"
	headers := map[string]string{
		"x-trace-id":    generateTraceID(),
		"Authorization": "Bearer " + token,
	}

	// 记录详细请求信息到日志文件
	if detailLogger != nil {
		detailLogger.Printf("--- %s ---", testName)
		detailLogger.Printf("URL: %s", url)
		detailLogger.Printf("Headers: %+v", headers)
	}

	resp, err := makeHTTPRequest("GET", url, headers, nil)
	duration := time.Since(start)

	if err != nil {
		if detailLogger != nil {
			detailLogger.Printf("Request failed: %v", err)
		}
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("Request failed: %v", err),
			Duration: duration,
		}
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		if detailLogger != nil {
			detailLogger.Printf("Failed to read response: %v", err)
		}
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("Failed to read response: %v", err),
			Duration: duration,
		}
	}

	// 记录详细响应信息到日志文件
	if detailLogger != nil {
		detailLogger.Printf("Response Status: %d", resp.StatusCode)
		detailLogger.Printf("Response Body: %s", prettyJson(body))
	}

	// 简要信息输出到控制台
	fmt.Printf("  %s: Status=%d\n", testName, resp.StatusCode)
	if resp.StatusCode == 200 {
		return TestResult{
			TestName: testName,
			Success:  true,
			Message:  "Packages retrieved successfully via External Gin HTTP",
			Duration: duration,
		}
	} else {
		if detailLogger != nil {
			detailLogger.Printf("Request failed with HTTP %d: %s", resp.StatusCode, string(body))
		}
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("HTTP %d: %s", resp.StatusCode, string(body)),
			Duration: duration,
		}
	}
}

// testListAllPackagesExternalWithFilters 测试外网获取流量包列表接口（带过滤条件）
func testListAllPackagesExternalWithFilters(config *EnvConfig, token string) TestResult {
	start := time.Now()
	testName := "List All Packages with Filters (External Gin HTTP)"

	url := config.ExternalURL + "/api/v1/pay-service/store-service/packages?currency=USD&country=US&limit=20&offset=0"
	headers := map[string]string{
		"x-trace-id":    generateTraceID(),
		"Authorization": "Bearer " + token,
	}

	// 记录详细请求信息到日志文件
	if detailLogger != nil {
		detailLogger.Printf("--- %s ---", testName)
		detailLogger.Printf("URL: %s", url)
		detailLogger.Printf("Headers: %+v", headers)
	}

	resp, err := makeHTTPRequest("GET", url, headers, nil)
	duration := time.Since(start)

	if err != nil {
		if detailLogger != nil {
			detailLogger.Printf("Request failed: %v", err)
		}
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("Request failed: %v", err),
			Duration: duration,
		}
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		if detailLogger != nil {
			detailLogger.Printf("Failed to read response: %v", err)
		}
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("Failed to read response: %v", err),
			Duration: duration,
		}
	}

	// 记录详细响应信息到日志文件
	if detailLogger != nil {
		detailLogger.Printf("Response Status: %d", resp.StatusCode)
		detailLogger.Printf("Response Body: %s", prettyJson(body))
	}

	// 简要信息输出到控制台
	fmt.Printf("  %s: Status=%d\n", testName, resp.StatusCode)
	if resp.StatusCode == 200 {
		return TestResult{
			TestName: testName,
			Success:  true,
			Message:  "Filtered packages retrieved successfully via External Gin HTTP",
			Duration: duration,
		}
	} else {
		if detailLogger != nil {
			detailLogger.Printf("Request failed with HTTP %d: %s", resp.StatusCode, string(body))
		}
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("HTTP %d: %s", resp.StatusCode, string(body)),
			Duration: duration,
		}
	}
}
