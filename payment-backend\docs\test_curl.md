# API 测试 curl 命令集合

本文档包含了支付后端服务的各种 API 接口的 curl 测试命令，便于开发和测试使用。

[TOC]

## 目录
- [环境变量设置](#环境变量设置)
- [订单管理接口](#订单管理接口)
- [流量包管理接口](#流量包管理接口)
  - [终端用户接口](#终端用户接口)
  - [管理员接口](#管理员接口)

## 环境变量设置

### Linux/macOS (Bash/Zsh)

```bash
# 设置服务器地址（可选，默认为 localhost:8080）
# export SERVER_HOST=http://localhost:8080
# export SERVER_HOST=http://**************:8080
export SERVER_HOST=http://**************:8080

# 或者设置为其他地址
# export SERVER_HOST=http://*************:8080
# export SERVER_HOST=https://api.example.com
```

### Windows PowerShell

```powershell
# 设置服务器地址（可选，默认为 localhost:8080）
$env:SERVER_HOST = "http://localhost:8080"

# 或者设置为其他地址
# $env:SERVER_HOST = "http://*************:8080"
# $env:SERVER_HOST = "https://api.example.com"
```

## 1 创建订单

### 创建订单 - Linux/macOS [本地开发测试]

```bash
curl -X POST "${SERVER_HOST:-http://localhost:8080}/api/v1/pay-service/order-service/orders" \
  -H "Content-Type: application/json" \
  -H "x-trace-id: 12340001" \
  -H "x-user-id: user123" \
  -H "x-role: customer" \
  -d '{
    "product_id": "prod_stripe_001",
    "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
    "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
    "quantity": 1,
    "psp_provider": "stripe"
  }'
```

### 创建订单 - Linux/macOS [dev1 环境外网测试]

```bash
export SERVER_HOST=http://ny10wt9045294.vicp.fun:25639

curl -v -X POST "${SERVER_HOST:-http://localhost:8080}/api/v1/pay-service/order-service/orders" \
  -H "Content-Type: application/json" \
  -H "x-trace-id: 12340001" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************.eB4reqDA4b8-pjK6oeStqVuzjryl_DYDiipwuHqIK0M" \
  -d '{
    "product_id": "prod_stripe_001",
    "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
    "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
    "quantity": 1,
    "psp_provider": "stripe"
  }'
```

### 预期响应

```json
{
	"order_id": "20250711145912STRIPE1943565722550534144",
	"checkout_url": "https://checkout.stripe.com/c/pay/cs_test_a1auhmrzLokvHFHOHdEJVvyHTARBC9iuyLLfOpLIF70MsRfv9VPtS3DPmc#fidkdWxOYHwnPyd1blpxYHZxWjA0V2dhc0ZGMDZIRGkzUmh0a2loPV1zM3NjUWNjfVJHbGB%2FXDFkbWM0cW90QFFzVjJ%2FbDNfRnI0NmRoVmFpMnJUdXxidUZxPUJJQ0lpY0dEQW18f0tBZFJxNTVLV2hsbFxQSScpJ2N3amhWYHdzYHcnP3F3cGApJ2lkfGpwcVF8dWAnPyd2bGtiaWBabHFgaCcpJ2BrZGdpYFVpZGZgbWppYWB3dic%2FcXdwYHgl",
	"amount": 0,
	"currency": "",
	"expires_at": "2025-07-12T14:59:13.4290951+08:00"
}
```

### 创建订单外网执行示例

```bash
[root@openEuler ~]# curl -X POST "${SERVER_HOST:-http://localhost:8080}/api/v1/pay-service/order-service/orders"   -H "Content-Type: application/json"   -H "x-trace-id: 12340001"   -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************.eB4reqDA4b8-pjK6oeStqVuzjryl_DYDiipwuHqIK0M"   -d '{
    "product_id": "prod_stripe_001",
    "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
    "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
    "quantity": 1,
    "psp_provider": "stripe"
  }'
  
{
	"order_id": "202507161517268684STRIPE1945382250535194624",
	"checkout_url": "https://checkout.stripe.com/c/pay/cs_test_a1GS8yAosFXZAtAKJuGqKMMt2fA42NOI81pDIWtn666NJ2H9jrCuh7s7U4#fidkdWxOYHwnPyd1blpxYHZxWjA0V2dhc0ZGMDZIRGkzUmh0a2loPV1zM3NjUWNjfVJHbGB%2FXDFkbWM0cW90QFFzVjJ%2FbDNfRnI0NmRoVmFpMnJUdXxidUZxPUJJQ0lpY0dEQW18f0tBZFJxNTVLV2hsbFxQSScpJ2N3amhWYHdzYHcnP3F3cGApJ2lkfGpwcVF8dWAnPyd2bGtiaWBabHFgaCcpJ2BrZGdpYFVpZGZgbWppYWB3dic%2FcXdwYHgl",
	"amount": 0,
	"currency": "",
	"expires_at": "2025-07-17T15:17:27.233307651+08:00"
}
```

## 2 获取批量订单(运营人员, 内网)

### 获取所有订单（默认分页）

```bash
curl -X GET "${SERVER_HOST:-http://localhost:8080}/api/v1/pay-service/pay-service/admin/orders" \
  -H "X-User-ID: admin123" \
  -H "X-Role: admin"
```

### 按用户ID过滤

```bash
curl -X GET "${SERVER_HOST:-http://localhost:8080}/api/v1/pay-service/admin/orders?user_id=user123" \
  -H "X-User-ID: admin123" \
  -H "X-Role: admin"
```

### 按支付状态和货币过滤

```bash
curl -X GET "${SERVER_HOST:-http://localhost:8080}/api/v1/pay-service/admin/orders?pay_status=succeeded&currency=USD" \
  -H "X-User-ID: admin123" \
  -H "X-Role: admin"
```

### 按订单创建时间范围过滤

```bash
curl -X GET "${SERVER_HOST:-http://localhost:8080}/api/v1/pay-service/admin/orders?limit=50&offset=0pay_status=created&created_at_start=2025-07-01T00:00:00Z&created_at_end=2025-07-31T23:59:59Z"
```

### 自定义分页

```bash
curl -X GET "${SERVER_HOST:-http://localhost:8080}/api/v1/pay-service/admin/orders?limit=0&offset=100" \
  -H "X-User-ID: admin123" \
  -H "X-Role: admin"
```

### 复合过滤条件

```bash
curl -X GET "http://localhost:8080/api/v1/pay-service/admin/orders?psp_provider=stripe&pay_status=succeeded&limit=10&offset=0" \
  -H "X-User-ID: admin123" \
  -H "X-Role: admin"
```

### 退款订单 (POST /orders/:order_id/refund)

**Linux/macOS:**
```bash
# 全额退款
curl -X POST "${SERVER_HOST:-http://localhost:8080}/api/v1/order-service/orders/20250710153045999stripe**********123456789/refund" \
  -H "Content-Type: application/json" \
  -H "x-user-id: user123" \
  -H "x-role: customer" \
  -d '{}'

# 部分退款
curl -X POST "${SERVER_HOST:-http://localhost:8080}/api/v1/order-service/orders/20250710153045999stripe**********123456789/refund" \
  -H "Content-Type: application/json" \
  -H "x-user-id: user123" \
  -H "x-role: customer" \
  -d '{
    "amount": 50.00,
    "reason": "Partial refund requested by customer"
  }'
```

## 3 Webhook 接口

### 处理支付提供商 Webhook (POST /webhooks/:provider)

**Linux/macOS:**
```bash
# Stripe Webhook
curl -X POST "${SERVER_HOST:-http://localhost:8080}/api/v1/order-service/webhooks/stripe" \
  -H "Content-Type: application/json" \
  -H "Stripe-Signature: t=**********,v1=signature_here" \
  -d '{
    "id": "evt_test_webhook",
    "object": "event",
    "type": "checkout.session.completed",
    "data": {
      "object": {
        "id": "cs_test_session_id",
        "payment_status": "paid"
      }
    }
  }'

# PayPal Webhook
curl -X POST "${SERVER_HOST:-http://localhost:8080}/api/v1/order-service/webhooks/paypal" \
  -H "Content-Type: application/json" \
  -H "PayPal-Transmission-Id: webhook_transmission_id" \
  -d '{
    "event_type": "PAYMENT.CAPTURE.COMPLETED",
    "resource": {
      "id": "payment_id_here",
      "status": "COMPLETED"
    }
  }'
```

## 4 获取用户订单(终端调用 外网)

```
C:\file\program\curl-8.1.2_3-win64-mingw\bin\curl -X GET -H "Content-Type: application/json" -H "x-user-id: user123" -H "x-role: customer" -H "x-trace-id: 123450001" "http://*************:15445/api/v1/pay-service/order-service/orders?limit=50&offset=0pay_status=created&created_at_start=2025-07-01T00:00:00Z&created_at_end=2025-07-31T23:59:59Z"
```

## 错误测试用例

### 1. 缺少认证头部

**Linux/macOS:**
```bash
# 缺少 x-user-id
curl -X POST "${SERVER_HOST:-http://localhost:8080}/api/v1/order-service/orders" \
  -H "Content-Type: application/json" \
  -H "x-role: customer" \
  -d '{
    "product_id": "prod_123",
    "price_id": "price_456",
    "quantity": 1,
    "psp_provider": "stripe"
  }'

# 预期响应: 401 Unauthorized
```

**Windows PowerShell:**
```powershell
$serverHost = if ($env:SERVER_HOST) { $env:SERVER_HOST } else { "http://localhost:8080" }

# 缺少 x-user-id
curl -X POST "$serverHost/api/v1/order-service/orders" `
  -H "Content-Type: application/json" `
  -H "x-role: customer" `
  -d '{
    "product_id": "prod_123",
    "price_id": "price_456",
    "quantity": 1,
    "psp_provider": "stripe"
  }'

# 预期响应: 401 Unauthorized
```

### 2. 无效的请求数据

**Linux/macOS:**
```bash
# 缺少必需字段
curl -X POST "${SERVER_HOST:-http://localhost:8080}/api/v1/order-service/orders" \
  -H "Content-Type: application/json" \
  -H "x-user-id: user123" \
  -H "x-role: customer" \
  -d '{
    "product_id": "prod_123"
  }'

# 预期响应: 400 Bad Request
```

### 3. 不支持的支付提供商

**Linux/macOS:**
```bash
curl -X POST "${SERVER_HOST:-http://localhost:8080}/api/v1/order-service/orders" \
  -H "Content-Type: application/json" \
  -H "x-user-id: user123" \
  -H "x-role: customer" \
  -d '{
    "product_id": "prod_123",
    "price_id": "price_456",
    "quantity": 1,
    "psp_provider": "unsupported_provider"
  }'

# 预期响应: 500 Internal Server Error
```


## 检查数据库

```bash
docker exec -it mysql-dev bash
mysql -uroot -p123456
use aibook_payment;
show tables;
```

## 测试脚本

### 快速测试脚本

**Linux/macOS - 创建 `test_orders.sh`：**

```bash
#!/bin/bash

# 设置服务器地址
SERVER_HOST=${SERVER_HOST:-http://localhost:8080}

echo "Testing Order API..."
echo "Server: $SERVER_HOST"
echo

# 测试创建订单
echo "1. Creating order..."
ORDER_RESPONSE=$(curl -s -X POST "$SERVER_HOST/api/v1/order-service/orders" \
  -H "Content-Type: application/json" \
  -H "x-user-id: test_user" \
  -H "x-role: customer" \
  -d '{
    "product_id": "prod_test",
    "product_desc": "Test Product",
    "price_id": "price_test",
    "quantity": 1,
    "currency": "USD",
    "payed_method": "stripe",
    "psp_provider": "stripe"
  }')

echo "Response: $ORDER_RESPONSE"
echo

# 提取订单ID（需要 jq 工具）
if command -v jq &> /dev/null; then
    ORDER_ID=$(echo "$ORDER_RESPONSE" | jq -r '.order_id')
    echo "Order ID: $ORDER_ID"

    if [ "$ORDER_ID" != "null" ] && [ "$ORDER_ID" != "" ]; then
        echo "2. Getting order details..."
        curl -s -X GET "$SERVER_HOST/api/v1/order-service/orders/$ORDER_ID" \
          -H "x-user-id: test_user" \
          -H "x-role: customer" | jq '.'
    fi
fi
```

**Windows PowerShell - 创建 `test_orders.ps1`：**

```powershell
# 设置服务器地址
$serverHost = if ($env:SERVER_HOST) { $env:SERVER_HOST } else { "http://localhost:8080" }

Write-Host "Testing Order API..."
Write-Host "Server: $serverHost"
Write-Host

# 测试创建订单
Write-Host "1. Creating order..."
$orderResponse = curl -s -X POST "$serverHost/api/v1/order-service/orders" `
  -H "Content-Type: application/json" `
  -H "x-user-id: test_user" `
  -H "x-role: customer" `
  -d '{
    "product_id": "prod_test",
    "product_desc": "Test Product",
    "price_id": "price_test",
    "quantity": 1,
    "currency": "USD",
    "payed_method": "stripe",
    "psp_provider": "stripe"
  }'

Write-Host "Response: $orderResponse"
Write-Host

# 提取订单ID（需要安装 jq 或使用 PowerShell 的 ConvertFrom-Json）
try {
    $responseObj = $orderResponse | ConvertFrom-Json
    $orderId = $responseObj.order_id
    Write-Host "Order ID: $orderId"

    if ($orderId -and $orderId -ne "null") {
        Write-Host "2. Getting order details..."
        $orderDetails = curl -s -X GET "$serverHost/api/v1/order-service/orders/$orderId" `
          -H "x-user-id: test_user" `
          -H "x-role: customer"

        $orderDetails | ConvertFrom-Json | ConvertTo-Json -Depth 10
    }
} catch {
    Write-Host "Failed to parse JSON response: $_"
}
```

## 注意事项

### 通用注意事项

1. **认证头部**: 所有需要认证的接口都必须包含 `x-user-id` 和 `x-role` 头部
2. **环境变量**: 使用 `SERVER_HOST` 环境变量可以灵活切换测试环境
3. **数据格式**: 请求体必须是有效的 JSON 格式
4. **错误处理**: 注意检查响应状态码和错误信息
5. **订单ID**: 创建订单后返回的 `order_id` 用于后续操作

### Windows PowerShell 特殊注意事项

1. **curl 安装**: 确保已安装 curl

## 性能测试

### 并发创建订单测试

```bash
# 使用 GNU parallel 进行并发测试（需要安装 parallel）
seq 1 10 | parallel -j 5 curl -s -X POST "${SERVER_HOST:-http://localhost:8080}/api/v1/order-service/orders" \
  -H "Content-Type: application/json" \
  -H "x-user-id: user{}" \
  -H "x-role: customer" \
  -d '{
    "product_id": "prod_load_test",
    "product_desc": "Load Test Product {}",
    "price_id": "price_load_test",
    "quantity": 1,
    "currency": "USD",
    "payed_method": "stripe",
    "psp_provider": "stripe"
  }'
```

### 压力测试脚本

```bash
#!/bin/bash
# stress_test.sh

SERVER_HOST=${SERVER_HOST:-http://localhost:8080}
CONCURRENT_USERS=10
REQUESTS_PER_USER=5

echo "Starting stress test..."
echo "Server: $SERVER_HOST"
echo "Concurrent users: $CONCURRENT_USERS"
echo "Requests per user: $REQUESTS_PER_USER"

for i in $(seq 1 $CONCURRENT_USERS); do
  {
    for j in $(seq 1 $REQUESTS_PER_USER); do
      curl -s -w "%{http_code} %{time_total}s\n" \
        -X POST "$SERVER_HOST/api/v1/order-service/orders" \
        -H "Content-Type: application/json" \
        -H "x-user-id: stress_user_$i" \
        -H "x-role: customer" \
        -d "{
          \"product_id\": \"prod_stress_$i\",
          \"product_desc\": \"Stress Test Product $i-$j\",
          \"price_id\": \"price_stress\",
          \"quantity\": 1,
          \"currency\": \"USD\",
          \"payed_method\": \"stripe\",
          \"psp_provider\": \"stripe\"
        }" > /dev/null
      echo "User $i Request $j completed"
    done
  } &
done

wait
echo "Stress test completed"
```


## 调试技巧

### 1. 详细输出

```bash
# 显示详细的请求和响应信息
curl -v -X POST "${SERVER_HOST:-http://localhost:8080}/api/v1/order-service/orders" \
  -H "Content-Type: application/json" \
  -H "x-user-id: debug_user" \
  -H "x-role: customer" \
  -d '{
    "product_id": "debug_prod",
    "price_id": "debug_price",
    "quantity": 1,
    "psp_provider": "stripe"
  }'
```

### 2. 保存响应到文件

```bash
# 保存响应头和响应体
curl -D headers.txt -o response.json \
  -X POST "${SERVER_HOST:-http://localhost:8080}/api/v1/order-service/orders" \
  -H "Content-Type: application/json" \
  -H "x-user-id: file_user" \
  -H "x-role: customer" \
  -d '{
    "product_id": "file_prod",
    "price_id": "file_price",
    "quantity": 1,
    "psp_provider": "stripe"
  }'

# 查看保存的文件
cat headers.txt
cat response.json | jq '.'
```

---

## 流量包管理接口

### 终端用户接口

#### 获取流量包列表 - Linux/macOS

```bash
# 基本查询（使用默认分页）
curl -X GET "${SERVER_HOST:-http://localhost:8080}/api/v1/pay-service/store-service/packages" \
  -H "Content-Type: application/json" \
  -H "x-trace-id: pkg_list_001" \
  -H "x-user-id: user123" \
  -H "x-role: customer"

# 按货币查询
curl -X GET "${SERVER_HOST:-http://localhost:8080}/api/v1/pay-service/store-service/packages?currency=USD" \
  -H "Content-Type: application/json" \
  -H "x-trace-id: pkg_list_002" \
  -H "x-user-id: user123" \
  -H "x-role: customer"

# 按国家查询
curl -X GET "${SERVER_HOST:-http://localhost:8080}/api/v1/pay-service/store-service/packages?country=US" \
  -H "Content-Type: application/json" \
  -H "x-trace-id: pkg_list_003" \
  -H "x-user-id: user123" \
  -H "x-role: customer"

# 分页查询
curl -X GET "${SERVER_HOST:-http://localhost:8080}/api/v1/pay-service/store-service/packages?limit=10&offset=0" \
  -H "Content-Type: application/json" \
  -H "x-trace-id: pkg_list_004" \
  -H "x-user-id: user123" \
  -H "x-role: customer"

# 组合查询
curl -X GET "${SERVER_HOST:-http://localhost:8080}/api/v1/pay-service/store-service/packages?currency=USD&country=US&limit=20&offset=0" \
  -H "Content-Type: application/json" \
  -H "x-trace-id: pkg_list_005" \
  -H "x-user-id: user123" \
  -H "x-role: customer"
```

#### 获取流量包列表 - Windows PowerShell

```powershell
# 基本查询（使用默认分页）
$serverHost = if ($env:SERVER_HOST) { $env:SERVER_HOST } else { "http://localhost:8080" }

curl -X GET "$serverHost/api/v1/pay-service/store-service/packages" `
  -H "Content-Type: application/json" `
  -H "x-trace-id: pkg_list_001" `
  -H "x-user-id: user123" `
  -H "x-role: customer"

# 按货币和国家查询
curl -X GET "$serverHost/api/v1/pay-service/store-service/packages?currency=USD&country=US&limit=20&offset=0" `
  -H "Content-Type: application/json" `
  -H "x-trace-id: pkg_list_005" `
  -H "x-user-id: user123" `
  -H "x-role: customer"
```

### 管理员接口

#### 创建流量包 - Linux/macOS

```bash
# 创建基础流量包（只有原价）
curl -X PUT "${SERVER_HOST:-http://localhost:8080}/api/v1/pay-service/admin/store-service/packages" \
  -H "Content-Type: application/json" \
  -H "x-trace-id: pkg_create_001" \
  -d '{
    "package_name": "基础流量包",
    "package_desc": "适合个人用户的基础流量包，包含100GB流量",
    "entitlement": 100,
    "original_price": 9.99,
    "sale_status": "on_sale",
    "currency": "USD",
    "country": "US",
    "extra1": "basic_plan",
    "extra2": "monthly",
    "extra3": 100,
    "extra4": 30
  }'

# 创建有优惠价的流量包（无时间限制）
curl -X PUT "${SERVER_HOST:-http://localhost:8080}/api/v1/pay-service/admin/store-service/packages" \
  -H "Content-Type: application/json" \
  -H "x-trace-id: pkg_create_002" \
  -d '{
    "package_name": "高级流量包",
    "package_desc": "适合企业用户的高级流量包，包含1TB流量",
    "entitlement": 1024,
    "original_price": 99.99,
    "discount_price": 79.99,
    "sale_status": "on_sale",
    "currency": "USD",
    "country": "US",
    "extra1": "premium_plan",
    "extra2": "monthly",
    "extra3": 1024,
    "extra4": 30
  }'

# 创建有时间限制优惠的流量包
curl -X PUT "${SERVER_HOST:-http://localhost:8080}/api/v1/pay-service/admin/store-service/packages" \
  -H "Content-Type: application/json" \
  -H "x-trace-id: pkg_create_003" \
  -d '{
    "package_name": "专业流量包",
    "package_desc": "适合专业用户的流量包，包含500GB流量",
    "entitlement": 500,
    "original_price": 49.99,
    "discount_price": 39.99,
    "discount_start_time": "2025-01-01T00:00:00Z",
    "discount_end_time": "2025-12-31T23:59:59Z",
    "sale_status": "on_sale",
    "currency": "USD",
    "country": "US",
    "extra1": "professional_plan",
    "extra2": "monthly",
    "extra3": 500,
    "extra4": 30
  }'

# 创建中国区流量包
curl -X PUT "${SERVER_HOST:-http://localhost:8080}/api/v1/pay-service/admin/store-service/packages" \
  -H "Content-Type: application/json" \
  -H "x-trace-id: pkg_create_004" \
  -d '{
    "package_name": "中国区基础流量包",
    "package_desc": "适合中国用户的基础流量包，包含100GB流量",
    "entitlement": 100,
    "original_price": 68.00,
    "sale_status": "on_sale",
    "currency": "CNY",
    "country": "CN",
    "extra1": "basic_plan_cn",
    "extra2": "monthly",
    "extra3": 100,
    "extra4": 30
  }'

# 创建限时优惠流量包（7天优惠）
curl -X PUT "${SERVER_HOST:-http://localhost:8080}/api/v1/pay-service/admin/store-service/packages" \
  -H "Content-Type: application/json" \
  -H "x-trace-id: pkg_create_005" \
  -d '{
    "package_name": "限时优惠流量包",
    "package_desc": "7天限时优惠，包含200GB流量",
    "entitlement": 200,
    "original_price": 29.99,
    "discount_price": 19.99,
    "discount_start_time": "2025-01-15T00:00:00Z",
    "discount_end_time": "2025-01-22T23:59:59Z",
    "sale_status": "on_sale",
    "currency": "USD",
    "country": "US",
    "extra1": "limited_time_offer",
    "extra2": "weekly",
    "extra3": 200,
    "extra4": 7
  }'

# 创建未来开始的优惠流量包
curl -X PUT "${SERVER_HOST:-http://localhost:8080}/api/v1/pay-service/admin/store-service/packages" \
  -H "Content-Type: application/json" \
  -H "x-trace-id: pkg_create_006" \
  -d '{
    "package_name": "春节特惠流量包",
    "package_desc": "春节期间特惠，包含1TB流量",
    "entitlement": 1024,
    "original_price": 199.99,
    "discount_price": 99.99,
    "discount_start_time": "2025-02-01T00:00:00Z",
    "discount_end_time": "2025-02-15T23:59:59Z",
    "sale_status": "on_sale",
    "currency": "USD",
    "country": "US",
    "extra1": "spring_festival_offer",
    "extra2": "seasonal",
    "extra3": 1024,
    "extra4": 15
  }'

# 创建停售状态的流量包
curl -X PUT "${SERVER_HOST:-http://localhost:8080}/api/v1/pay-service/admin/store-service/packages" \
  -H "Content-Type: application/json" \
  -H "x-trace-id: pkg_create_007" \
  -d '{
    "package_name": "维护中流量包",
    "package_desc": "暂时停售维护中的流量包",
    "entitlement": 50,
    "original_price": 9.99,
    "sale_status": "off_sale",
    "currency": "USD",
    "country": "US",
    "extra1": "maintenance",
    "extra2": "temporary",
    "extra3": 50,
    "extra4": 0
  }'
```

#### 创建流量包 - Windows PowerShell

```powershell
$serverHost = if ($env:SERVER_HOST) { $env:SERVER_HOST } else { "http://localhost:8080" }

# 创建带优惠时间的流量包
curl -X PUT "$serverHost/api/v1/pay-service/admin/store-service/packages" `
  -H "Content-Type: application/json" `
  -H "x-trace-id: pkg_create_ps_001" `
  -d '{
    "package_name": "PowerShell测试流量包",
    "package_desc": "使用PowerShell创建的测试流量包",
    "entitlement": 200,
    "original_price": 39.99,
    "discount_price": 29.99,
    "discount_start_time": "2025-01-01T00:00:00Z",
    "discount_end_time": "2025-12-31T23:59:59Z",
    "sale_status": "on_sale",
    "currency": "USD",
    "country": "US",
    "extra1": "powershell_test",
    "extra2": "monthly",
    "extra3": 200,
    "extra4": 30
  }'
```
```

#### 管理员查询流量包 - Linux/macOS

```bash
# 查询所有流量包（管理员视图）
curl -X GET "${SERVER_HOST:-http://localhost:8080}/api/v1/pay-service/admin/store-service/packages" \
  -H "Content-Type: application/json" \
  -H "x-trace-id: admin_list_001"

# 按货币查询
curl -X GET "${SERVER_HOST:-http://localhost:8080}/api/v1/pay-service/admin/store-service/packages?currency=USD" \
  -H "Content-Type: application/json" \
  -H "x-trace-id: admin_list_002"

# 按国家查询
curl -X GET "${SERVER_HOST:-http://localhost:8080}/api/v1/pay-service/admin/store-service/packages?country=CN" \
  -H "Content-Type: application/json" \
  -H "x-trace-id: admin_list_003"

# 分页查询
curl -X GET "${SERVER_HOST:-http://localhost:8080}/api/v1/pay-service/admin/store-service/packages?limit=5&offset=0" \
  -H "Content-Type: application/json" \
  -H "x-trace-id: admin_list_004"
```

#### 更新流量包 - Linux/macOS

```bash
# 更新流量包信息（需要先创建一个流量包并获取package_id）
# 注意：package_id 需要替换为实际的流量包ID
curl -X POST "${SERVER_HOST:-http://localhost:8080}/api/v1/pay-service/admin/store-service/packages" \
  -H "Content-Type: application/json" \
  -H "x-trace-id: pkg_update_001" \
  -d '{
    "package_id": "替换为实际的package_id",
    "package_name": "更新后的流量包名称",
    "package_desc": "更新后的流量包描述",
    "entitlement": 150,
    "original_price": 19.99,
    "discount_price": 15.99,
    "sale_status": "on_sale"
  }'

# 更新优惠时间和价格
curl -X POST "${SERVER_HOST:-http://localhost:8080}/api/v1/pay-service/admin/store-service/packages" \
  -H "Content-Type: application/json" \
  -H "x-trace-id: pkg_update_002" \
  -d '{
    "package_id": "520d5c57-d474-475d-8e1b-c69b639899aa",
    "original_price": 29.99,
    "discount_price": 24.99,
    "discount_start_time": "2025-01-20T00:00:00Z",
    "discount_end_time": "2025-01-31T23:59:59Z"
  }'

# 设置流量包为停售状态
curl -X POST "${SERVER_HOST:-http://localhost:8080}/api/v1/pay-service/admin/store-service/packages" \
  -H "Content-Type: application/json" \
  -H "x-trace-id: pkg_update_003" \
  -d '{
    "package_id": "替换为实际的package_id",
    "sale_status": "off_sale"
  }'

# 取消优惠（设置优惠价格为null）
curl -X POST "${SERVER_HOST:-http://localhost:8080}/api/v1/pay-service/admin/store-service/packages" \
  -H "Content-Type: application/json" \
  -H "x-trace-id: pkg_update_004" \
  -d '{
    "package_id": "替换为实际的package_id",
    "discount_price": null,
    "discount_start_time": null,
    "discount_end_time": null
  }'
```

#### 删除流量包 - Linux/macOS

```bash
# 删除流量包（软删除）
# 注意：package_id 需要替换为实际的流量包ID
curl -X POST "${SERVER_HOST:-http://localhost:8080}/api/v1/pay-service/admin/store-service/packages" \
  -H "Content-Type: application/json" \
  -H "x-trace-id: pkg_delete_001" \
  -d '{
    "package_id": "替换为实际的package_id",
    "currency": "USD",
    "country": "US"
  }'

# 删除中国区流量包
curl -X POST "${SERVER_HOST:-http://localhost:8080}/api/v1/pay-service/admin/store-service/packages" \
  -H "Content-Type: application/json" \
  -H "x-trace-id: pkg_delete_002" \
  -d '{
    "package_id": "替换为实际的package_id",
    "currency": "CNY",
    "country": "CN"
  }'
```

### 完整测试流程示例

```bash
# 1. 设置环境变量
export SERVER_HOST=http://localhost:8080

# 2. 创建一个测试流量包
echo "=== 创建流量包 ==="
curl -X PUT "${SERVER_HOST}/api/v1/pay-service/admin/store-service/packages" \
  -H "Content-Type: application/json" \
  -H "x-trace-id: test_flow_001" \
  -d '{
    "package_name": "测试流量包",
    "package_desc": "用于测试的流量包",
    "entitlement": 100,
    "original_price": 19.99,
    "discount_price": 15.99,
    "discount_start_time": "2025-01-01T00:00:00Z",
    "discount_end_time": "2025-12-31T23:59:59Z",
    "sale_status": "on_sale",
    "currency": "USD",
    "country": "US",
    "extra3": 100,
    "extra4": 30
  }' | jq '.'

# 3. 查询流量包列表（终端用户视图）
echo "=== 终端用户查询 ==="
curl -X GET "${SERVER_HOST}/api/v1/pay-service/store-service/packages" \
  -H "Content-Type: application/json" \
  -H "x-trace-id: test_flow_002" \
  -H "x-user-id: test_user" \
  -H "x-role: customer" | jq '.'

# 4. 查询流量包列表（管理员视图）
echo "=== 管理员查询 ==="
curl -X GET "${SERVER_HOST}/api/v1/pay-service/admin/store-service/packages" \
  -H "Content-Type: application/json" \
  -H "x-trace-id: test_flow_003" | jq '.'

# 5. 更新流量包（需要从上面的查询结果中获取package_id）
# echo "=== 更新流量包 ==="
# PACKAGE_ID="从查询结果中获取的package_id"
# curl -X POST "${SERVER_HOST}/api/v1/pay-service/admin/store-service/packages" \
#   -H "Content-Type: application/json" \
#   -H "x-trace-id: test_flow_004" \
#   -d "{
#     \"package_id\": \"${PACKAGE_ID}\",
#     \"package_name\": \"更新后的测试流量包\",
#     \"original_price\": 24.99
#   }" | jq '.'

# 6. 删除流量包
# echo "=== 删除流量包 ==="
# curl -X POST "${SERVER_HOST}/api/v1/pay-service/admin/store-service/packages" \
#   -H "Content-Type: application/json" \
#   -H "x-trace-id: test_flow_005" \
#   -d "{
#     \"package_id\": \"${PACKAGE_ID}\",
#     \"currency\": \"USD\",
#     \"country\": \"US\"
#   }" | jq '.'
```

### 优惠时间测试专区

#### 测试不同优惠时间场景

```bash
# 测试当前时间在优惠期内的流量包
curl -X PUT "${SERVER_HOST:-http://localhost:8080}/api/v1/pay-service/admin/store-service/packages" \
  -H "Content-Type: application/json" \
  -H "x-trace-id: discount_test_001" \
  -d '{
    "package_name": "当前优惠流量包",
    "package_desc": "当前时间在优惠期内",
    "entitlement": 200,
    "original_price": 39.99,
    "discount_price": 29.99,
    "discount_start_time": "2024-01-01T00:00:00Z",
    "discount_end_time": "2026-12-31T23:59:59Z",
    "sale_status": "on_sale",
    "currency": "USD",
    "country": "US"
  }'

# 测试优惠未开始的流量包
curl -X PUT "${SERVER_HOST:-http://localhost:8080}/api/v1/pay-service/admin/store-service/packages" \
  -H "Content-Type: application/json" \
  -H "x-trace-id: discount_test_002" \
  -d '{
    "package_name": "未来优惠流量包",
    "package_desc": "优惠还未开始",
    "entitlement": 300,
    "original_price": 59.99,
    "discount_price": 39.99,
    "discount_start_time": "2026-01-01T00:00:00Z",
    "discount_end_time": "2026-12-31T23:59:59Z",
    "sale_status": "on_sale",
    "currency": "USD",
    "country": "US"
  }'

# 测试优惠已结束的流量包
curl -X PUT "${SERVER_HOST:-http://localhost:8080}/api/v1/pay-service/admin/store-service/packages" \
  -H "Content-Type: application/json" \
  -H "x-trace-id: discount_test_003" \
  -d '{
    "package_name": "过期优惠流量包",
    "package_desc": "优惠已经结束",
    "entitlement": 150,
    "original_price": 29.99,
    "discount_price": 19.99,
    "discount_start_time": "2023-01-01T00:00:00Z",
    "discount_end_time": "2023-12-31T23:59:59Z",
    "sale_status": "on_sale",
    "currency": "USD",
    "country": "US"
  }'

# 测试只有开始时间的优惠（永久优惠）
curl -X PUT "${SERVER_HOST:-http://localhost:8080}/api/v1/pay-service/admin/store-service/packages" \
  -H "Content-Type: application/json" \
  -H "x-trace-id: discount_test_004" \
  -d '{
    "package_name": "永久优惠流量包",
    "package_desc": "从指定时间开始永久优惠",
    "entitlement": 500,
    "original_price": 99.99,
    "discount_price": 79.99,
    "discount_start_time": "2024-01-01T00:00:00Z",
    "sale_status": "on_sale",
    "currency": "USD",
    "country": "US"
  }'
```

### 注意事项

1. **认证头部**: 终端用户接口需要 `x-user-id` 和 `x-role` 头部
2. **管理员接口**: 当前配置为可选认证，生产环境需要添加管理员权限验证
3. **package_id**: 更新和删除操作需要实际的流量包ID，需要先创建或查询获取
4. **默认值**: 如果不传 `currency` 和 `country`，系统会使用默认值 USD 和 US
5. **字段变更**:
   - `entitlement` 现在是 int32 类型，不再是字符串
   - 删除了 `discount_percent` 字段，只保留 `discount_price`
   - 新增 `discount_start_time` 和 `discount_end_time` 优惠时间字段
   - 新增 `sale_status` 出售状态字段（on_sale/off_sale）
6. **优惠逻辑**:
   - 优惠价格只在有效期内生效
   - 如果没有设置优惠时间，优惠永久有效
   - 终端用户只能看到起售状态（on_sale）的流量包
   - 管理员可以看到所有状态的流量包
7. **时间格式**: 使用 RFC3339 格式，如 "2025-01-01T00:00:00Z"
8. **分页**: 默认 limit=50，最大 limit=500
