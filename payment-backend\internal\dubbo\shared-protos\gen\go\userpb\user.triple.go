// Code generated by protoc-gen-triple. DO NOT EDIT.
//
// Source: protos/user.proto
package userpb

import (
	"context"
)

import (
	"dubbo.apache.org/dubbo-go/v3"
	"dubbo.apache.org/dubbo-go/v3/client"
	"dubbo.apache.org/dubbo-go/v3/common"
	"dubbo.apache.org/dubbo-go/v3/common/constant"
	"dubbo.apache.org/dubbo-go/v3/protocol/triple/triple_protocol"
	"dubbo.apache.org/dubbo-go/v3/server"
)

// This is a compile-time assertion to ensure that this generated file and the Triple package
// are compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of Triple newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of Triple or updating the Triple
// version compiled into your binary.
const _ = triple_protocol.IsAtLeastVersion0_1_0

const (
	// UserServiceName is the fully-qualified name of the UserService service.
	UserServiceName = "com.aibook.user.grpc.UserService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// UserServiceGetEmailVerifyCodeProcedure is the fully-qualified name of the UserService's GetEmailVerifyCode RPC.
	UserServiceGetEmailVerifyCodeProcedure = "/com.aibook.user.grpc.UserService/GetEmailVerifyCode"
	// UserServiceCreateUserProcedure is the fully-qualified name of the UserService's CreateUser RPC.
	UserServiceCreateUserProcedure = "/com.aibook.user.grpc.UserService/CreateUser"
	// UserServiceLoginProcedure is the fully-qualified name of the UserService's Login RPC.
	UserServiceLoginProcedure = "/com.aibook.user.grpc.UserService/Login"
	// UserServiceLoginWithCodeProcedure is the fully-qualified name of the UserService's LoginWithCode RPC.
	UserServiceLoginWithCodeProcedure = "/com.aibook.user.grpc.UserService/LoginWithCode"
	// UserServiceRefreshTokenProcedure is the fully-qualified name of the UserService's RefreshToken RPC.
	UserServiceRefreshTokenProcedure = "/com.aibook.user.grpc.UserService/RefreshToken"
	// UserServiceGetProfileProcedure is the fully-qualified name of the UserService's GetProfile RPC.
	UserServiceGetProfileProcedure = "/com.aibook.user.grpc.UserService/GetProfile"
	// UserServiceUpdateProfileProcedure is the fully-qualified name of the UserService's UpdateProfile RPC.
	UserServiceUpdateProfileProcedure = "/com.aibook.user.grpc.UserService/UpdateProfile"
	// UserServiceResetPasswordProcedure is the fully-qualified name of the UserService's ResetPassword RPC.
	UserServiceResetPasswordProcedure = "/com.aibook.user.grpc.UserService/ResetPassword"
	// UserServiceUpdateSystemPlanProcedure is the fully-qualified name of the UserService's UpdateSystemPlan RPC.
	UserServiceUpdateSystemPlanProcedure = "/com.aibook.user.grpc.UserService/UpdateSystemPlan"
	// UserServiceGetSystemPlansProcedure is the fully-qualified name of the UserService's GetSystemPlans RPC.
	UserServiceGetSystemPlansProcedure = "/com.aibook.user.grpc.UserService/GetSystemPlans"
	// UserServiceDeleteSystemPlanProcedure is the fully-qualified name of the UserService's DeleteSystemPlan RPC.
	UserServiceDeleteSystemPlanProcedure = "/com.aibook.user.grpc.UserService/DeleteSystemPlan"
	// UserServiceUpdateSystemFeaturePointsProcedure is the fully-qualified name of the UserService's UpdateSystemFeaturePoints RPC.
	UserServiceUpdateSystemFeaturePointsProcedure = "/com.aibook.user.grpc.UserService/UpdateSystemFeaturePoints"
	// UserServiceGetSystemFeaturePointsProcedure is the fully-qualified name of the UserService's GetSystemFeaturePoints RPC.
	UserServiceGetSystemFeaturePointsProcedure = "/com.aibook.user.grpc.UserService/GetSystemFeaturePoints"
	// UserServiceDeleteSystemFeaturePointsProcedure is the fully-qualified name of the UserService's DeleteSystemFeaturePoints RPC.
	UserServiceDeleteSystemFeaturePointsProcedure = "/com.aibook.user.grpc.UserService/DeleteSystemFeaturePoints"
	// UserServiceGetPlanProcedure is the fully-qualified name of the UserService's GetPlan RPC.
	UserServiceGetPlanProcedure = "/com.aibook.user.grpc.UserService/GetPlan"
	// UserServiceReducePlanProcedure is the fully-qualified name of the UserService's ReducePlan RPC.
	UserServiceReducePlanProcedure = "/com.aibook.user.grpc.UserService/ReducePlan"
	// UserServiceRollbackHalfPointBenefitProcedure is the fully-qualified name of the UserService's RollbackHalfPointBenefit RPC.
	UserServiceRollbackHalfPointBenefitProcedure = "/com.aibook.user.grpc.UserService/RollbackHalfPointBenefit"
	// UserServiceBenefitRecordProcedure is the fully-qualified name of the UserService's BenefitRecord RPC.
	UserServiceBenefitRecordProcedure = "/com.aibook.user.grpc.UserService/BenefitRecord"
	// UserServiceChargePointsProcedure is the fully-qualified name of the UserService's ChargePoints RPC.
	UserServiceChargePointsProcedure = "/com.aibook.user.grpc.UserService/ChargePoints"
	// UserServiceDeleteProcedure is the fully-qualified name of the UserService's Delete RPC.
	UserServiceDeleteProcedure = "/com.aibook.user.grpc.UserService/Delete"
	// UserServiceSearchProcedure is the fully-qualified name of the UserService's Search RPC.
	UserServiceSearchProcedure = "/com.aibook.user.grpc.UserService/Search"
)

var (
	_ UserService = (*UserServiceImpl)(nil)
)

// UserService is a client for the com.aibook.user.grpc.UserService service.
type UserService interface {
	GetEmailVerifyCode(ctx context.Context, req *GetEmailVerifyCodeRequest, opts ...client.CallOption) (*GetEmailVerifyCodeResponse, error)
	CreateUser(ctx context.Context, req *CreateUserRequest, opts ...client.CallOption) (*LoginResponse, error)
	Login(ctx context.Context, req *LoginRequest, opts ...client.CallOption) (*LoginResponse, error)
	LoginWithCode(ctx context.Context, req *LoginWithCodeRequest, opts ...client.CallOption) (*LoginResponse, error)
	RefreshToken(ctx context.Context, req *Empty, opts ...client.CallOption) (*LoginResponse, error)
	GetProfile(ctx context.Context, req *GetProfileRequest, opts ...client.CallOption) (*UserProfile, error)
	UpdateProfile(ctx context.Context, req *UpdateProfileRequest, opts ...client.CallOption) (*UserProfile, error)
	ResetPassword(ctx context.Context, req *ResetPasswordRequest, opts ...client.CallOption) (*Empty, error)
	UpdateSystemPlan(ctx context.Context, req *SystemPlan, opts ...client.CallOption) (*Empty, error)
	GetSystemPlans(ctx context.Context, req *Empty, opts ...client.CallOption) (*SystemPlans, error)
	DeleteSystemPlan(ctx context.Context, req *DeleteSystemPlanRequest, opts ...client.CallOption) (*Empty, error)
	UpdateSystemFeaturePoints(ctx context.Context, req *SystemFeaturePoints, opts ...client.CallOption) (*Empty, error)
	GetSystemFeaturePoints(ctx context.Context, req *Empty, opts ...client.CallOption) (*GetSystemFeaturePointsResponse, error)
	DeleteSystemFeaturePoints(ctx context.Context, req *DeleteSystemFeaturePointsRequest, opts ...client.CallOption) (*Empty, error)
	GetPlan(ctx context.Context, req *Empty, opts ...client.CallOption) (*UserPlan, error)
	ReducePlan(ctx context.Context, req *ReduceBenefitRequest, opts ...client.CallOption) (*BenefitOrder, error)
	RollbackHalfPointBenefit(ctx context.Context, req *BenefitOrder, opts ...client.CallOption) (*Empty, error)
	BenefitRecord(ctx context.Context, req *PageRequest, opts ...client.CallOption) (*BenefitRecordResponse, error)
	ChargePoints(ctx context.Context, req *ChargeRequest, opts ...client.CallOption) (*Empty, error)
	Delete(ctx context.Context, req *Empty, opts ...client.CallOption) (*Empty, error)
	Search(ctx context.Context, req *SearchRequest, opts ...client.CallOption) (*SearchResponse, error)
}

// NewUserService constructs a client for the userpb.UserService service.
func NewUserService(cli *client.Client, opts ...client.ReferenceOption) (UserService, error) {
	conn, err := cli.DialWithInfo("com.aibook.user.grpc.UserService", &UserService_ClientInfo, opts...)
	if err != nil {
		return nil, err
	}
	return &UserServiceImpl{
		conn: conn,
	}, nil
}

func SetConsumerUserService(srv common.RPCService) {
	dubbo.SetConsumerServiceWithInfo(srv, &UserService_ClientInfo)
}

// UserServiceImpl implements UserService.
type UserServiceImpl struct {
	conn *client.Connection
}

func (c *UserServiceImpl) GetEmailVerifyCode(ctx context.Context, req *GetEmailVerifyCodeRequest, opts ...client.CallOption) (*GetEmailVerifyCodeResponse, error) {
	resp := new(GetEmailVerifyCodeResponse)
	if err := c.conn.CallUnary(ctx, []interface{}{req}, resp, "GetEmailVerifyCode", opts...); err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *UserServiceImpl) CreateUser(ctx context.Context, req *CreateUserRequest, opts ...client.CallOption) (*LoginResponse, error) {
	resp := new(LoginResponse)
	if err := c.conn.CallUnary(ctx, []interface{}{req}, resp, "CreateUser", opts...); err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *UserServiceImpl) Login(ctx context.Context, req *LoginRequest, opts ...client.CallOption) (*LoginResponse, error) {
	resp := new(LoginResponse)
	if err := c.conn.CallUnary(ctx, []interface{}{req}, resp, "Login", opts...); err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *UserServiceImpl) LoginWithCode(ctx context.Context, req *LoginWithCodeRequest, opts ...client.CallOption) (*LoginResponse, error) {
	resp := new(LoginResponse)
	if err := c.conn.CallUnary(ctx, []interface{}{req}, resp, "LoginWithCode", opts...); err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *UserServiceImpl) RefreshToken(ctx context.Context, req *Empty, opts ...client.CallOption) (*LoginResponse, error) {
	resp := new(LoginResponse)
	if err := c.conn.CallUnary(ctx, []interface{}{req}, resp, "RefreshToken", opts...); err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *UserServiceImpl) GetProfile(ctx context.Context, req *GetProfileRequest, opts ...client.CallOption) (*UserProfile, error) {
	resp := new(UserProfile)
	if err := c.conn.CallUnary(ctx, []interface{}{req}, resp, "GetProfile", opts...); err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *UserServiceImpl) UpdateProfile(ctx context.Context, req *UpdateProfileRequest, opts ...client.CallOption) (*UserProfile, error) {
	resp := new(UserProfile)
	if err := c.conn.CallUnary(ctx, []interface{}{req}, resp, "UpdateProfile", opts...); err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *UserServiceImpl) ResetPassword(ctx context.Context, req *ResetPasswordRequest, opts ...client.CallOption) (*Empty, error) {
	resp := new(Empty)
	if err := c.conn.CallUnary(ctx, []interface{}{req}, resp, "ResetPassword", opts...); err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *UserServiceImpl) UpdateSystemPlan(ctx context.Context, req *SystemPlan, opts ...client.CallOption) (*Empty, error) {
	resp := new(Empty)
	if err := c.conn.CallUnary(ctx, []interface{}{req}, resp, "UpdateSystemPlan", opts...); err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *UserServiceImpl) GetSystemPlans(ctx context.Context, req *Empty, opts ...client.CallOption) (*SystemPlans, error) {
	resp := new(SystemPlans)
	if err := c.conn.CallUnary(ctx, []interface{}{req}, resp, "GetSystemPlans", opts...); err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *UserServiceImpl) DeleteSystemPlan(ctx context.Context, req *DeleteSystemPlanRequest, opts ...client.CallOption) (*Empty, error) {
	resp := new(Empty)
	if err := c.conn.CallUnary(ctx, []interface{}{req}, resp, "DeleteSystemPlan", opts...); err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *UserServiceImpl) UpdateSystemFeaturePoints(ctx context.Context, req *SystemFeaturePoints, opts ...client.CallOption) (*Empty, error) {
	resp := new(Empty)
	if err := c.conn.CallUnary(ctx, []interface{}{req}, resp, "UpdateSystemFeaturePoints", opts...); err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *UserServiceImpl) GetSystemFeaturePoints(ctx context.Context, req *Empty, opts ...client.CallOption) (*GetSystemFeaturePointsResponse, error) {
	resp := new(GetSystemFeaturePointsResponse)
	if err := c.conn.CallUnary(ctx, []interface{}{req}, resp, "GetSystemFeaturePoints", opts...); err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *UserServiceImpl) DeleteSystemFeaturePoints(ctx context.Context, req *DeleteSystemFeaturePointsRequest, opts ...client.CallOption) (*Empty, error) {
	resp := new(Empty)
	if err := c.conn.CallUnary(ctx, []interface{}{req}, resp, "DeleteSystemFeaturePoints", opts...); err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *UserServiceImpl) GetPlan(ctx context.Context, req *Empty, opts ...client.CallOption) (*UserPlan, error) {
	resp := new(UserPlan)
	if err := c.conn.CallUnary(ctx, []interface{}{req}, resp, "GetPlan", opts...); err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *UserServiceImpl) ReducePlan(ctx context.Context, req *ReduceBenefitRequest, opts ...client.CallOption) (*BenefitOrder, error) {
	resp := new(BenefitOrder)
	if err := c.conn.CallUnary(ctx, []interface{}{req}, resp, "ReducePlan", opts...); err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *UserServiceImpl) RollbackHalfPointBenefit(ctx context.Context, req *BenefitOrder, opts ...client.CallOption) (*Empty, error) {
	resp := new(Empty)
	if err := c.conn.CallUnary(ctx, []interface{}{req}, resp, "RollbackHalfPointBenefit", opts...); err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *UserServiceImpl) BenefitRecord(ctx context.Context, req *PageRequest, opts ...client.CallOption) (*BenefitRecordResponse, error) {
	resp := new(BenefitRecordResponse)
	if err := c.conn.CallUnary(ctx, []interface{}{req}, resp, "BenefitRecord", opts...); err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *UserServiceImpl) ChargePoints(ctx context.Context, req *ChargeRequest, opts ...client.CallOption) (*Empty, error) {
	resp := new(Empty)
	if err := c.conn.CallUnary(ctx, []interface{}{req}, resp, "ChargePoints", opts...); err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *UserServiceImpl) Delete(ctx context.Context, req *Empty, opts ...client.CallOption) (*Empty, error) {
	resp := new(Empty)
	if err := c.conn.CallUnary(ctx, []interface{}{req}, resp, "Delete", opts...); err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *UserServiceImpl) Search(ctx context.Context, req *SearchRequest, opts ...client.CallOption) (*SearchResponse, error) {
	resp := new(SearchResponse)
	if err := c.conn.CallUnary(ctx, []interface{}{req}, resp, "Search", opts...); err != nil {
		return nil, err
	}
	return resp, nil
}

var UserService_ClientInfo = client.ClientInfo{
	InterfaceName: "com.aibook.user.grpc.UserService",
	MethodNames:   []string{"GetEmailVerifyCode", "CreateUser", "Login", "LoginWithCode", "RefreshToken", "GetProfile", "UpdateProfile", "ResetPassword", "UpdateSystemPlan", "GetSystemPlans", "DeleteSystemPlan", "UpdateSystemFeaturePoints", "GetSystemFeaturePoints", "DeleteSystemFeaturePoints", "GetPlan", "ReducePlan", "RollbackHalfPointBenefit", "BenefitRecord", "ChargePoints", "Delete", "Search"},
	ConnectionInjectFunc: func(dubboCliRaw interface{}, conn *client.Connection) {
		dubboCli := dubboCliRaw.(*UserServiceImpl)
		dubboCli.conn = conn
	},
}

// UserServiceHandler is an implementation of the com.aibook.user.grpc.UserService service.
type UserServiceHandler interface {
	GetEmailVerifyCode(context.Context, *GetEmailVerifyCodeRequest) (*GetEmailVerifyCodeResponse, error)
	CreateUser(context.Context, *CreateUserRequest) (*LoginResponse, error)
	Login(context.Context, *LoginRequest) (*LoginResponse, error)
	LoginWithCode(context.Context, *LoginWithCodeRequest) (*LoginResponse, error)
	RefreshToken(context.Context, *Empty) (*LoginResponse, error)
	GetProfile(context.Context, *GetProfileRequest) (*UserProfile, error)
	UpdateProfile(context.Context, *UpdateProfileRequest) (*UserProfile, error)
	ResetPassword(context.Context, *ResetPasswordRequest) (*Empty, error)
	UpdateSystemPlan(context.Context, *SystemPlan) (*Empty, error)
	GetSystemPlans(context.Context, *Empty) (*SystemPlans, error)
	DeleteSystemPlan(context.Context, *DeleteSystemPlanRequest) (*Empty, error)
	UpdateSystemFeaturePoints(context.Context, *SystemFeaturePoints) (*Empty, error)
	GetSystemFeaturePoints(context.Context, *Empty) (*GetSystemFeaturePointsResponse, error)
	DeleteSystemFeaturePoints(context.Context, *DeleteSystemFeaturePointsRequest) (*Empty, error)
	GetPlan(context.Context, *Empty) (*UserPlan, error)
	ReducePlan(context.Context, *ReduceBenefitRequest) (*BenefitOrder, error)
	RollbackHalfPointBenefit(context.Context, *BenefitOrder) (*Empty, error)
	BenefitRecord(context.Context, *PageRequest) (*BenefitRecordResponse, error)
	ChargePoints(context.Context, *ChargeRequest) (*Empty, error)
	Delete(context.Context, *Empty) (*Empty, error)
	Search(context.Context, *SearchRequest) (*SearchResponse, error)
}

func RegisterUserServiceHandler(srv *server.Server, hdlr UserServiceHandler, opts ...server.ServiceOption) error {
	return srv.Register(hdlr, &UserService_ServiceInfo, opts...)
}

func SetProviderUserService(srv common.RPCService) {
	dubbo.SetProviderServiceWithInfo(srv, &UserService_ServiceInfo)
}

var UserService_ServiceInfo = server.ServiceInfo{
	InterfaceName: "com.aibook.user.grpc.UserService",
	ServiceType:   (*UserServiceHandler)(nil),
	Methods: []server.MethodInfo{
		{
			Name: "GetEmailVerifyCode",
			Type: constant.CallUnary,
			ReqInitFunc: func() interface{} {
				return new(GetEmailVerifyCodeRequest)
			},
			MethodFunc: func(ctx context.Context, args []interface{}, handler interface{}) (interface{}, error) {
				req := args[0].(*GetEmailVerifyCodeRequest)
				res, err := handler.(UserServiceHandler).GetEmailVerifyCode(ctx, req)
				if err != nil {
					return nil, err
				}
				return triple_protocol.NewResponse(res), nil
			},
		},
		{
			Name: "CreateUser",
			Type: constant.CallUnary,
			ReqInitFunc: func() interface{} {
				return new(CreateUserRequest)
			},
			MethodFunc: func(ctx context.Context, args []interface{}, handler interface{}) (interface{}, error) {
				req := args[0].(*CreateUserRequest)
				res, err := handler.(UserServiceHandler).CreateUser(ctx, req)
				if err != nil {
					return nil, err
				}
				return triple_protocol.NewResponse(res), nil
			},
		},
		{
			Name: "Login",
			Type: constant.CallUnary,
			ReqInitFunc: func() interface{} {
				return new(LoginRequest)
			},
			MethodFunc: func(ctx context.Context, args []interface{}, handler interface{}) (interface{}, error) {
				req := args[0].(*LoginRequest)
				res, err := handler.(UserServiceHandler).Login(ctx, req)
				if err != nil {
					return nil, err
				}
				return triple_protocol.NewResponse(res), nil
			},
		},
		{
			Name: "LoginWithCode",
			Type: constant.CallUnary,
			ReqInitFunc: func() interface{} {
				return new(LoginWithCodeRequest)
			},
			MethodFunc: func(ctx context.Context, args []interface{}, handler interface{}) (interface{}, error) {
				req := args[0].(*LoginWithCodeRequest)
				res, err := handler.(UserServiceHandler).LoginWithCode(ctx, req)
				if err != nil {
					return nil, err
				}
				return triple_protocol.NewResponse(res), nil
			},
		},
		{
			Name: "RefreshToken",
			Type: constant.CallUnary,
			ReqInitFunc: func() interface{} {
				return new(Empty)
			},
			MethodFunc: func(ctx context.Context, args []interface{}, handler interface{}) (interface{}, error) {
				req := args[0].(*Empty)
				res, err := handler.(UserServiceHandler).RefreshToken(ctx, req)
				if err != nil {
					return nil, err
				}
				return triple_protocol.NewResponse(res), nil
			},
		},
		{
			Name: "GetProfile",
			Type: constant.CallUnary,
			ReqInitFunc: func() interface{} {
				return new(GetProfileRequest)
			},
			MethodFunc: func(ctx context.Context, args []interface{}, handler interface{}) (interface{}, error) {
				req := args[0].(*GetProfileRequest)
				res, err := handler.(UserServiceHandler).GetProfile(ctx, req)
				if err != nil {
					return nil, err
				}
				return triple_protocol.NewResponse(res), nil
			},
		},
		{
			Name: "UpdateProfile",
			Type: constant.CallUnary,
			ReqInitFunc: func() interface{} {
				return new(UpdateProfileRequest)
			},
			MethodFunc: func(ctx context.Context, args []interface{}, handler interface{}) (interface{}, error) {
				req := args[0].(*UpdateProfileRequest)
				res, err := handler.(UserServiceHandler).UpdateProfile(ctx, req)
				if err != nil {
					return nil, err
				}
				return triple_protocol.NewResponse(res), nil
			},
		},
		{
			Name: "ResetPassword",
			Type: constant.CallUnary,
			ReqInitFunc: func() interface{} {
				return new(ResetPasswordRequest)
			},
			MethodFunc: func(ctx context.Context, args []interface{}, handler interface{}) (interface{}, error) {
				req := args[0].(*ResetPasswordRequest)
				res, err := handler.(UserServiceHandler).ResetPassword(ctx, req)
				if err != nil {
					return nil, err
				}
				return triple_protocol.NewResponse(res), nil
			},
		},
		{
			Name: "UpdateSystemPlan",
			Type: constant.CallUnary,
			ReqInitFunc: func() interface{} {
				return new(SystemPlan)
			},
			MethodFunc: func(ctx context.Context, args []interface{}, handler interface{}) (interface{}, error) {
				req := args[0].(*SystemPlan)
				res, err := handler.(UserServiceHandler).UpdateSystemPlan(ctx, req)
				if err != nil {
					return nil, err
				}
				return triple_protocol.NewResponse(res), nil
			},
		},
		{
			Name: "GetSystemPlans",
			Type: constant.CallUnary,
			ReqInitFunc: func() interface{} {
				return new(Empty)
			},
			MethodFunc: func(ctx context.Context, args []interface{}, handler interface{}) (interface{}, error) {
				req := args[0].(*Empty)
				res, err := handler.(UserServiceHandler).GetSystemPlans(ctx, req)
				if err != nil {
					return nil, err
				}
				return triple_protocol.NewResponse(res), nil
			},
		},
		{
			Name: "DeleteSystemPlan",
			Type: constant.CallUnary,
			ReqInitFunc: func() interface{} {
				return new(DeleteSystemPlanRequest)
			},
			MethodFunc: func(ctx context.Context, args []interface{}, handler interface{}) (interface{}, error) {
				req := args[0].(*DeleteSystemPlanRequest)
				res, err := handler.(UserServiceHandler).DeleteSystemPlan(ctx, req)
				if err != nil {
					return nil, err
				}
				return triple_protocol.NewResponse(res), nil
			},
		},
		{
			Name: "UpdateSystemFeaturePoints",
			Type: constant.CallUnary,
			ReqInitFunc: func() interface{} {
				return new(SystemFeaturePoints)
			},
			MethodFunc: func(ctx context.Context, args []interface{}, handler interface{}) (interface{}, error) {
				req := args[0].(*SystemFeaturePoints)
				res, err := handler.(UserServiceHandler).UpdateSystemFeaturePoints(ctx, req)
				if err != nil {
					return nil, err
				}
				return triple_protocol.NewResponse(res), nil
			},
		},
		{
			Name: "GetSystemFeaturePoints",
			Type: constant.CallUnary,
			ReqInitFunc: func() interface{} {
				return new(Empty)
			},
			MethodFunc: func(ctx context.Context, args []interface{}, handler interface{}) (interface{}, error) {
				req := args[0].(*Empty)
				res, err := handler.(UserServiceHandler).GetSystemFeaturePoints(ctx, req)
				if err != nil {
					return nil, err
				}
				return triple_protocol.NewResponse(res), nil
			},
		},
		{
			Name: "DeleteSystemFeaturePoints",
			Type: constant.CallUnary,
			ReqInitFunc: func() interface{} {
				return new(DeleteSystemFeaturePointsRequest)
			},
			MethodFunc: func(ctx context.Context, args []interface{}, handler interface{}) (interface{}, error) {
				req := args[0].(*DeleteSystemFeaturePointsRequest)
				res, err := handler.(UserServiceHandler).DeleteSystemFeaturePoints(ctx, req)
				if err != nil {
					return nil, err
				}
				return triple_protocol.NewResponse(res), nil
			},
		},
		{
			Name: "GetPlan",
			Type: constant.CallUnary,
			ReqInitFunc: func() interface{} {
				return new(Empty)
			},
			MethodFunc: func(ctx context.Context, args []interface{}, handler interface{}) (interface{}, error) {
				req := args[0].(*Empty)
				res, err := handler.(UserServiceHandler).GetPlan(ctx, req)
				if err != nil {
					return nil, err
				}
				return triple_protocol.NewResponse(res), nil
			},
		},
		{
			Name: "ReducePlan",
			Type: constant.CallUnary,
			ReqInitFunc: func() interface{} {
				return new(ReduceBenefitRequest)
			},
			MethodFunc: func(ctx context.Context, args []interface{}, handler interface{}) (interface{}, error) {
				req := args[0].(*ReduceBenefitRequest)
				res, err := handler.(UserServiceHandler).ReducePlan(ctx, req)
				if err != nil {
					return nil, err
				}
				return triple_protocol.NewResponse(res), nil
			},
		},
		{
			Name: "RollbackHalfPointBenefit",
			Type: constant.CallUnary,
			ReqInitFunc: func() interface{} {
				return new(BenefitOrder)
			},
			MethodFunc: func(ctx context.Context, args []interface{}, handler interface{}) (interface{}, error) {
				req := args[0].(*BenefitOrder)
				res, err := handler.(UserServiceHandler).RollbackHalfPointBenefit(ctx, req)
				if err != nil {
					return nil, err
				}
				return triple_protocol.NewResponse(res), nil
			},
		},
		{
			Name: "BenefitRecord",
			Type: constant.CallUnary,
			ReqInitFunc: func() interface{} {
				return new(PageRequest)
			},
			MethodFunc: func(ctx context.Context, args []interface{}, handler interface{}) (interface{}, error) {
				req := args[0].(*PageRequest)
				res, err := handler.(UserServiceHandler).BenefitRecord(ctx, req)
				if err != nil {
					return nil, err
				}
				return triple_protocol.NewResponse(res), nil
			},
		},
		{
			Name: "ChargePoints",
			Type: constant.CallUnary,
			ReqInitFunc: func() interface{} {
				return new(ChargeRequest)
			},
			MethodFunc: func(ctx context.Context, args []interface{}, handler interface{}) (interface{}, error) {
				req := args[0].(*ChargeRequest)
				res, err := handler.(UserServiceHandler).ChargePoints(ctx, req)
				if err != nil {
					return nil, err
				}
				return triple_protocol.NewResponse(res), nil
			},
		},
		{
			Name: "Delete",
			Type: constant.CallUnary,
			ReqInitFunc: func() interface{} {
				return new(Empty)
			},
			MethodFunc: func(ctx context.Context, args []interface{}, handler interface{}) (interface{}, error) {
				req := args[0].(*Empty)
				res, err := handler.(UserServiceHandler).Delete(ctx, req)
				if err != nil {
					return nil, err
				}
				return triple_protocol.NewResponse(res), nil
			},
		},
		{
			Name: "Search",
			Type: constant.CallUnary,
			ReqInitFunc: func() interface{} {
				return new(SearchRequest)
			},
			MethodFunc: func(ctx context.Context, args []interface{}, handler interface{}) (interface{}, error) {
				req := args[0].(*SearchRequest)
				res, err := handler.(UserServiceHandler).Search(ctx, req)
				if err != nil {
					return nil, err
				}
				return triple_protocol.NewResponse(res), nil
			},
		},
	},
}
