# 测试程序更新日志

## 新增功能 (2025-07-24)

### 新增接口测试

#### 1. 退款接口（内网）
- **文件**: `testForceRefundGin.go`
- **接口**: `POST /api/v1/pay-service/admin/order-service/orders/:order_id/force-refund`
- **说明**: 管理员强制退款接口，支持全额退款和部分退款
- **注意**: 目前只有HTTP实现，没有对应的RPC接口

#### 2. 流量包管理接口（内网）

##### Gin HTTP 接口
- **文件**: `testAdminPackagesGin.go`
- **接口列表**:
  - `PUT /api/v1/pay-service/admin/store-service/packages` - 创建流量包
  - `GET /api/v1/pay-service/admin/store-service/packages` - 获取所有流量包
  - `POST /api/v1/pay-service/admin/store-service/packages` - 更新流量包
  - `DELETE /api/v1/pay-service/admin/store-service/packages` - 删除流量包

##### Dubbo RPC 接口
- **文件**: `testAdminPackagesRPC.go`
- **接口列表**:
  - `POST /com.aibook.payment.grpc.StoreService/AdminAddPackages` - 创建流量包
  - `POST /com.aibook.payment.grpc.StoreService/AdminListAllPackages` - 获取所有流量包
  - `POST /com.aibook.payment.grpc.StoreService/AdminUpdatePackages` - 更新流量包
  - `POST /com.aibook.payment.grpc.StoreService/AdminDeletePackages` - 删除流量包

#### 3. 流量包接口（外网）
- **文件**: `testListAllPackagesExternal.go`
- **接口列表**:
  - `GET /api/v1/pay-service/store-service/packages` - 获取流量包列表
  - `GET /api/v1/pay-service/store-service/packages?currency=USD&country=US` - 获取流量包列表（带过滤条件）

### 数据结构扩展

#### 新增请求/响应结构体（在 `common.go` 中）
- `RefundOrderRequest` - 退款请求
- `CreatePackageRequest` - 创建流量包请求
- `UpdatePackageRequest` - 更新流量包请求
- `DeletePackageRequest` - 删除流量包请求

### 测试流程更新

#### 外网接口测试（4个测试）
1. 创建订单
2. 获取用户订单
3. 获取流量包列表
4. 获取流量包列表（带过滤条件）

#### 内网接口测试（11个测试）
1. 获取批量订单 (Gin HTTP)
2. 获取批量订单 (Dubbo RPC)
3. 强制退款 (Gin HTTP)
4. 创建流量包 (Gin HTTP)
5. 获取所有流量包 (Gin HTTP)
6. 更新流量包 (Gin HTTP)
7. 删除流量包 (Gin HTTP)
8. 创建流量包 (Dubbo RPC)
9. 获取所有流量包 (Dubbo RPC)
10. 更新流量包 (Dubbo RPC)
11. 删除流量包 (Dubbo RPC)

### 技术细节

#### 认证方式
- **外网接口**: 使用Bearer Token认证
- **内网接口**: 使用x-user-id和x-role头部认证

#### 请求格式
- **HTTP接口**: 标准的RESTful API调用
- **RPC接口**: 使用HTTP协议调用Dubbo RPC服务

#### 错误处理
- 所有接口都包含完整的错误处理
- 详细的日志记录到test.log文件
- 控制台显示简要的测试结果

### 使用说明

#### 编译
```bash
cd payment-backend/test/testapi
go build -o testapi.exe ./src
```

#### 运行
```bash
# 测试所有环境
testapi.exe

# 测试特定环境
testapi.exe dev1
testapi.exe dev2
testapi.exe sit
```

#### 查看结果
- 控制台输出：实时显示测试进度和结果汇总
- 日志文件：`test.log` 包含所有详细信息

### 注意事项

1. **测试数据**: 某些测试使用示例数据，实际测试时需要替换为真实数据
2. **依赖关系**: 更新和删除流量包需要先创建流量包获取真实的package_id
3. **环境要求**: 确保能够访问对应环境的内网和外网地址
4. **数据影响**: 测试会创建真实的订单和流量包数据，请在测试环境中运行

### 文件清单

#### 新增文件
- `testForceRefundGin.go` - 强制退款测试
- `testAdminPackagesGin.go` - 流量包管理HTTP接口测试
- `testAdminPackagesRPC.go` - 流量包管理RPC接口测试
- `testListAllPackagesExternal.go` - 外网流量包接口测试
- `CHANGELOG.md` - 本更新日志

#### 修改文件
- `common.go` - 添加新的数据结构
- `test_api.go` - 添加新的测试函数调用
- `README.md` - 更新文档说明

### 总计测试接口数量
- **外网接口**: 4个（全部为HTTP）
- **内网接口**: 11个（6个HTTP + 5个RPC，注意强制退款只有HTTP）
- **总计**: 15个测试接口
