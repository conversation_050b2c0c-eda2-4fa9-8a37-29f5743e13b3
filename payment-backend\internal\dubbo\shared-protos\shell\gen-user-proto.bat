@echo off
setlocal


set PROTO_DIR=../..


set OUT_DIR=gen


if not exist %OUT_DIR% (
    mkdir %OUT_DIR%
)
if not exist %OUT_DIR%\\python (
    mkdir %OUT_DIR%\\python
)
if not exist %OUT_DIR%\\go (
    mkdir %OUT_DIR%\\go
)

if not exist %OUT_DIR%\\go_for_gateway (
    mkdir %OUT_DIR%\\go_for_gateway
)


cd gen/go
echo %PROTO_DIR%/third_party/validate

protoc -I=%PROTO_DIR% ^
       -I=%PROTO_DIR%/third_party/googleapis ^
       -I=%PROTO_DIR%/third_party/validate ^
       --go_out=.  ^
       --go-grpc_out=.  ^
       --go-triple_out=.  ^
       --validate_out=lang=go:. ^
       %PROTO_DIR%\protos\user.proto



cd ../go_for_gateway
protoc -I=%PROTO_DIR% ^
       -I=%PROTO_DIR%/third_party/googleapis ^
       -I=%PROTO_DIR%/third_party/validate ^
       --go_out=.  ^
       --go-grpc_out=.  ^
       --grpc-gateway_out=.  ^
       %PROTO_DIR%\protos\user.proto



cd ../..
set PROTO_DIR=.
python -m grpc_tools.protoc -I%PROTO_DIR% -I%PROTO_DIR%/third_party/googleapis -I=%PROTO_DIR%/third_party/validate --python_out=%OUT_DIR%/python --grpc_python_out=%OUT_DIR%/python %PROTO_DIR%\protos\user.proto

@REM grpc_tools_node_protoc ^
@REM   --js_out=import_style=commonjs,binary:./gen/nodejs ^
@REM   --grpc_out=grpc_js:./gen/nodejs ^
@REM   -I %PROTO_DIR% ^
@REM   %PROTO_DIR%\protos\user.proto



if errorlevel 1 (
    echo %time%  [ERROR] protoc xxxx  failed
    exit /b 1
) else (
    echo %time%  [OK] xxxx %OUT_DIR%
)

endlocal
